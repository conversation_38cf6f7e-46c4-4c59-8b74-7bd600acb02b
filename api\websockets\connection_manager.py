"""
WebSocket Connection Manager
Handles WebSocket connections for prayer generation updates.
"""

from fastapi import WebSocket
from typing import Dict, List, Callable, Any
import json
import asyncio

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, run_id: str):
        """
        Connect a WebSocket client to a specific prayer run
        """
        await websocket.accept()
        if run_id not in self.active_connections:
            self.active_connections[run_id] = []
        self.active_connections[run_id].append(websocket)
        print(f"[INFO] WebSocket client connected to run {run_id}")

    def disconnect(self, websocket: WebSocket, run_id: str):
        """
        Disconnect a WebSocket client from a specific prayer run
        """
        if run_id in self.active_connections:
            if websocket in self.active_connections[run_id]:
                self.active_connections[run_id].remove(websocket)
                print(f"[INFO] WebSocket client disconnected from run {run_id}")
            # Clean up if no more connections for this run
            if not self.active_connections[run_id]:
                del self.active_connections[run_id]
                print(f"[INFO] No more connections for run {run_id}")

    async def broadcast(self, run_id: str, message: Any):
        """
        Broadcast a message to all connected clients for a specific prayer run
        """
        if run_id not in self.active_connections:
            return
            
        # Convert to JSON string if not already
        if not isinstance(message, str):
            message = json.dumps(message)
            
        # Send to all connections for this run
        disconnected_websockets = []
        for websocket in self.active_connections[run_id]:
            try:
                await websocket.send_text(message)
            except Exception as e:
                print(f"[ERROR] Failed to send message to WebSocket: {e}")
                # Mark for removal
                disconnected_websockets.append(websocket)
                
        # Clean up disconnected websockets
        for websocket in disconnected_websockets:
            self.disconnect(websocket, run_id)

# Create a singleton instance
manager = ConnectionManager()
