"""
Prayer Aggregation Nodes
LangGraph nodes for prayer aggregation and unified prayer generation.
"""
from typing import Dict
import os
import datetime

from models.prayer_state import PrayerState
# Use the OpenAI prayer agent instead of Gemini service
from services.openai_prayer_agent import OpenAIPrayerAgent
from utils.file_utils import sanitize_filename, save_unified_prayer
from divine_names import DIVINE_NAMES
# Remove duplicate import
# from utils.file_utils import sanitize_filename, save_unified_prayer

def aggregate_prayers_node(state: PrayerState) -> Dict:
    """
    Aggregate only the prayers from the state that were explicitly selected.

    Args:
        state: Current workflow state

    Returns:
        Updated state with combined prayer text
    """
    print("\nNode: Aggregating individual prayers...")
    individual_prayers = state.get('individual_prayers', {})

    # Get the list of faiths that were originally selected
    original_selected_faiths = list(individual_prayers.keys())
    print(f"   [INFO] Selected faiths that were processed: {', '.join(original_selected_faiths)}")

    # Only include prayers from faiths that had successful generation
    prayer_texts = [text for faith, text in individual_prayers.items() if text]

    if not prayer_texts:
        print("   [WARNING] No individual prayer texts found to aggregate.")
        return {"combined_prayer_text": ""}

    combined_text = "\n\n---\n\n".join(prayer_texts)
    print(f"   [INFO] Successfully aggregated {len(prayer_texts)} prayers.")

    return {"combined_prayer_text": combined_text}

def generate_unified_prayer_node(state: PrayerState) -> Dict:
    """
    Generate a unified prayer using our prayer agent based on aggregated text in state.

    Args:
        state: Current workflow state

    Returns:
        Updated state with unified prayer text and filename
    """
    print("\nNode: Generating Unified Prayer...")
    combined_prayers = state.get('combined_prayer_text')
    prayer_focus_theme = state.get('prayer_focus_theme', 'N/A')

    # Check if we have combined prayers
    if not combined_prayers:
        print("   [WARNING] No combined prayer text available. Cannot generate unified prayer.")
        return {
            "unified_prayer_markdown": "",
            "unified_prayer_filename": "",
            "error_message": "No prayer text available for unification"
        }

    # Aggregate "universal with specific" divine names from all faiths
    faith_research = state.get('faith_research', {})
    multifaith_divine_names = []
    # Build an expansive, beautiful string of divine names from all traditions
    all_divine_names = []
    for concept, entry in DIVINE_NAMES.items():
        if concept == "universal_titles":
            all_divine_names.extend(entry)
        elif isinstance(entry, dict):
            universal = entry.get("universal")
            specifics = entry.get("specific", [])
            if universal:
                all_divine_names.append(universal)
            all_divine_names.extend(specifics)
    # Remove duplicates while preserving order
    seen = set()
    unique_divine_names = []
    for name in all_divine_names:
        if name not in seen:
            unique_divine_names.append(name)
            seen.add(name)
    multifaith_divine_names_str = ", ".join(unique_divine_names)

    # Use the OpenAI prayer agent to generate the unified prayer
    try:
        # Instantiate the prayer agent with the Llama Maverick model
        prayer_agent = OpenAIPrayerAgent(model_name="Llama-4-Maverick-17B-128E-Instruct-FP8")
        print(f"   [INFO] Using OpenAI prayer agent with model: {prayer_agent.model_name}")

        # Generate unified prayer with divine names
        result = prayer_agent.generate_unified_prayer(
            individual_prayers=combined_prayers.split("\n\n---\n\n"),
            prayer_focus=prayer_focus_theme,
            wisdom_focus=prayer_focus_theme,
            multifaith_divine_names=multifaith_divine_names_str  # Pass the divine names
        )

        # Extract results
        unified_prayer_text = result.get("unified_prayer_text", "")
        suggested_filename = result.get("suggested_filename", "")
        error = result.get("error")

        if error:
            print(f"   [ERROR] Failed to generate unified prayer: {error}")
        elif not unified_prayer_text:
            print("   [WARNING] Empty unified prayer text returned.")
            error = "Empty unified prayer text returned"
        else:
            print("   [INFO] Successfully generated unified prayer.")

        # If no valid filename was suggested, create a default one
        if not suggested_filename:
            suggested_filename = f"Unified_Prayer_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

        # Update state with full results
        return {
            "unified_prayer_markdown": unified_prayer_text,
            "unified_prayer_filename": suggested_filename,
            "error_message": error,  # Pass along any error that occurred
            "combined_prayer_text": combined_prayers  # Preserve the combined prayers
        }

    except Exception as e:
        error_msg = f"Error generating unified prayer: {str(e)}"
        print(f"   [ERROR] {error_msg}")
        return {
            "unified_prayer_markdown": "",
            "unified_prayer_filename": f"Error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            "error_message": error_msg,
            "combined_prayer_text": combined_prayers  # Preserve the combined prayers
        }

def save_unified_prayer_node(state: PrayerState) -> Dict:
    """
    Save the generated unified prayer to a Markdown file.

    Args:
        state: Current workflow state

    Returns:
        Updated state with filepath to saved unified prayer
    """
    print("\nNode: Saving Unified Prayer...")
    unified_prayer_text = state.get('unified_prayer_markdown')
    suggested_filename = state.get('unified_prayer_filename')
    prayer_focus_theme = state.get('prayer_focus_theme', 'N/A')
    error = state.get('error_message')  # Check for errors from previous node

    if unified_prayer_text is None or suggested_filename is None:
        # This case shouldn't happen if generate_unified_prayer_node always returns values
        err_msg = "Unified prayer text or filename missing in state."
        print(f"   [ERROR] {err_msg}")
        return {"error_message": error or err_msg}  # Preserve previous error if exists

    if not unified_prayer_text and not error:
        # If text is empty but no error was explicitly reported, log it.
        print("   [WARNING] Unified prayer text is empty, saving an empty file.")

    # Sanitize and finalize filename
    if not suggested_filename.lower().endswith(".md"):
        suggested_filename += ".md"
    final_filename = sanitize_filename(suggested_filename)

    # Save the unified prayer to the main Saved_Prayers directory
    output_dir = "Saved_Prayers"  # Save unified prayer here
    filepath = os.path.join(output_dir, final_filename)

    # Save using utility function
    success = save_unified_prayer(
        filepath=filepath,
        prayer_text=unified_prayer_text,
        prayer_focus_theme=prayer_focus_theme,
        model_name="Llama-4-Maverick-17B-128E-Instruct-FP8"  # Updated to the Llama Maverick model
    )

    save_error = None if success else f"Failed to save unified prayer file to {filepath}"

    # Return existing error OR the save error, and the filepath if successful
    return {
        "error_message": error or save_error,
        "unified_prayer_filepath": filepath if success else None
    }

def should_request_audio(state: PrayerState) -> str:
    """
    Determine if audio conversion should be requested based on state.

    Args:
        state: Current workflow state

    Returns:
        Next workflow edge to follow
    """
    # Check if we have an error or no unified prayer
    if state.get('error_message') or not state.get('unified_prayer_filepath'):
        print("\nEdge: No valid unified prayer to convert, skipping audio conversion.")
        return "no_audio"

    # Otherwise, proceed to audio conversion request
    print("\nEdge: Unified prayer available, proceeding to audio conversion request.")
    return "request_audio"
