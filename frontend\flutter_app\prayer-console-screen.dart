import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';
import '../models/prayer_state.dart';
import '../providers/prayer_provider.dart';
import '../widgets/prayer_console_widget.dart';

class PrayerConsoleScreen extends ConsumerStatefulWidget {
  final String runId;
  
  const PrayerConsoleScreen({
    Key? key,
    required this.runId,
  }) : super(key: key);

  @override
  ConsumerState<PrayerConsoleScreen> createState() => _PrayerConsoleScreenState();
}

class _PrayerConsoleScreenState extends ConsumerState<PrayerConsoleScreen> {
  bool _isInitialized = false;
  
  @override
  void initState() {
    super.initState();
    // Load prayer data with the provided run ID
    _loadPrayer();
  }
  
  // Load prayer data
  Future<void> _loadPrayer() async {
    try {
      await ref.read(prayerStateProvider.notifier).loadPrayer(widget.runId);
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading prayer: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final prayerStateAsync = ref.watch(prayerStateProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Prayer Console'),
        actions: [
          // Cancel button
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () {
              _showCancelDialog(context);
            },
          ),
        ],
      ),
      body: prayerStateAsync.when(
        data: (prayerState) {
          if (!_isInitialized || prayerState == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          // Check if prayer is complete
          if (prayerState.isCompleted) {
            // Automatically navigate to results screen
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.replace('${AppConstants.prayerResultsRoute}/${widget.runId}');
            });
            
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Prayer completed! Redirecting to results...'),
                ],
              ),
            );
          }
          
          // Check for errors
          if (prayerState.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'An error occurred',
                    style: theme.textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      prayerState.errorMessage ?? 'Unknown error',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.pop();
                    },
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            );
          }
          
          // Show prayer console with progress information
          return PrayerConsoleWidget(prayerState: prayerState);
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.pop();
                },
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Show cancel confirmation dialog
  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Prayer Generation?'),
        content: const Text(
          'Are you sure you want to cancel the prayer generation process? Any progress will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('No, Continue'),
          ),
          ElevatedButton(
            onPressed: () {
              // Cancel prayer generation
              ref.read(prayerStateProvider.notifier).cancelPrayerGeneration();
              // Pop dialog and go back
              Navigator.of(context).pop();
              context.pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }
}
