"""
Async Prayer Application Graph Definition
Defines the enhanced LangGraph workflow with async video generation and parallel media processing.
"""

from langgraph.graph import StateGraph, END
from langgraph.constants import Send
from models.prayer_state import PrayerState
from nodes.research_nodes import perform_research_node
from nodes.prayer_generation import (
    prepare_next_movement_node,
    generate_single_prayer_node,
    save_individual_prayer_node,
    should_continue_generating
)
from nodes.prayer_aggregation import (
    aggregate_prayers_node,
    generate_unified_prayer_node,
    save_unified_prayer_node,
)
from nodes.tts_nodes import (
    ask_audio_conversion_node,
    prepare_tts_text_node,
    generate_audio_node,
    audio_requested
)
from nodes.async_video_nodes import (
    parallel_media_generation_node,
    should_run_parallel_media_generation,
    media_aggregation_node,
    async_video_generation_node,
    async_image_generation_task,
    async_audio_generation_task
)
from nodes.scene_generation_nodes import generate_sacred_scenes_node # Import from new file

# Import the image prayer nodes with fallback
try:
    from nodes.updated_image_prayer_node import (
        generate_image_prayer_node,
        should_generate_image_prayer
    )
    print("[INFO] Using updated image prayer nodes with async support.")
except ImportError:
    try:
        from nodes.image_prayer_node import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[INFO] Using robust image prayer nodes.")
    except ImportError:
        from nodes.gemini_image_prayer import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[WARNING] Using original image prayer nodes.")

def create_async_prayer_workflow() -> StateGraph:
    """
    Create the enhanced LangGraph workflow with TRUE parallel video generation.
    Video generation starts immediately after unified prayer is saved, running
    concurrently with any remaining pipeline steps.

    Returns:
        Compiled LangGraph workflow with async capabilities
    """
    # Initialize the graph with our enhanced state model
    workflow = StateGraph(PrayerState)

    # Add research node
    workflow.add_node("perform_research", perform_research_node)

    # Add individual prayer generation nodes
    workflow.add_node("prepare_movement", prepare_next_movement_node)
    workflow.add_node("generate_prayer", generate_single_prayer_node)
    workflow.add_node("save_prayer", save_individual_prayer_node)

    # Add unified prayer generation nodes
    workflow.add_node("aggregate_prayers", aggregate_prayers_node)
    workflow.add_node("generate_unified", generate_unified_prayer_node)
    workflow.add_node("save_unified", save_unified_prayer_node)

    # Add scene generation node (now imported)
    workflow.add_node("generate_scenes", generate_sacred_scenes_node)

    # Add TRUE parallel media generation nodes
    workflow.add_node("start_parallel_media", start_parallel_media_node)
    workflow.add_node("video_generation", async_video_generation_node)
    workflow.add_node("image_generation", async_image_generation_task)
    workflow.add_node("audio_generation", async_audio_generation_task)
    workflow.add_node("media_aggregation", media_aggregation_node)

    # Add fallback nodes for individual media types (if parallel fails)
    workflow.add_node("fallback_tts", ask_audio_conversion_node)
    workflow.add_node("fallback_audio", generate_audio_node)
    workflow.add_node("fallback_image", generate_image_prayer_node)

    # Set the entry point - start with research
    workflow.set_entry_point("perform_research")

    # Connect research to prayer generation
    workflow.add_edge("perform_research", "prepare_movement")

    # Define the individual prayer generation cycle
    workflow.add_edge("prepare_movement", "generate_prayer")
    workflow.add_edge("generate_prayer", "save_prayer")

    # Conditional edge after saving individual prayer
    workflow.add_conditional_edges(
        "save_prayer",
        should_continue_generating,
        {
            "continue_generation": "prepare_movement",  # Loop back if more movements
            "aggregate": "aggregate_prayers",           # Go to aggregate when done looping
        }
    )

    # Unified prayer generation flow
    workflow.add_edge("aggregate_prayers", "generate_unified")
    workflow.add_edge("generate_unified", "save_unified")

    # Scene generation for media
    workflow.add_edge("save_unified", "generate_scenes")

    # TRUE PARALLEL EXECUTION: Start all media generation simultaneously
    workflow.add_edge("generate_scenes", "start_parallel_media")

    # From start_parallel_media, launch all media types in parallel using Send API
    workflow.add_conditional_edges(
        "start_parallel_media",
        route_to_parallel_media_with_send,
        # No mapping needed - the function returns Send objects directly
    )

    # All media generation nodes converge to aggregation
    workflow.add_edge("video_generation", "media_aggregation")
    workflow.add_edge("image_generation", "media_aggregation")
    workflow.add_edge("audio_generation", "media_aggregation")

    # Fallback processing after aggregation
    workflow.add_conditional_edges(
        "media_aggregation",
        should_run_fallback_processing,
        {
            "run_tts_fallback": "fallback_tts",
            "run_image_fallback": "fallback_image",
            "complete": END
        }
    )

    # Fallback TTS flow
    workflow.add_conditional_edges(
        "fallback_tts",
        audio_requested,
        {
            "yes": "fallback_audio",
            "no": "fallback_image"
        }
    )
    workflow.add_edge("fallback_audio", "fallback_image")

    # Fallback image generation
    workflow.add_conditional_edges(
        "fallback_image",
        should_generate_image_prayer,
        {
            "generate_image": END,
            "skip_image": END
        }
    )

    print("[INFO] Using TRUE parallel async LangGraph workflow with concurrent media processing")

    # Compile the graph
    return workflow.compile()

# Removed generate_sacred_scenes_node as it's now in nodes/scene_generation_nodes.py

def should_run_fallback_processing(state: PrayerState) -> str:
    """
    Determine if fallback processing is needed for failed media generation.
    This function now checks for specific media types using dict access.
    """
    # Access state as dict for consistency
    if isinstance(state, dict):
        # Check if video generation was requested and failed
        video_requested = state.get('generate_video', False)
        video_generated = state.get('generated_video') is not None

        # Check if image generation was requested and failed
        image_requested = state.get('image_prayer_requested', False)
        image_generated = state.get('image_prayer_generated', False) or (
            state.get('generated_images') is not None and len(state.get('generated_images', [])) > 0
        )

        # Check if audio generation was requested and failed
        audio_requested_flag = state.get('audio_conversion_requested', False)
        audio_generated = (state.get('audio_filepath') is not None or
                          state.get('generated_audio') is not None)
    else:
        # Fallback to getattr for TypedDict objects
        video_requested = getattr(state, 'generate_video', False)
        video_generated = getattr(state, 'generated_video', None) is not None

        image_requested = getattr(state, 'image_prayer_requested', False)
        image_generated = (getattr(state, 'image_prayer_generated', False) or
                          (getattr(state, 'generated_images', []) is not None and
                           len(getattr(state, 'generated_images', [])) > 0))

        audio_requested_flag = getattr(state, 'audio_conversion_requested', False)
        audio_generated = (getattr(state, 'audio_filepath', None) is not None or
                          getattr(state, 'generated_audio', None) is not None)

    # Prioritize audio fallback, then image
    # No video fallback node exists, so we skip video fallback
    if audio_requested_flag and not audio_generated:
        return "run_tts_fallback"

    if image_requested and not image_generated:
        return "run_image_fallback"

    return "complete"

def start_parallel_media_node(state: PrayerState) -> PrayerState:
    """
    Initialize parallel media generation by setting up the state.
    This node prepares the state for concurrent media processing.
    """
    print("[INFO] Starting parallel media generation setup...")

    # Ensure all required fields are initialized
    if isinstance(state, dict):
        state.setdefault('execution_logs', []).append("Parallel media generation initiated")
        state.setdefault('async_tasks_status', {})
        state['parallel_processing_enabled'] = True
    else:
        # Handle TypedDict case
        if not hasattr(state, 'execution_logs'):
            state['execution_logs'] = []
        if not hasattr(state, 'async_tasks_status'):
            state['async_tasks_status'] = {}
        state['execution_logs'].append("Parallel media generation initiated")
        state['parallel_processing_enabled'] = True

    print("[INFO] Parallel media generation setup complete")
    return state

def route_to_parallel_media(state: PrayerState) -> str:
    """
    Route to appropriate parallel media generation based on user requests.
    Returns the routing decision for which media types to generate.
    """
    # Access state as dict for consistency
    if isinstance(state, dict):
        video_requested = state.get('generate_video', False)
        image_requested = state.get('image_prayer_requested', False)
        audio_requested = state.get('audio_conversion_requested', False)
    else:
        video_requested = getattr(state, 'generate_video', False)
        image_requested = getattr(state, 'image_prayer_requested', False)
        audio_requested = getattr(state, 'audio_conversion_requested', False)

    print(f"[ROUTING] Video: {video_requested}, Image: {image_requested}, Audio: {audio_requested}")

    # Determine routing based on requested media types
    if video_requested and image_requested and audio_requested:
        return "all_media"
    elif video_requested and image_requested:
        return "video_image"
    elif video_requested and audio_requested:
        return "video_audio"
    elif image_requested and audio_requested:
        return "image_audio"
    elif video_requested:
        return "video_only"
    elif image_requested:
        return "image_only"
    elif audio_requested:
        return "audio_only"
    else:
        return "no_media"

def route_to_parallel_media_with_send(state: PrayerState):
    """
    Route to parallel media generation using Send API for true parallelism.
    Returns a list of Send objects to execute nodes in parallel.
    """
    # Access state as dict for consistency
    if isinstance(state, dict):
        video_requested = state.get('generate_video', False)
        image_requested = state.get('image_prayer_requested', False)
        audio_requested = state.get('audio_conversion_requested', False)
    else:
        video_requested = getattr(state, 'generate_video', False)
        image_requested = getattr(state, 'image_prayer_requested', False)
        audio_requested = getattr(state, 'audio_conversion_requested', False)

    print(f"[SEND ROUTING] Video: {video_requested}, Image: {image_requested}, Audio: {audio_requested}")

    # Create Send objects for requested media types
    sends = []

    if video_requested:
        sends.append(Send("video_generation", state))
        print("[SEND] Adding video generation to parallel execution")

    if image_requested:
        sends.append(Send("image_generation", state))
        print("[SEND] Adding image generation to parallel execution")

    if audio_requested:
        sends.append(Send("audio_generation", state))
        print("[SEND] Adding audio generation to parallel execution")

    # If no media requested, go directly to aggregation
    if not sends:
        sends.append(Send("media_aggregation", state))
        print("[SEND] No media requested, going to aggregation")

    print(f"[SEND] Executing {len(sends)} parallel tasks")
    return sends

def create_hybrid_prayer_workflow() -> StateGraph:
    """
    Create a hybrid workflow that can fallback to the original sequential processing
    if async processing fails.

    Returns:
        Compiled hybrid LangGraph workflow
    """
    # This would be a more conservative version that tries async first,
    # but falls back to the original workflow if needed
    workflow = StateGraph(PrayerState)

    # Add all nodes from both workflows
    # ... (implementation would include both async and original nodes)

    # Add decision points to choose between async and sequential
    # ... (based on system capabilities and user preferences)

    return workflow.compile()

# Utility function to choose the appropriate workflow
def create_prayer_workflow(enable_async: bool = True) -> StateGraph:
    """
    Factory function to create the appropriate prayer workflow.

    Args:
        enable_async: Whether to use the async workflow with video generation

    Returns:
        Compiled LangGraph workflow
    """
    if enable_async:
        try:
            return create_async_prayer_workflow()
        except ImportError as e:
            print(f"[WARNING] Async workflow unavailable: {e}")
            print("[INFO] Falling back to original workflow")
            from graph_definition_new import create_prayer_workflow as create_original
            return create_original()
        except Exception as e:
            print(f"[ERROR] Failed to create async workflow: {e}")
            print("[INFO] Falling back to original workflow")
            from graph_definition_new import create_prayer_workflow as create_original
            return create_original()
    else:
        from graph_definition_new import create_prayer_workflow as create_original
        return create_original()

if __name__ == "__main__":
    # Test the workflow creation
    try:
        workflow = create_async_prayer_workflow()
        print("✅ Async prayer workflow created successfully")
    except Exception as e:
        print(f"❌ Error creating async workflow: {e}")
