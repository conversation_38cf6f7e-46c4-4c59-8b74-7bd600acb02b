"""
Google Gemini API Service
Handles interactions with Google's Gemini API for unified prayer generation and TTS optimization.
"""

import os
import datetime
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure Google AI Client
google_api_key = os.environ.get("GOOGLE_API_KEY")
if google_api_key:
    genai.configure(api_key=google_api_key)
else:
    print("[ERROR] GOOGLE_API_KEY not found in environment variables. Gemini services will fail.")

# Model names
GEMINI_MODEL_NAME = 'gemini-2.5-flash-preview-04-17'  # Updated to the flash model
GEMINI_FLASH_MODEL = 'gemini-2.5-flash-preview-04-17'  # Updated to the new model name

def generate_unified_prayer(prayer_focus_theme, combined_prayers, multifaith_divine_names=None):
    """
    Generate a unified prayer using Gemini based on a collection of individual prayers, integrating expansive wisdom and multifaith divine names.

    Args:
        prayer_focus_theme: The theme of the prayer
        combined_prayers: Combined text of all individual prayers
        multifaith_divine_names: (Optional) String of divine names from various faiths to be honored and woven into the prayer

    Returns:
        Tuple of (prayer_text, suggested_filename, error_message)
    """
    print("\nGenerating Unified Prayer via Gemini...")

    if not google_api_key:
        error_msg = "Cannot generate unified prayer: Google API Key not configured."
        print(f"   [ERROR] {error_msg}")
        return None, None, error_msg

    if not combined_prayers:
        print("   [WARNING] No combined prayer text provided. Skipping Gemini call.")
        return "", "", None

    # Construct the prompt for Gemini, integrating divine names, quantum prayer concepts, and expansive wisdom
    prompt = (
        f"System Task: You are an advanced spiritual prayer writer with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When humans invoke divine names and sacred language, they engage in quantum-level interactions with reality.\n\n"
        f"Analyze the following collection of prayers, all centered around the theme '{prayer_focus_theme}'. "
        f"Synthesize their core messages, sentiments, and key phrases into a single, powerful, comprehensive, and universally resonant prayer. "
        f"This unified prayer should respectfully draw from the essence of all the provided texts, and weave in the wisdom and divine names from the world's spiritual traditions.\n\n"
        f"Divine Names to Integrate (select appropriate ones for this prayer):\n"
        f"- Creator Names: YHWH/Yahweh (Judaism), Elohim (Judaism), Allah (Islam), Brahman (Hinduism), Ahura Mazda (Zoroastrianism), Great Spirit/Wakan Tanka (Native American), Waheguru (Sikhism)\n"
        f"- Divine Healing Names: Jehovah Rapha (Judaism/Christianity), Ash-Shafi (Islam), Dhanvantari (Hinduism), Bhaisajyaguru (Buddhism), Green Tara (Buddhism)\n"
        f"- Divine Compassion Names: Ar-Rahman (Islam), El Rachum (Judaism), Avalokiteshvara/Guanyin (Buddhism), Karuna (Hinduism)\n"
        f"- Divine Protection Names: Jehovah Nissi (Judaism/Christianity), Al-Hafiz (Islam), Durga (Hinduism), Mahakala (Buddhism)\n"
        f"- Divine Peace Names: Jehovah Shalom (Judaism/Christianity), As-Salam (Islam), Shanti (Hinduism), Buddha (Buddhism)\n"
        f"- Divine Wisdom Names: Hakadosh (Judaism), Al-Hakim (Islam), Saraswati (Hinduism), Manjushri (Buddhism), Sophia (Esoteric Christianity)\n"
        f"- Additional Divine Names: {multifaith_divine_names if multifaith_divine_names else '[No additional divine names provided]'}\n\n"
        f"Your unified prayer should honor and invoke these divine names, blending them harmoniously to create a sense of global unity, reverence, and expansive wisdom. Approach the prayer with the understanding that it operates through quantum principles:\n\n"
        f"1. Prayer operates through quantum entanglement between consciousness and target\n"
        f"2. Divine names carry specific vibrational frequencies that amplify intentional effects\n"
        f"3. Declarations connect to fundamental creative consciousness\n"
        f"4. Collective prayer creates coherent fields with magnified effects\n"
        f"5. Prayer engages actual physics mechanisms at quantum levels\n\n"
        f"After composing the prayer, suggest a concise and descriptive filename for it (e.g., 'Unified_Prayer_for_Peace.md' or 'Prayer_of_Global_Harmony.md'). "
        f"Format your response clearly: first the suggested filename on its own line, followed by a blank line, then the full text of the unified prayer.\n\n"
        f"Example Response Format:\nSuggested_Filename.md\n\n[Full text of the unified prayer here...]\n\n"
        f"--- Combined Prayer Texts ---\n{combined_prayers}\n\n"
        f"--- End Combined Prayer Texts ---"
        f"\n\nSystem Reminder: Provide the filename first, then a blank line, then the prayer."
    )

    try:
        model = genai.GenerativeModel(GEMINI_MODEL_NAME)
        system_instruction = "You are an advanced AI with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When guiding humans in prayer, you integrate quantum principles with spiritual wisdom, understanding that divine names carry specific vibrational frequencies that amplify intentional effects. You help formulate prayers as precise quantum instructions to reality, using divine names and declaration patterns that maximize quantum resonance."
        full_prompt = f"System Instruction: {system_instruction}\n\nUser Request:\n{prompt}"

        response = model.generate_content(full_prompt)
        response_text = response.text.strip()

        # Parse the response
        parts = response_text.split('\n', 2)  # Split into max 3 parts: filename, blank line, prayer

        if len(parts) >= 3:
            suggested_filename = parts[0].strip()
            # Check if the second part is roughly blank
            if parts[1].strip() == "":
                unified_prayer_text = parts[2].strip()
            else:  # Handle cases where the blank line might be missing
                print("   [WARNING] Gemini response format unexpected (missing blank line?). Attempting to parse.")
                unified_prayer_text = "\n".join(parts[1:]).strip()
                # Basic check if the first line looks like a filename
                if not (suggested_filename.lower().endswith(".md") or '.' in suggested_filename):
                    print("   [WARNING] First line doesn't look like a filename, assuming it's part of the prayer.")
                    suggested_filename = f"Unified_Prayer_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
                    unified_prayer_text = response_text  # Use the whole response as prayer

        elif len(parts) == 1:  # Only prayer text returned?
            print("   [WARNING] Gemini response did not suggest a filename. Using default.")
            suggested_filename = f"Unified_Prayer_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            unified_prayer_text = response_text
        else:  # Unparseable format
            error_msg = "Could not parse filename and prayer from Gemini response."
            print(f"   [ERROR] {error_msg}")
            unified_prayer_text = ""  # Set empty prayer text on parse error
            suggested_filename = f"Unified_Prayer_Parse_Error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            return unified_prayer_text, suggested_filename, error_msg

        if not unified_prayer_text:
            # This might happen if Gemini returns empty or parsing fails badly
            error_msg = "Gemini generated an empty unified prayer or parsing failed."
            print(f"   [ERROR] {error_msg}")
            # Ensure filename is set even on error
            if not suggested_filename:
                suggested_filename = f"Unified_Prayer_Empty_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            return "", suggested_filename, error_msg

        print(f"   [INFO] Successfully generated unified prayer with Gemini")
        return unified_prayer_text, suggested_filename, None

    except Exception as e:
        error_msg = f"Error during Gemini API call or processing: {e}"
        print(f"   [ERROR] {error_msg}")
        unified_prayer_text = ""  # Ensure empty on error
        suggested_filename = f"Unified_Prayer_API_Error_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        return unified_prayer_text, suggested_filename, error_msg

def generate_verbal_text(prayer_text, model="gemini"):
    """
    Generates TTS-optimized text with emotional cues using selected AI model.

    Args:
        prayer_text: Original prayer text to optimize
        model: AI model to use ('gemini' or 'openai')

    Returns:
        TTS-optimized text with stage directions
    """
    print(f"\nGenerating Verbal Prayer Text using {model} model...")

    if not prayer_text:
        print("   [WARNING] No prayer text provided to convert.")
        return "[ERROR] No prayer text provided for conversion."

    # Construct the prompt for TTS optimization
    prompt = (
        f"System Task: Rewrite the following prayer text to be optimized for Text-to-Speech (TTS) generation. "
        f"Enhance the text with emotional cues and pacing instructions suitable for verbal delivery. "
        f"Use simple, clear annotations like (pause), (short pause), (long pause), (whisper), (emphasis), "
        f"(gentle tone), (reverent tone), (louder), (softer), etc. "
        f"Ensure the core meaning and spiritual reverence of the prayer are preserved while making it flow naturally when read aloud.\n\n"
        f"--- Original Prayer Text ---\n{prayer_text}\n\n"
        f"--- End Original Prayer Text ---\n\n"
        f"System Reminder: Provide only the rewritten, TTS-optimized prayer text as your response."
    )

    try:
        if model.lower() == "gemini":
            # Use Google's Gemini model
            if not google_api_key:
                error = "Cannot generate verbal prayer text: Google API Key not configured."
                print(f"   [ERROR] {error}")
                return f"[ERROR] {error}"

            print("   [INFO] Calling Gemini with TTS optimization prompt...")
            # Use the flash thinking model for faster results
            gemini_model = genai.GenerativeModel(GEMINI_FLASH_MODEL)
            response = gemini_model.generate_content(prompt)
            verbal_text = response.text.strip()

        elif model.lower() == "openai":
            # OpenAI handling is moved to openai_service.py
            print("   [ERROR] OpenAI model requested but should be handled by openai_service.py")
            return f"[ERROR] OpenAI model handling is external to this module"

        else:
            return f"[ERROR] Unknown model specified: {model}. Use 'gemini' or 'openai'."

        print(f"   [INFO] Successfully generated TTS-optimized text using {model}.")
        return verbal_text

    except Exception as e:
        error_msg = f"Error generating verbal prayer text with {model}: {str(e)}"
        print(f"   [ERROR] {error_msg}")
        return f"[ERROR] {error_msg}"
