# Environment files containing API keys
.env
.env.local
.env.production
.env.staging
*.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
%TEMP%/venv_new/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Generated content (prayers, audio, images)
Saved_Prayers/
*.mp3
*.wav
*.png
*.jpg
*.jpeg
*.gif
*.md
!README.md
!**/README.md

# Temporary files
*.tmp
*.temp
*.log
*.bak
*.backup
*.20*

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Cloud deployment
.gcloudignore
.terraform/
*.tfstate
*.tfstate.*

# Model files (too large for git)
*.pkl
*.joblib
*.h5
*.pb

# Local development
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.dmypy.json
dmypy.json

# Jupyter Notebooks
.ipynb_checkpoints
*.ipynb

# Database files
*.db
*.sqlite
*.sqlite3

# Configuration override files
*local.json
*local.yaml
*local.yml
