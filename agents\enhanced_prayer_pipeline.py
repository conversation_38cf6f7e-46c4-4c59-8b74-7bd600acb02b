import asyncio
import logging
from .pydantic_ai_state import PrayerPipelineState
from .gemini_orchestrator import GeminiOrchestrator
from .scene_writing_agent import SceneWritingAgent
from .gemini_audio_agent import GeminiAudioAgent
from .async_mcp_connector import Async<PERSON><PERSON>ana<PERSON>, VideoGenerationService, setup_prayer_mcp_servers

logger = logging.getLogger(__name__)

class EnhancedPrayerPipeline:
    def __init__(self, config: dict):
        """
        Initializes the Enhanced Prayer Pipeline with real async MCP support.

        Args:
            config: A dictionary containing configuration, e.g., API keys.
                    Expected keys: 'gemini_api_key'.
        """
        gemini_api_key = config.get('gemini_api_key')
        if not gemini_api_key:
            raise ValueError("gemini_api_key not found in configuration.")

        self.orchestrator = GeminiOrchestrator(gemini_api_key)
        
        # Initialize specialized agents
        self.scene_writer_pro = SceneWritingAgent("pro")
        self.scene_writer_flash = SceneWritingAgent("flash")
        self.audio_agent = GeminiAudioAgent(gemini_api_key)
        
        # Initialize async MCP manager
        self.mcp_manager = None
        self.video_service = None
        
        logger.info("EnhancedPrayerPipeline initialized with async MCP support")

    async def _setup_mcp_connections(self):
        """Setup MCP server connections if not already done"""
        if not self.mcp_manager:
            logger.info("Setting up MCP server connections...")
            self.mcp_manager = await setup_prayer_mcp_servers()
            
            if self.mcp_manager.is_server_connected("replicate-mcp"):
                self.video_service = VideoGenerationService(self.mcp_manager)
                logger.info("Video generation service initialized")
            else:
                logger.warning("Replicate MCP server not connected - video generation unavailable")

    def _convert_input_to_state(self, user_input: dict) -> PrayerPipelineState:
        """Converts raw user input to the PrayerPipelineState."""
        # Basic validation for required fields
        required_fields = ['pray_for', 'wisdom_to_integrate', 'selected_faiths']
        for field in required_fields:
            if field not in user_input:
                raise ValueError(f"Missing required field in user_input: {field}")
        
        # Create initial state with async support
        state = PrayerPipelineState(
            prayer_focus=user_input['pray_for'],
            wisdom_theme=user_input['wisdom_to_integrate'],
            selected_faiths=user_input['selected_faiths'],
            # Add async video fields
            generate_video=user_input.get('generate_video', False),
            generate_images=user_input.get('generate_images', False),
            parallel_processing_enabled=True,
            execution_logs=[],
            sacred_scenes=[],
            generated_videos=[],
            generated_images=[],
            async_tasks_status={}
        )
        
        return state

    def _format_results(self, final_state: PrayerPipelineState) -> dict:
        """Formats the final state into a dictionary for the user/caller."""
        results = {
            "prayer_focus": final_state.prayer_focus,
            "wisdom_theme": final_state.wisdom_theme,
            "selected_faiths": final_state.selected_faiths,
            "research_summary": final_state.faith_research,
            "sacred_scenes_generated": final_state.sacred_scenes,
            "image_prompts_used": final_state.scene_prompts,
            "generated_image_urls": final_state.generated_images,
            "generated_audio_url": final_state.generated_audio,
            "generated_video_url": final_state.generated_video,
            "pipeline_logs": final_state.execution_logs
        }
        
        # Add async processing results
        if hasattr(final_state, 'generated_videos') and final_state.generated_videos:
            results["all_generated_videos"] = final_state.generated_videos
            
        if hasattr(final_state, 'media_summary') and final_state.media_summary:
            results["media_summary"] = final_state.media_summary
            
        if hasattr(final_state, 'video_generation_errors') and final_state.video_generation_errors:
            results["video_generation_errors"] = final_state.video_generation_errors
            
        return results

    async def _run_parallel_media_generation(self, state: PrayerPipelineState) -> PrayerPipelineState:
        """Run media generation tasks in parallel"""
        logger.info("Starting parallel media generation")
        
        tasks = []
        task_names = []
        
        # Video generation task
        if state.generate_video and self.video_service:
            tasks.append(self._generate_videos_async(state))
            task_names.append("video_generation")
            
        # Image generation task (placeholder for now)
        if state.generate_images:
            tasks.append(self._generate_images_async(state))
            task_names.append("image_generation")
            
        # Audio generation task
        if getattr(state, 'audio_conversion_requested', False):
            tasks.append(self._generate_audio_async(state))
            task_names.append("audio_generation")
        
        if not tasks:
            logger.info("No media generation tasks to run")
            return state
            
        state.execution_logs.append(f"Running {len(tasks)} media tasks in parallel: {', '.join(task_names)}")
        
        # Execute all tasks in parallel
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results and merge back into state
            for i, result in enumerate(results):
                task_name = task_names[i]
                
                if isinstance(result, Exception):
                    logger.error(f"Task {task_name} failed: {result}")
                    state.execution_logs.append(f"Media task '{task_name}' failed: {str(result)}")
                else:
                    logger.info(f"Task {task_name} completed successfully")
                    # Merge successful results
                    if hasattr(result, 'generated_videos'):
                        state.generated_videos.extend(result.generated_videos or [])
                    if hasattr(result, 'generated_images'):
                        state.generated_images.extend(result.generated_images or [])
                    if hasattr(result, 'generated_audio'):
                        state.generated_audio = result.generated_audio
                        
        except Exception as e:
            logger.error(f"Error in parallel media generation: {e}")
            state.execution_logs.append(f"Parallel media generation error: {str(e)}")
            
        return state

    async def _generate_videos_async(self, state: PrayerPipelineState) -> PrayerPipelineState:
        """Generate videos for sacred scenes"""
        if not self.video_service or not state.sacred_scenes:
            return state
            
        try:
            logger.info(f"Generating videos for {len(state.sacred_scenes)} scenes")
            
            video_results = await self.video_service.generate_prayer_video_sequence(
                state.sacred_scenes,
                model="meta/veo-2"
            )
            
            state.generated_videos = video_results
            
            # Set primary video URL
            successful_videos = [v for v in video_results if "error" not in v]
            if successful_videos:
                state.generated_video = successful_videos[0].get("video_url")
                
            logger.info(f"Video generation completed: {len(successful_videos)} successful")
            
        except Exception as e:
            logger.error(f"Error in video generation: {e}")
            state.video_generation_error = str(e)
            
        return state

    async def _generate_images_async(self, state: PrayerPipelineState) -> PrayerPipelineState:
        """Generate images async (placeholder implementation)"""
        logger.info("Image generation async placeholder")
        # This would integrate with fal-ai MCP server
        return state

    async def _generate_audio_async(self, state: PrayerPipelineState) -> PrayerPipelineState:
        """Generate audio async (placeholder implementation)"""
        logger.info("Audio generation async placeholder")
        # This would use the existing audio generation but wrapped in async
        return state

    async def run(self, user_input: dict) -> dict:
        """
        Executes the full prayer generation pipeline with async video support.

        Args:
            user_input: A dictionary containing user preferences, e.g.,
                        {'pray_for': '...', 'wisdom_to_integrate': '...', 'selected_faiths': [...],
                         'generate_video': True, 'generate_images': True}

        Returns:
            A dictionary containing the results of the prayer generation.
        """
        logger.info(f"EnhancedPrayerPipeline run started with input: {user_input}")
        
        try:
            # Setup MCP connections
            await self._setup_mcp_connections()
            
            # Convert input to state
            initial_state = self._convert_input_to_state(user_input)
            initial_state.mcp_manager = self.mcp_manager
            
            # Execute the orchestrator's pipeline (text generation, research, scenes)
            intermediate_state = await self.orchestrator.execute_pipeline(initial_state)
            
            # Run parallel media generation if requested
            if (intermediate_state.generate_video or 
                intermediate_state.generate_images or 
                getattr(intermediate_state, 'audio_conversion_requested', False)):
                
                final_state = await self._run_parallel_media_generation(intermediate_state)
            else:
                final_state = intermediate_state
            
            # Format and return results
            formatted_results = self._format_results(final_state)
            logger.info("EnhancedPrayerPipeline run completed successfully")
            return formatted_results
            
        except ValueError as ve:
            logger.error(f"ValueError in pipeline: {ve}")
            return {"error": str(ve), "pipeline_logs": [str(ve)]}
        except Exception as e:
            logger.error(f"Unexpected error in pipeline: {e}")
            import traceback
            tb_str = traceback.format_exc()
            return {"error": f"An unexpected error occurred: {e}", "pipeline_logs": [str(e), tb_str]}
        finally:
            # Cleanup MCP connections
            if self.mcp_manager:
                await self.mcp_manager.shutdown_all()

    async def cleanup(self):
        """Cleanup resources"""
        if self.mcp_manager:
            await self.mcp_manager.shutdown_all()
            logger.info("MCP connections cleaned up")

# Example Usage (for testing purposes)
async def main():
    import os
    
    config = {
        "gemini_api_key": os.environ.get("GOOGLE_API_KEY", "YOUR_GOOGLE_API_KEY_FALLBACK")
    }
    
    if config["gemini_api_key"] == "YOUR_GOOGLE_API_KEY_FALLBACK":
        print("Warning: Using fallback API key for Gemini. Please set GOOGLE_API_KEY.")

    pipeline = EnhancedPrayerPipeline(config=config)
    
    try:
        user_preferences = {
            "pray_for": "Global Unity and Understanding",
            "wisdom_to_integrate": "The interconnectedness of all life",
            "selected_faiths": ["Universal Spirituality", "Indigenous Traditions"],
            "generate_video": True,
            "generate_images": True
        }
        
        results = await pipeline.run(user_preferences)
        
        print("\n--- Enhanced Pipeline Results ---")
        if "error" in results:
            print(f"Error: {results['error']}")
        else:
            for key, value in results.items():
                if key == "pipeline_logs":
                    print(f"\n{key.replace('_', ' ').title()}:")
                    for log_entry in value:
                        print(f"  - {log_entry}")
                else:
                    print(f"{key.replace('_', ' ').title()}: {value}")
    finally:
        await pipeline.cleanup()

if __name__ == "__main__":
    # asyncio.run(main()) # Commented out as this is library code.
    pass
