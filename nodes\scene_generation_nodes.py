import logging
import asyncio
from typing import Dict, List, Optional
from models.prayer_state import PrayerState
from nodes.enhanced_video_scene_generator import EnhancedVideoSceneGenerator

logger = logging.getLogger(__name__)

def generate_sacred_scenes_node(state: PrayerState) -> PrayerState:
    """
    Generate enhanced sacred scenes with beautiful spiritual imagery using advanced LLM prompting.
    Creates scenes with symbols, mandalas, angels, healing themes, sacred geometry, and fractals.

    Args:
        state: Current workflow state, expected to contain 'unified_prayer_markdown'.

    Returns:
        Updated state with 'sacred_scenes' populated with enhanced spiritual scenes.
    """
    logger.info("Node: Generating enhanced sacred scenes with advanced LLM prompting...")

    try:
        # Get prayer data from state
        if isinstance(state, dict):
            unified_prayer_text = state.get('unified_prayer_markdown', '')
            prayer_theme = state.get('prayer_focus_theme', 'Spiritual Healing and Divine Connection')
            generate_video = state.get('generate_video', False)
        else:
            unified_prayer_text = getattr(state, 'unified_prayer_markdown', '')
            prayer_theme = getattr(state, 'prayer_focus_theme', 'Spiritual Healing and Divine Connection')
            generate_video = getattr(state, 'generate_video', False)

        logger.info(f"Prayer theme: {prayer_theme}")
        logger.info(f"Video generation requested: {generate_video}")

        # Only generate enhanced scenes if video is requested
        if not generate_video:
            logger.info("Video generation not requested, using basic scene generation")
            return _generate_basic_scenes(state, unified_prayer_text, prayer_theme)

        # Use enhanced scene generator for video generation
        logger.info("Using enhanced LLM-powered scene generation for video...")

        # Run async scene generation in sync context
        loop = None
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        # Generate enhanced scenes
        generator = EnhancedVideoSceneGenerator()
        enhanced_scenes = loop.run_until_complete(
            generator.generate_enhanced_scenes(
                prayer_text=unified_prayer_text,
                prayer_theme=prayer_theme,
                num_scenes=5  # Generate 5 beautiful spiritual scenes
            )
        )

        # Update state with enhanced scenes
        if isinstance(state, dict):
            state['sacred_scenes'] = enhanced_scenes
            state.setdefault('execution_logs', []).append(f"Generated {len(enhanced_scenes)} enhanced spiritual scenes with LLM")
        else:
            state.sacred_scenes = enhanced_scenes
            if not hasattr(state, 'execution_logs'):
                state.execution_logs = []
            state.execution_logs.append(f"Generated {len(enhanced_scenes)} enhanced spiritual scenes with LLM")

        logger.info(f"Successfully generated {len(enhanced_scenes)} enhanced spiritual scenes")

        # Log scene details
        for i, scene in enumerate(enhanced_scenes):
            logger.info(f"Scene {i+1}: {scene.get('description', '')[:100]}...")

        return state

    except Exception as e:
        logger.error(f"Error in enhanced scene generation: {e}")
        logger.info("Falling back to basic scene generation...")

        # Fallback to basic scene generation
        return _generate_basic_scenes(state, unified_prayer_text, prayer_theme)

def _generate_basic_scenes(state: PrayerState, unified_prayer_text: str, prayer_theme: str) -> PrayerState:
    """Generate basic spiritual scenes as fallback."""
    logger.info("Generating basic spiritual scenes...")

    # Basic spiritual scenes with sacred imagery
    basic_scenes = [
        {
            "scene_id": 0,
            "description": "A magnificent Tree of Life with luminous branches reaching into cosmic dimensions, its roots intertwined with galaxies while sacred geometry patterns flow through its trunk like liquid light.",
            "video_prompt": "Cinematic view of a glowing Tree of Life with cosmic branches, sacred geometry flowing through trunk, divine lighting, slow camera movement, ethereal particles, 5 seconds",
            "visual_description": "Tree of Life with cosmic branches and sacred geometry",
            "content": "Tree of Life cosmic scene",
            "duration": 5,
            "style": "spiritual visionary art"
        },
        {
            "scene_id": 1,
            "description": "A Flower of Life mandala slowly rotating in space, each intersection point blooming with fractal lotus petals that emit healing rainbow frequencies across multiple dimensions.",
            "video_prompt": "Rotating Flower of Life mandala in space, fractal lotus petals blooming, rainbow healing frequencies, gentle rotation, divine glow, 5 seconds",
            "visual_description": "Flower of Life mandala with fractal lotus petals",
            "content": "Flower of Life mandala scene",
            "duration": 5,
            "style": "sacred geometric art"
        },
        {
            "scene_id": 2,
            "description": "Seraphim wings made of pure crystalline light unfold across a cosmic landscape, while Merkaba star tetrahedrons spin in perfect harmony, creating portals of divine energy.",
            "video_prompt": "Crystalline light Seraphim wings unfolding, Merkaba tetrahedrons spinning, divine energy portals, cosmic landscape, ethereal movement, 5 seconds",
            "visual_description": "Seraphim wings and Merkaba energy portals",
            "content": "Angelic Merkaba scene",
            "duration": 5,
            "style": "angelic spiritual art"
        }
    ]

    # Update state with basic scenes
    if isinstance(state, dict):
        state['sacred_scenes'] = basic_scenes
        state.setdefault('execution_logs', []).append(f"Generated {len(basic_scenes)} basic spiritual scenes")
    else:
        state.sacred_scenes = basic_scenes
        if not hasattr(state, 'execution_logs'):
            state.execution_logs = []
        state.execution_logs.append(f"Generated {len(basic_scenes)} basic spiritual scenes")

    logger.info(f"Generated {len(basic_scenes)} basic spiritual scenes")
    return state
