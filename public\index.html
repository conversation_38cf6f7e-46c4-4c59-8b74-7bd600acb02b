
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prayer Image Generator</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1 {
      color: #2c3e50;
      text-align: center;
    }
    .prayer-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 20px;
      margin-top: 20px;
    }
    .prayer-item {
      background-color: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      cursor: pointer;
      transition: transform 0.2s;
    }
    .prayer-item:hover {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    .prompts-container {
      margin-top: 30px;
      background-color: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .prompt-item {
      margin-bottom: 15px;
      padding: 10px;
      background-color: #f0f7ff;
      border-radius: 4px;
    }
    .image-gallery {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 30px;
    }
    .image-container {
      background-color: white;
      border-radius: 8px;
      padding: 15px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .image-container img {
      width: 100%;
      height: auto;
      border-radius: 4px;
    }
  </style>
</head>
<body>
  <h1>Prayer to Image Generator</h1>
  
  <div class="prayer-list" id="prayerList">
    <!-- Prayer items will be populated here -->
    <div class="prayer-item">Loading prayers...</div>
  </div>
  
  <div class="prompts-container" id="promptsContainer" style="display:none;">
    <h2 id="selectedPrayerTitle">Selected Prayer</h2>
    <div id="promptsList">
      <!-- Prompts will be shown here -->
    </div>
    <div class="image-gallery" id="imageGallery">
      <!-- Generated images will be shown here -->
    </div>
  </div>
  
  <script>
    // Fetch prayer files when page loads
    window.addEventListener('DOMContentLoaded', async () => {
      try {
        const response = await fetch('/prayers');
        const data = await response.json();
        
        const prayerList = document.getElementById('prayerList');
        prayerList.innerHTML = '';
        
        data.prayers.forEach(prayer => {
          const prayerItem = document.createElement('div');
          prayerItem.className = 'prayer-item';
          prayerItem.textContent = prayer.name;
          prayerItem.onclick = () => generatePromptsForPrayer(prayer);
          prayerList.appendChild(prayerItem);
        });
      } catch (error) {
        console.error('Error fetching prayers:', error);
        document.getElementById('prayerList').innerHTML = '<div class="prayer-item">Error loading prayers</div>';
      }
    });
    
    // Generate prompts for selected prayer
    async function generatePromptsForPrayer(prayer) {
      try {
        const response = await fetch('/generate-prompts', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ prayerFilePath: prayer.path })
        });
        
        const data = await response.json();
        
        // Show prompts container
        const promptsContainer = document.getElementById('promptsContainer');
        promptsContainer.style.display = 'block';
        
        // Set prayer title
        document.getElementById('selectedPrayerTitle').textContent = prayer.name;
        
        // Display prompts
        const promptsList = document.getElementById('promptsList');
        promptsList.innerHTML = '';
        
        data.prompts.forEach((prompt, index) => {
          const promptItem = document.createElement('div');
          promptItem.className = 'prompt-item';
          promptItem.innerHTML = '<strong>Prompt ' + (index + 1) + ':</strong> ' + prompt;
          promptsList.appendChild(promptItem);
          
          // In a complete implementation, this would call the Replicate API 
          // to generate images based on the prompts
        });
        
        // In a real implementation, you would display generated images here
        const imageGallery = document.getElementById('imageGallery');
        imageGallery.innerHTML = '<p>In a complete implementation, images would be generated here using the Replicate API.</p>';
        
      } catch (error) {
        console.error('Error generating prompts:', error);
        alert('Failed to generate prompts. Please try again.');
      }
    }
  </script>
</body>
</html>
