name: Pre-Release (RC)

on:
  workflow_dispatch: # Allows manual triggering from GitHub UI/API
  push:
    branches:
      - 'next'

concurrency: pre-release-${{ github.ref }}

jobs:
  rc:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            */*/node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci
        timeout-minutes: 2

      - name: Enter RC mode
        run: |
          npx changeset pre exit || true
          npx changeset pre enter rc

      - name: Version RC packages
        run: |
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          npx changeset version
          git add .
          git commit -m "chore: rc version bump" || echo "No changes to commit"

      - name: Create Release Candidate Pull Request or Publish Release Candidate to npm
        uses: changesets/action@v1
        with:
          publish: npm run release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
