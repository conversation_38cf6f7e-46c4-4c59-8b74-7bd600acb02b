import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/auth_service.dart';

// Provider for the auth service
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

// Provider for the auth state changes stream
final authStateProvider = StreamProvider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user != null,
    loading: () => false,
    error: (_, __) => false,
  );
});

// Provider to get current user
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (user) => user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// Auth notifier
class AuthNotifier extends StateNotifier<AsyncValue<User?>> {
  final AuthService _authService;
  
  AuthNotifier(this._authService) : super(const AsyncValue.loading()) {
    _init();
  }
  
  void _init() {
    _authService.authStateChanges.listen(
      (user) => state = AsyncValue.data(user),
      onError: (error) => state = AsyncValue.error(error, StackTrace.current),
    );
  }
  
  // Sign in with Google
  Future<void> signInWithGoogle() async {
    try {
      state = const AsyncValue.loading();
      await _authService.signInWithGoogle();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Sign in with Apple
  Future<void> signInWithApple() async {
    try {
      state = const AsyncValue.loading();
      await _authService.signInWithApple();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Sign in with GitHub
  Future<void> signInWithGitHub(BuildContext context) async {
    try {
      state = const AsyncValue.loading();
      await _authService.signInWithGitHub(context);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Sign in anonymously
  Future<void> signInAnonymously() async {
    try {
      state = const AsyncValue.loading();
      await _authService.signInAnonymously();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Sign out
  Future<void> signOut() async {
    try {
      await _authService.signOut();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// Provider for the auth notifier
final authNotifierProvider = StateNotifierProvider<AuthNotifier, AsyncValue<User?>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});
