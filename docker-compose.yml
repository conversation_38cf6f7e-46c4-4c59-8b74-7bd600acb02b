version: '3.8'

services:
  vibe-prayer-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vibe-prayer-omnifaith
    environment:
      - PYTHONPATH=/app
      - GROQ_API_KEY=${GROQ_API_KEY}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - XAI_API_KEY=${XAI_API_KEY}
      - SAMBANOVA_API_KEY=${SAMBANOVA_API_KEY}
      - FAL_API_KEY=${FAL_API_KEY}
      - LLAMA_API_KEY=${LLAMA_API_KEY}
      - NO_TORCH_COMPILE=1
    volumes:
      - ./output:/app/output
      - ./Saved_Prayers:/app/Saved_Prayers
    ports:
      - "8000:8000"
    restart: unless-stopped
    stdin_open: true
    tty: true
    networks:
      - prayer-network

  # Optional: Add a web interface service (if api/app.py exists)
  web-interface:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vibe-prayer-web
    command: python api/app.py
    environment:
      - PYTHONPATH=/app
      - GROQ_API_KEY=${GROQ_API_KEY}
      - PERPLEXITY_API_KEY=${PERPLEXITY_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY}
      - HUGGING_FACE_HUB_TOKEN=${HUGGING_FACE_HUB_TOKEN}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - BRAVE_API_KEY=${BRAVE_API_KEY}
      - REPLICATE_API_KEY=${REPLICATE_API_KEY}
      - XAI_API_KEY=${XAI_API_KEY}
      - SAMBANOVA_API_KEY=${SAMBANOVA_API_KEY}
      - FAL_API_KEY=${FAL_API_KEY}
      - LLAMA_API_KEY=${LLAMA_API_KEY}
      - NO_TORCH_COMPILE=1
    volumes:
      - ./output:/app/output
      - ./Saved_Prayers:/app/Saved_Prayers
    ports:
      - "8080:8080"
    depends_on:
      - vibe-prayer-app
    restart: unless-stopped
    networks:
      - prayer-network
    profiles:
      - web

networks:
  prayer-network:
    driver: bridge

volumes:
  prayer-output:
    driver: local
