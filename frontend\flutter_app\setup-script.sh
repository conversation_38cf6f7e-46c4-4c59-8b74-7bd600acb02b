#!/bin/bash

# SpiritSync Setup Script
# This script creates the necessary folder structure and placeholder files for the SpiritSync app

echo "🙏 Setting up SpiritSync folder structure..."

# Create main directories
mkdir -p lib/{constants,models,services,providers,screens,widgets,theme,routes}
mkdir -p assets/{images,animations,fonts}
mkdir -p test/{unit,widget,integration}

# Create placeholder image files
touch assets/images/prayer_background.jpg
touch assets/images/pattern_light.png
touch assets/images/pattern_dark.png
touch assets/images/app_preview.png

# Create placeholder animation files
touch assets/animations/loading_animation.json
touch assets/animations/success_animation.json

# Create placeholder font files
touch assets/fonts/Poppins-Regular.ttf
touch assets/fonts/Poppins-Bold.ttf
touch assets/fonts/Poppins-Light.ttf
touch assets/fonts/Poppins-Medium.ttf

# Create Firebase configuration placeholder
touch firebase_options.dart

echo "📂 Folder structure created successfully!"
echo ""
echo "🚀 Next steps:"
echo "1. Run 'flutter pub get' to install dependencies"
echo "2. Configure Firebase with 'flutterfire configure'"
echo "3. Update the baseApiUrl in lib/constants/app_constants.dart"
echo "4. Add your background images to the assets/images folder"
echo "5. Run the app with 'flutter run'"
echo ""
echo "✨ Happy coding! ✨"
