# divine_names.py

"""
Centralized multifaith divine names and spiritual wisdom data module.
Provides structured access to names/titles for the Creator, healing, compassion, protection, peace, wisdom, and universal titles.
Includes a utility for "universal with specific" formatting.
"""

DIVINE_NAMES = {
    "creator": {
        "universal": "Divine Source",
        "specific": [
            "<PERSON>", "YH<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>i", "El Shaddai", "El Elyon", "Abba",
            "<PERSON>rah<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Bhagavan", "Paramatma", "Ahura Mazda", "Waheguru", "<PERSON><PERSON><PERSON>",
            "Great Spirit", "Wakan Tanka", "Gitche Manitou", "Orend<PERSON>", "<PERSON>jaw", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON> and <PERSON>",
            "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>lodu<PERSON>", "<PERSON><PERSON> Rabbi", "<PERSON>"
        ]
    },
    "healing": {
        "universal": "Divine Healer",
        "specific": [
            "Jehovah Rapha", "Ash-<PERSON><PERSON><PERSON>", "Al-<PERSON><PERSON><PERSON><PERSON>", "Holy Spirit", "<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>", "<PERSON> (<PERSON><PERSON><PERSON><PERSON>)", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON> Tara",
            "<PERSON><PERSON> Chel", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>pius"
        ]
    },
    "compassion": {
        "universal": "Source of Compassion",
        "specific": [
            "Ar-<PERSON>", "<PERSON>r-<PERSON><PERSON>", "El <PERSON>chum", "El <PERSON>nun", "<PERSON>",
            "Karuna", "<PERSON><PERSON><PERSON><PERSON>hvara", "<PERSON>", "<PERSON><PERSON> <PERSON>ituo<PERSON>", "<PERSON>pani",
            "<PERSON><PERSON>yin", "<PERSON><PERSON> <PERSON>", "Inari", "<PERSON><PERSON>n"
        ]
    },
    "protection": {
        "universal": "Divine Protector",
        "specific": [
            "Jehovah Nissi", "Al-Hafiz", "Al-Muhaymin", "El Sali", "Michael",
            "Durga", "Mahakala", "Ganesha", "Hanuman", "Vajrapani",
            "Thunderbird", "Sedna", "Pele", "Athena", "Heimdall"
        ]
    },
    "peace": {
        "universal": "Source of Peace",
        "specific": [
            "Jehovah Shalom", "As-Salam", "Prince of Peace", "Sar Shalom", "Emmanuel",
            "Shanti", "Vishnu", "Buddha", "Ahimsa", "Krishna (Yogeshvara)",
            "Enekpe", "Eirene", "Concordia", "Pax", "Heiwa"
        ]
    },
    "wisdom": {
        "universal": "Infinite Wisdom",
        "specific": [
            "Hakadosh", "El Roi", "Al-Hakim", "Al-'Alim", "Sophia", "Logos",
            "Saraswati", "Hayagriva", "Manjushri", "Prajnaparamita", "Ganesha",
            "Orunmila", "Athena", "Minerva", "Thoth", "Odin", "Sarasvati"
        ]
    },
    "universal_titles": [
        "Divine Source", "Creator", "Great Spirit", "Divine Healer", "Source of Compassion",
        "Eternal Light", "Divine Presence", "Sacred Mystery", "Infinite Wisdom", "Universal Love",
        "Divine Mother", "Divine Father", "Great Mystery"
    ]
}

def get_universal_with_specific(concept: str) -> str:
    """
    Returns a string in the "universal with specific" format for the given concept.
    Example: "Divine Healer, known as Jehovah Rapha to some, as Ash-Shafi to others, as Bhaisajyaguru to others..."
    """
    entry = DIVINE_NAMES.get(concept)
    if not entry:
        return ""
    universal = entry.get("universal", "")
    specific = entry.get("specific", [])
    if not universal or not specific:
        return ""
    # Format: "Divine Healer, known as Jehovah Rapha to some, as Ash-Shafi to others, ..."
    specifics = ", ".join([f"as {name} to others" for name in specific])
    return f"{universal}, known as {specific[0]} to some, {', '.join([f'as {name} to others' for name in specific[1:]])}"