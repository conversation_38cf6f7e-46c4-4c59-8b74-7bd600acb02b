"""
Async Video Generation Nodes for LangGraph
Handles parallel video processing with progress tracking
"""

import asyncio
import logging
import os
from typing import Dict, List, Any
from models.prayer_state import PrayerState
import replicate
from replicate.exceptions import ModelError
from dotenv import load_dotenv
from utils.file_utils import download_video_async, ensure_video_directory

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

async def async_video_generation_node(state: PrayerState) -> PrayerState:
    """
    Generate videos asynchronously for prayer scenes using Replicate Python SDK.
    Handles parallel video processing with progress tracking.
    """
    logger.info("Starting async video generation with Replicate SDK")
    
    # Skip if video generation is not enabled or no scenes
    if not state.get('generate_video', False) or not state.get('sacred_scenes'):
        logger.info("Video generation skipped - not enabled or no scenes")
        return state
    
    try:
        # Extract scenes for video generation
        scenes = state.get('sacred_scenes', [])
        logger.info(f"Generating videos for {len(scenes)} scenes")
        
        # Create a unique prayer ID for organizing videos
        prayer_id = state.get('prayer_id', f"prayer_{state.get('timestamp', '')}")
        
        # Ensure video directory exists
        ensure_video_directory(prayer_id)
        
        # Track progress
        state['async_tasks_status'] = state.get('async_tasks_status', {})
        state['async_tasks_status']['video_generation'] = 'in_progress'
        
        # Initialize Replicate client
        api_key = os.environ.get("REPLICATE_API_KEY")
        if not api_key:
            raise ValueError("REPLICATE_API_KEY not found in environment variables")
        
        # Prepare video generation tasks
        tasks = []
        for i, scene in enumerate(scenes):
            prompt = scene.get('description', '')
            model = scene.get('video_model', 'meta/veo-2')
            
            # Add task to list
            tasks.append(generate_video_for_scene(
                scene=scene,
                index=i,
                model=model,
                prayer_id=prayer_id
            ))
        
        # Run all video generation tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        successful_videos = []
        failed_videos = []
        
        for result in results:
            if isinstance(result, Exception):
                # Handle exception
                failed_videos.append({
                    "error": str(result),
                    "timestamp": state.get('timestamp')
                })
            elif result.get("error"):
                # Handle API error
                failed_videos.append(result)
            else:
                # Handle success
                successful_videos.append(result)
        
        # Update state with results
        state['generated_videos'] = successful_videos
        state['video_generation_errors'] = failed_videos
        
        # Set primary video URL and local path
        if successful_videos:
            state['generated_video'] = successful_videos[0].get("video_url")
            state['generated_video_local_path'] = successful_videos[0].get("local_path")
        
        # Update status
        state['async_tasks_status']['video_generation'] = 'completed'
        
        if failed_videos:
            state.setdefault('execution_logs', []).append(f"Failed to generate {len(failed_videos)} videos")

        logger.info(f"Video generation completed: {len(successful_videos)} successful, {len(failed_videos)} failed")

    except Exception as e:
        logger.error(f"Error in async video generation: {e}")
        state.setdefault('execution_logs', []).append(f"Video generation error: {str(e)}")
        state['video_generation_error'] = str(e)
        state['async_tasks_status']['video_generation'] = 'failed'

    return state

async def generate_video_for_scene(scene: Dict, index: int, model: str = "meta/veo-2", prayer_id: str = None) -> Dict:
    """
    Generate a video for a single scene and download it
    
    Args:
        scene: Scene description dictionary
        index: Scene index for tracking
        model: Replicate model to use
        prayer_id: Prayer ID for organizing videos
        
    Returns:
        Dictionary with video information including local path
    """
    try:
        # Extract scene information
        prompt = scene.get('description', '')
        scene_name = scene.get('name', f'scene_{index}')
        
        logger.info(f"Generating video {index+1}: {scene_name}")
        
        # Prepare input for Replicate
        input_params = {
            "prompt": prompt,
            "num_frames": 24,  # Default for most models
            "fps": 8,
        }
        
        # Add model-specific parameters
        if model == "meta/veo-2":
            input_params["fps"] = 24
            input_params["seconds"] = scene.get('duration', 3)
        elif "stable-video-diffusion" in model:
            input_params["motion_bucket_id"] = 127  # High motion
            input_params["fps"] = 24
        
        # Run the model
        output = replicate.run(model, input=input_params)
        
        # Extract video URL based on model output format
        video_url = None
        if isinstance(output, dict):
            video_url = output.get('video') or output.get('output_video')
        elif isinstance(output, list) and len(output) > 0:
            video_url = output[0]  # Some models return a list with the URL as first item
        else:
            video_url = output  # Some models return the URL directly
        
        if not video_url:
            return {
                "error": f"Model {model} did not return a valid video URL",
                "scene": scene_name,
                "prompt": prompt,
                "model": model
            }
        
        # Download the video
        download_result = await download_video_async(
            video_url=video_url,
            prayer_id=prayer_id,
            scene_name=scene_name
        )
        
        # Return combined result
        return {
            "video_url": video_url,
            "local_path": download_result.get("local_path"),
            "scene": scene_name,
            "prompt": prompt,
            "model": model,
            "index": index,
            "prayer_id": prayer_id,
            "download_success": download_result.get("success", False)
        }
        
    except Exception as e:
        logger.error(f"Error generating video for scene {index}: {e}")
        return {
            "error": str(e),
            "scene": scene.get('name', f'scene_{index}'),
            "prompt": scene.get('description', ''),
            "model": model,
            "index": index
        }


async def parallel_media_generation_node(state: PrayerState) -> PrayerState:
    """
    Run image, video, and audio generation in parallel
    This is the main coordination node for async media processing
    """
    logger.info("Starting parallel media generation")

    try:
        # Prepare tasks for parallel execution
        tasks = []
        task_names = []

        # Video generation task
        if state.get('generate_video', False):
            tasks.append(async_video_generation_node(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("video_generation")

        # Image generation task
        if state.get('image_prayer_requested', False):
            tasks.append(async_image_generation_task(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("image_generation")

        # Audio generation task
        if state.get('audio_conversion_requested', False):
            tasks.append(async_audio_generation_task(state.copy()))  # Pass copy to avoid state conflicts
            task_names.append("audio_generation")

        if not tasks:
            logger.info("No media generation tasks requested")
            state.setdefault('execution_logs', []).append("No media generation tasks to run")
            return state

        state.setdefault('execution_logs', []).append(f"Running {len(tasks)} media generation tasks in parallel: {', '.join(task_names)}")

        # Run all media generation tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and merge state updates
        for i, result in enumerate(results):
            task_name = task_names[i]

            if isinstance(result, Exception):
                logger.error(f"Task {task_name} failed: {result}")
                state.setdefault('execution_logs', []).append(f"Media generation task '{task_name}' failed: {str(result)}")
            else:
                # Merge successful results back into main state
                result_videos = result.get('generated_videos')
                if result_videos and isinstance(result_videos, list):
                    state['generated_videos'] = state.get('generated_videos', []) + result_videos

                result_images = result.get('generated_images')
                if result_images and isinstance(result_images, list):
                    state['generated_images'] = state.get('generated_images', []) + result_images

                if result.get('generated_audio'):
                    state['generated_audio'] = result['generated_audio']

                # Merge execution logs
                if result.get('execution_logs'):
                    state.setdefault('execution_logs', []).extend(result['execution_logs'])

                logger.info(f"Task {task_name} completed successfully")

        state.setdefault('execution_logs', []).append("Parallel media generation completed")

    except Exception as e:
        logger.error(f"Error in parallel media generation: {e}")
        state.setdefault('execution_logs', []).append(f"Parallel media generation error: {str(e)}")

    return state


async def async_image_generation_task(state: PrayerState) -> PrayerState:
    """
    Async wrapper for image generation
    This allows image generation to run in parallel with video generation
    """
    logger.info("Starting async image generation")

    try:
        # Import the existing image generation node
        from nodes.updated_image_prayer_node import generate_image_prayer_node

        # Run the image generation
        result_state = await generate_image_prayer_node(state) # Await directly

        logger.info("Async image generation completed")
        return result_state

    except Exception as e:
        logger.error(f"Error in async image generation: {e}")
        state.setdefault('execution_logs', []).append(f"Async image generation error: {str(e)}")
        return state


async def async_audio_generation_task(state: PrayerState) -> PrayerState:
    """
    Async wrapper for audio generation
    This allows audio generation to run in parallel with other media
    """
    logger.info("Starting async audio generation")

    try:
        # Import the existing audio generation nodes
        from nodes.tts_nodes import prepare_tts_text_node, generate_audio_node

        # Check that we have the required state fields
        if not state.get('unified_prayer_filepath'):
            error_msg = "No unified_prayer_filepath found in state for audio generation"
            logger.error(error_msg)
            state.setdefault('execution_logs', []).append(error_msg)
            return state

        # Run TTS preparation and generation in thread executor
        # to avoid blocking the event loop
        loop = asyncio.get_event_loop()

        # Run TTS preparation in a separate thread
        state = await loop.run_in_executor(None, prepare_tts_text_node, state)

        # Run audio generation in a separate thread
        state = await loop.run_in_executor(None, generate_audio_node, state)

        logger.info("Async audio generation completed")
        return state

    except Exception as e:
        logger.error(f"Error in async audio generation: {e}")
        # Handle state consistently
        if isinstance(state, dict):
            state.setdefault('execution_logs', []).append(f"Async audio generation error: {str(e)}")
        else:
            execution_logs = getattr(state, 'execution_logs', [])
            execution_logs.append(f"Async audio generation error: {str(e)}")
            state.execution_logs = execution_logs
        return state


def should_run_parallel_media_generation(state: PrayerState) -> str:
    """
    Conditional check: Should we run parallel media generation?
    """
    generate_video = state.get('generate_video', False)
    generate_images = state.get('image_prayer_requested', False) # Use image_prayer_requested
    audio_requested = state.get('audio_conversion_requested', False)

    # Run parallel generation if any media type is requested
    if generate_video or generate_images or audio_requested:
        logger.info("Parallel media generation requested")
        return "run_parallel_media"
    else:
        logger.info("No media generation requested")
        return "skip_media"


async def media_aggregation_node(state: PrayerState) -> PrayerState:
    """
    Aggregate all generated media and create final unified output
    """
    logger.info("Starting media aggregation")

    try:
        # Create a comprehensive media summary
        media_summary = {}

        # Video summary
        if state.get('generated_videos'):
            media_summary['videos'] = {
                'count': len(state['generated_videos']),
                'urls': [v.get('video_url') for v in state['generated_videos'] if v.get('video_url')],
                'primary_video': state['generated_videos'][0].get('video_url') if state['generated_videos'] else None
            }
            state['generated_video'] = media_summary['videos']['primary_video']

        # Image summary
        if state.get('generated_images'):
            media_summary['images'] = {
                'count': len(state['generated_images']),
                'urls': state['generated_images']
            }

        # Audio summary
        if state.get('generated_audio'):
            media_summary['audio'] = {
                'url': state['generated_audio']
            }

        # Update unified prayer with media links
        if state.get('unified_prayer_markdown'):
            # Add media section to the unified prayer
            media_section = "\n\n## Generated Media\n\n"

            if 'videos' in media_summary:
                media_section += f"### Prayer Videos ({media_summary['videos']['count']} generated)\n"
                for i, url in enumerate(media_summary['videos']['urls']):
                    media_section += f"- [Prayer Video {i+1}]({url})\n"
                media_section += "\n"

            if 'images' in media_summary:
                media_section += f"### Prayer Images ({media_summary['images']['count']} generated)\n"
                for i, url in enumerate(media_summary['images']['urls']):
                    media_section += f"- [Prayer Image {i+1}]({url})\n"
                media_section += "\n"

            if 'audio' in media_summary:
                media_section += f"### Prayer Audio\n"
                media_section += f"- [Prayer Audio]({media_summary['audio']['url']})\n\n"

            # Append media section to unified prayer
            state['unified_prayer_markdown'] += media_section

        state['media_summary'] = media_summary
        state.setdefault('execution_logs', []).append("Media aggregation completed")

        logger.info(f"Media aggregation completed: {len(media_summary)} media types processed")

    except Exception as e:
        logger.error(f"Error in media aggregation: {e}")
        state.setdefault('execution_logs', []).append(f"Media aggregation error: {str(e)}")

    return state
