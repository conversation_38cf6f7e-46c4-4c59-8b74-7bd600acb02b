"""
Prayer Application Graph Definition
Defines the LangGraph workflow for the prayer generation and TTS processes.
"""

from langgraph.graph import StateGraph, END

from models.prayer_state import PrayerState
from nodes.research_nodes import perform_research_node
from nodes.prayer_generation import (
    prepare_next_movement_node,
    generate_single_prayer_node,
    save_individual_prayer_node,
    should_continue_generating
)
from nodes.prayer_aggregation import (
    aggregate_prayers_node,
    generate_unified_prayer_node,
    save_unified_prayer_node,
)
from nodes.tts_nodes import (
    ask_audio_conversion_node,
    prepare_tts_text_node,
    generate_audio_node,
    audio_requested
)

# Import the image prayer nodes
try:
    # Try to use the updated image prayer nodes first
    from nodes.updated_image_prayer_node import (
        generate_image_prayer_node,
        should_generate_image_prayer
    )
    print("[INFO] Using updated image prayer nodes with Replicate API integration.")
except ImportError:
    try:
        # Fall back to the robust image prayer nodes if available
        from nodes.image_prayer_node import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[INFO] Using robust image prayer nodes.")
    except ImportError:
        # Fall back to original nodes if neither are available
        from nodes.gemini_image_prayer import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("[WARNING] Using original image prayer nodes. Consider using the updated version.")

def create_prayer_workflow() -> StateGraph:
    """
    Create the LangGraph workflow for prayer generation, TTS, and image prayer generation.

    Returns:
        Compiled LangGraph workflow
    """
    # Initialize the graph with our state model
    workflow = StateGraph(PrayerState)

    # Add research node
    workflow.add_node("perform_research", perform_research_node)

    # Add individual prayer generation nodes
    workflow.add_node("prepare_movement", prepare_next_movement_node)
    workflow.add_node("generate_prayer", generate_single_prayer_node)
    workflow.add_node("save_prayer", save_individual_prayer_node)

    # Add unified prayer generation nodes
    workflow.add_node("aggregate_prayers", aggregate_prayers_node)
    workflow.add_node("generate_unified", generate_unified_prayer_node)
    workflow.add_node("save_unified", save_unified_prayer_node)

    # Add TTS nodes
    workflow.add_node("ask_audio_conversion", ask_audio_conversion_node)
    workflow.add_node("prepare_tts_text", prepare_tts_text_node)
    workflow.add_node("generate_audio", generate_audio_node)

    # Add image prayer nodes
    workflow.add_node("check_image_prayer", lambda x: x)  # Simple passthrough node
    workflow.add_node("generate_image_prayer", generate_image_prayer_node)

    # Set the entry point - start with research
    workflow.set_entry_point("perform_research")

    # Connect research to prayer generation
    workflow.add_edge("perform_research", "prepare_movement")

    # Define the individual prayer generation cycle
    workflow.add_edge("prepare_movement", "generate_prayer")
    workflow.add_edge("generate_prayer", "save_prayer")

    # Conditional edge after saving individual prayer
    workflow.add_conditional_edges(
        "save_prayer",
        should_continue_generating,
        {
            "continue_generation": "prepare_movement",  # Loop back if more movements
            "aggregate": "aggregate_prayers",           # Go to aggregate when done looping
            "end_error": END                            # End immediately on error
        }
    )

    # Unified prayer generation flow
    workflow.add_edge("aggregate_prayers", "generate_unified")
    workflow.add_edge("generate_unified", "save_unified")

    # TTS flow - sequential, not parallel
    workflow.add_conditional_edges(
        "save_unified",
        should_run_tts,
        {
            "run_tts": "ask_audio_conversion",  # Go to TTS flow
            "skip_tts": "check_image_prayer"    # Skip to image prayer check
        }
    )

    # TTS Branch Execution
    workflow.add_conditional_edges(
        "ask_audio_conversion",
        audio_requested,
        {
            "yes": "prepare_tts_text",
            "no": "check_image_prayer"   # If user declines TTS, go to image prayer check
        }
    )
    workflow.add_edge("prepare_tts_text", "generate_audio")
    workflow.add_edge("generate_audio", "check_image_prayer")  # After audio, go to image prayer check

    # Image Prayer flow
    workflow.add_conditional_edges(
        "check_image_prayer",
        should_generate_image_prayer,
        {
            "generate_image": "generate_image_prayer",  # Go to image prayer generation
            "skip_image": END                          # Skip directly to end
        }
    )

    workflow.add_edge("generate_image_prayer", END)  # After image prayer, go to end

    # Note: Error handling is implemented differently in LangGraph 0.2.21
    # The add_exception_handler method is not available in this version
    
    print("[INFO] Using LangGraph 0.2.21 native error handling")
    
    # Compile the graph
    return workflow.compile()

def should_run_tts(state: PrayerState) -> str:
    """Conditional check: Should the TTS branch run?"""
    # Check if we should run the TTS confirmation step
    run_tts = False
    if isinstance(state, dict):
        # Only check audio_conversion_requested here, which is set during initialization
        run_tts = state.get('audio_conversion_requested', True)
    else:
        run_tts = getattr(state, 'audio_conversion_requested', True)

    print(f"[CONDITIONAL] Should run TTS? {run_tts}")
    return "run_tts" if run_tts else "skip_tts"
