#!/usr/bin/env python3
"""
Test script for the enhanced video scene generation with beautiful spiritual imagery.
Tests LLM-powered scene creation with symbols, mandalas, angels, healing themes, sacred geometry, and fractals.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from nodes.enhanced_video_scene_generator import EnhancedVideoSceneGenerator
from nodes.scene_generation_nodes import generate_sacred_scenes_node
from models.prayer_state import PrayerState

def test_enhanced_scene_generator():
    """Test the enhanced video scene generator directly."""
    print("🧪 Testing Enhanced Video Scene Generator...")
    
    try:
        generator = EnhancedVideoSceneGenerator()
        
        # Test spiritual elements
        print("✅ Sacred symbols loaded:", len(generator.sacred_symbols))
        print("✅ Angelic elements loaded:", len(generator.angelic_elements))
        print("✅ Healing themes loaded:", len(generator.healing_themes))
        print("✅ Sacred geometry loaded:", len(generator.sacred_geometry))
        print("✅ Fractal elements loaded:", len(generator.fractal_elements))
        
        # Test random element selection
        random_elements = generator._get_random_spiritual_elements()
        print("✅ Random spiritual elements generated:")
        for key, value in random_elements.items():
            print(f"   {key}: {value[:50]}...")
        
        return True
    except Exception as e:
        print(f"❌ Enhanced scene generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_scene_generation():
    """Test async scene generation with sample prayer."""
    print("\n🧪 Testing Async Scene Generation...")
    
    try:
        generator = EnhancedVideoSceneGenerator()
        
        sample_prayer = """
        Divine Source of all creation, we gather in unity to seek healing and wisdom.
        May the light of understanding illuminate our path, and may sacred geometry
        guide our consciousness toward higher realms. Let the Tree of Life flourish
        within our hearts, and may angelic presence surround us with protection.
        Through the power of divine love, we manifest healing for all beings.
        """
        
        sample_theme = "Divine Healing and Sacred Geometry"
        
        print(f"Prayer theme: {sample_theme}")
        print(f"Prayer text length: {len(sample_prayer)} characters")
        
        # Generate enhanced scenes
        scenes = await generator.generate_enhanced_scenes(
            prayer_text=sample_prayer,
            prayer_theme=sample_theme,
            num_scenes=3
        )
        
        print(f"✅ Generated {len(scenes)} enhanced scenes")
        
        for i, scene in enumerate(scenes):
            print(f"\n📋 Scene {i+1}:")
            print(f"   Description: {scene.get('description', '')[:100]}...")
            print(f"   Video Prompt: {scene.get('video_prompt', '')[:100]}...")
            print(f"   Style: {scene.get('style', 'N/A')}")
            print(f"   Duration: {scene.get('duration', 'N/A')} seconds")
            
            if 'spiritual_elements' in scene:
                elements = scene['spiritual_elements']
                print(f"   Sacred Symbol: {elements.get('sacred_symbol', 'N/A')[:50]}...")
                print(f"   Angelic Element: {elements.get('angelic_element', 'N/A')[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Async scene generation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scene_generation_node():
    """Test the enhanced scene generation node."""
    print("\n🧪 Testing Scene Generation Node...")
    
    try:
        # Create test state
        test_state = {
            'unified_prayer_markdown': """
            # Universal Prayer for Healing and Wisdom
            
            Divine Source of infinite love and light, we come before you with hearts
            open to receive your sacred wisdom. May the Tree of Life flourish within
            our consciousness, its branches reaching toward cosmic understanding while
            its roots ground us in divine truth.
            
            Let sacred geometry guide our perception, revealing the hidden patterns
            that connect all existence. May angelic beings surround us with protection
            and healing energy, their crystalline light transforming all that needs
            renewal.
            
            Through the power of divine love, we manifest healing for ourselves,
            our loved ones, and all beings throughout the cosmos. May fractal
            patterns of blessing expand infinitely, touching every corner of creation.
            """,
            'prayer_focus_theme': 'Divine Healing Through Sacred Geometry and Angelic Light',
            'generate_video': True,
            'execution_logs': []
        }
        
        # Test enhanced scene generation
        result_state = generate_sacred_scenes_node(test_state)
        
        sacred_scenes = result_state.get('sacred_scenes', [])
        print(f"✅ Generated {len(sacred_scenes)} scenes through node")
        
        for i, scene in enumerate(sacred_scenes):
            print(f"\n📋 Node Scene {i+1}:")
            print(f"   ID: {scene.get('scene_id', 'N/A')}")
            print(f"   Description: {scene.get('description', '')[:100]}...")
            print(f"   Video Prompt: {scene.get('video_prompt', '')[:100]}...")
            
        # Test fallback with video disabled
        test_state_no_video = test_state.copy()
        test_state_no_video['generate_video'] = False
        
        result_state_basic = generate_sacred_scenes_node(test_state_no_video)
        basic_scenes = result_state_basic.get('sacred_scenes', [])
        print(f"✅ Generated {len(basic_scenes)} basic scenes (video disabled)")
        
        return True
        
    except Exception as e:
        print(f"❌ Scene generation node test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_availability():
    """Test if required APIs are available."""
    print("\n🧪 Testing API Availability...")
    
    gemini_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
    xai_key = os.getenv("XAI_API_KEY")
    
    if gemini_key:
        print(f"✅ Gemini API key found: {gemini_key[:10]}...")
    else:
        print("⚠️  Gemini API key not found")
    
    if xai_key:
        print(f"✅ XAI API key found: {xai_key[:10]}...")
    else:
        print("⚠️  XAI API key not found")
    
    if not gemini_key and not xai_key:
        print("⚠️  No LLM API keys found - will use fallback generation")
    
    return True

def main():
    """Run all enhanced video scene tests."""
    print("🚀 Starting Enhanced Video Scene Generation Tests")
    print("=" * 70)
    
    tests = [
        ("API Availability", test_api_availability),
        ("Enhanced Scene Generator", test_enhanced_scene_generator),
        ("Async Scene Generation", lambda: asyncio.run(test_async_scene_generation())),
        ("Scene Generation Node", test_scene_generation_node)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Enhanced video scene generation is working!")
        print("\n💡 Key features implemented:")
        print("   🕉️  Sacred symbols (Tree of Life, Flower of Life, Merkaba, etc.)")
        print("   👼 Angelic elements (Seraphim wings, divine light beings)")
        print("   🌟 Healing themes (Chakras, auras, energy fields)")
        print("   📐 Sacred geometry (Platonic solids, golden ratio spirals)")
        print("   🌀 Fractal patterns (Mandelbrot sets, Julia sets)")
        print("   🤖 Advanced LLM prompting for beautiful spiritual scenes")
        print("   🎬 Optimized video generation prompts")
        print("   🔄 Graceful fallbacks when APIs are unavailable")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
