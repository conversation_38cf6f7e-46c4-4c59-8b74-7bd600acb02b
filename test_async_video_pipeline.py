"""
Test script for the async video pipeline integration with LangGraph and PydanticAI
"""

import asyncio
import os
import logging
from typing import Dict, Any
from dotenv import load_dotenv

# Import the new async components
from agents.async_mcp_connector import AsyncMCPManager
from agents.enhanced_prayer_pipeline import <PERSON>han<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from models.prayer_state import Prayer<PERSON>tate
from graph_definition_async import create_async_prayer_workflow

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_async_mcp_connection():
    """Test the async MCP connector functionality"""
    print("\n" + "="*50)
    print("🔧 TESTING ASYNC MCP CONNECTION")
    print("="*50)
    
    try:
        # Initialize the async MCP manager
        mcp_manager = AsyncMCPManager()
        
        # Test connection to replicate MCP server
        await mcp_manager.initialize()
        
        # Check if replicate server is available
        replicate_tools = await mcp_manager.get_available_tools("replicate")
        if replicate_tools:
            print(f"✅ Replicate MCP server connected with {len(replicate_tools)} tools")
            for tool in replicate_tools[:3]:  # Show first 3 tools
                print(f"   - {tool}")
        else:
            print("⚠️ Replicate MCP server not available")
        
        # Test video generation capability
        print("\n🎬 Testing video generation capability...")
        
        test_video_result = await mcp_manager.generate_video(
            server_name="replicate",
            prompt="A peaceful meditation scene with soft lighting",
            model="meta/veo-2",
            duration=3,
            aspect_ratio="16:9"
        )
        
        if test_video_result and "video_url" in test_video_result:
            print(f"✅ Test video generation successful: {test_video_result['video_url']}")
        else:
            print("⚠️ Test video generation failed or returned no URL")
            
        await mcp_manager.cleanup()
        print("✅ MCP connection test completed")
        
    except Exception as e:
        logger.error(f"MCP connection test failed: {e}")
        print(f"❌ MCP connection test failed: {e}")

async def test_async_video_nodes():
    """Test the async video nodes from LangGraph"""
    print("\n" + "="*50)
    print("🎬 TESTING ASYNC VIDEO NODES")
    print("="*50)
    
    try:
        # Import the async video nodes
        from nodes.async_video_nodes import async_video_generation_node, parallel_media_generation_node
        
        # Create test state
        test_state = PrayerState(
            prayer_focus="Peace and healing",
            wisdom_theme="Universal compassion",
            selected_faiths=["Buddhism", "Christianity"],
            generate_video=True,
            parallel_processing_enabled=True,
            sacred_scenes=[
                {
                    "title": "Meditation Garden",
                    "description": "A serene garden with Buddha statue and flowing water",
                    "faith_tradition": "Buddhism",
                    "visual_elements": ["lotus flowers", "peaceful water", "golden light"]
                }
            ]
        )
        
        # Test individual video generation node
        print("🔄 Testing individual video generation...")
        video_result = await async_video_generation_node(test_state)
        
        if video_result.generated_video_url:
            print(f"✅ Video node test successful: {video_result.generated_video_url}")
        else:
            print("⚠️ Video node test did not generate video URL")
        
        # Test parallel media generation
        print("🔄 Testing parallel media generation...")
        parallel_result = await parallel_media_generation_node(test_state)
        
        if parallel_result.all_generated_videos:
            print(f"✅ Parallel generation successful: {len(parallel_result.all_generated_videos)} videos")
        else:
            print("⚠️ Parallel generation test completed without videos")
            
        print("✅ Async video nodes test completed")
        
    except Exception as e:
        logger.error(f"Async video nodes test failed: {e}")
        print(f"❌ Async video nodes test failed: {e}")

async def test_langraph_async_workflow():
    """Test the complete LangGraph async workflow"""
    print("\n" + "="*50)
    print("🔗 TESTING LANGRAPH ASYNC WORKFLOW")
    print("="*50)
    
    try:
        # Create the async graph
        graph = create_async_prayer_workflow()
        
        # Test input
        test_input = {
            "pray_for": "World peace and understanding",
            "wisdom_to_integrate": "Unity in diversity", 
            "selected_faiths": ["Buddhism", "Christianity"],
            "generate_video": True,
            "generate_images": False,
            "audio_conversion_requested": False,
            "parallel_processing_enabled": True
        }
        
        print("🔄 Starting async LangGraph workflow...")
        
        # Execute the graph
        result = await graph.ainvoke(test_input)
        
        if result and "unified_prayer_markdown" in result:
            print("✅ LangGraph async workflow completed successfully")
            print(f"   📝 Prayer generated: {len(result['unified_prayer_markdown'])} characters")
            
            if result.get("generated_video_url"):
                print(f"   🎬 Video generated: {result['generated_video_url']}")
            else:
                print("   ⚠️ No video generated in workflow")
        else:
            print("⚠️ LangGraph workflow completed but no prayer content found")
            
        print("✅ LangGraph async workflow test completed")
        
    except Exception as e:
        logger.error(f"LangGraph async workflow test failed: {e}")
        print(f"❌ LangGraph async workflow test failed: {e}")

async def test_enhanced_pipeline_integration():
    """Test the complete enhanced pipeline integration"""
    print("\n" + "="*50)
    print("🚀 TESTING ENHANCED PIPELINE INTEGRATION")
    print("="*50)
    
    try:
        # Initialize the enhanced pipeline
        config = {
            "gemini_api_key": os.environ.get("GOOGLE_API_KEY", ""),
        }
        
        pipeline = EnhancedPrayerPipeline(config=config)
        
        # Test input with video generation
        test_input = {
            "pray_for": "Healing and compassion",
            "wisdom_to_integrate": "Love transcends all boundaries",
            "selected_faiths": ["Christianity", "Buddhism"],
            "generate_video": True,
            "generate_images": False,
            "audio_conversion_requested": False,
            "parallel_processing_enabled": True
        }
        
        print("🔄 Running enhanced pipeline with async video generation...")
        
        # Execute the pipeline
        results = await pipeline.run(test_input)
        
        if results and "unified_prayer_markdown" in results:
            print("✅ Enhanced pipeline integration successful")
            print(f"   📝 Prayer length: {len(results['unified_prayer_markdown'])} characters")
            
            # Check video generation results
            if results.get("generated_video_url"):
                print(f"   🎬 Primary video: {results['generated_video_url']}")
            
            if results.get("all_generated_videos"):
                print(f"   📹 Total videos: {len(results['all_generated_videos'])}")
                
            # Check for any errors
            if results.get("video_generation_errors"):
                print(f"   ⚠️ Video errors: {len(results['video_generation_errors'])}")
        else:
            print("⚠️ Enhanced pipeline completed but no results found")
        
        # Cleanup
        await pipeline.cleanup()
        print("✅ Enhanced pipeline integration test completed")
        
    except Exception as e:
        logger.error(f"Enhanced pipeline integration test failed: {e}")
        print(f"❌ Enhanced pipeline integration test failed: {e}")

async def main():
    """Run all async video pipeline tests"""
    print("\n" + "="*60)
    print("🎬 ASYNC VIDEO PIPELINE INTEGRATION TESTS")
    print("="*60)
    
    # Check for required API keys
    required_keys = ["GOOGLE_API_KEY", "REPLICATE_API_TOKEN"]
    missing_keys = [key for key in required_keys if not os.environ.get(key)]
    
    if missing_keys:
        print(f"\n⚠️ Missing API keys: {', '.join(missing_keys)}")
        print("Some tests may fail or be limited without proper API keys.")
        print("Set these in your .env file for full functionality.")
    else:
        print("\n✅ All required API keys are available")
    
    # Run individual test suites
    test_suites = [
        ("Async MCP Connection", test_async_mcp_connection),
        ("Async Video Nodes", test_async_video_nodes), 
        ("LangGraph Async Workflow", test_langraph_async_workflow),
        ("Enhanced Pipeline Integration", test_enhanced_pipeline_integration)
    ]
    
    results = {}
    
    for test_name, test_func in test_suites:
        try:
            print(f"\n🧪 Starting {test_name} test...")
            await test_func()
            results[test_name] = "✅ PASSED"
        except Exception as e:
            logger.error(f"{test_name} test failed: {e}")
            results[test_name] = f"❌ FAILED: {e}"
    
    # Display test summary
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    
    for test_name, result in results.items():
        print(f"{result} {test_name}")
    
    passed_tests = sum(1 for result in results.values() if "PASSED" in result)
    total_tests = len(results)
    
    print(f"\n🎯 OVERALL: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! Async video pipeline is ready for use.")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} tests failed. Check the logs above for details.")
    
    print("\n" + "="*60)

if __name__ == "__main__":
    # Use uvloop for better async performance if available
    try:
        import uvloop
        uvloop.install()
        print("[INFO] 🚀 uvloop installed for enhanced async performance")
    except ImportError:
        pass
    
    asyncio.run(main())
