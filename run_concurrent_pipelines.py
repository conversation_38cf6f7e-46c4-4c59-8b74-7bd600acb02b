"""
Concurrent Pipeline Runner
Runs both the LangGraph async video pipeline and PydanticAI async video pipeline concurrently
"""

import asyncio
import threading
import time
from typing import Dict, Any

def run_main_langgraph_pipeline():
    """Run the main.py LangGraph pipeline in a separate thread"""
    print("[THREAD] 🔧 Starting LangGraph pipeline (main.py)...")
    try:
        # Import and run main.py
        import main
        main.main()
        print("[THREAD] ✅ LangGraph pipeline completed")
    except Exception as e:
        print(f"[THREAD] ❌ LangGraph pipeline error: {e}")

async def run_pydantic_pipeline():
    """Run the PydanticAI async video pipeline"""
    print("[ASYNC] 🎬 Starting PydanticAI async pipeline...")
    try:
        # Import and run pydantic_prayer.py
        import pydantic_prayer
        await pydantic_prayer.main()
        print("[ASYNC] ✅ PydanticAI pipeline completed")
    except Exception as e:
        print(f"[ASYNC] ❌ PydanticAI pipeline error: {e}")

async def main():
    """Main concurrent execution"""
    print("\n" + "=" * 80)
    print("   🚀 CONCURRENT PRAYER PIPELINE EXECUTION 🚀")
    print("=" * 80)
    print("\nRunning both pipelines simultaneously:")
    print("  📊 LangGraph Pipeline: Enhanced with async video nodes")  
    print("  🎬 PydanticAI Pipeline: Pure async video generation")
    print("\n" + "=" * 80)

    # Start LangGraph pipeline in a separate thread
    langgraph_thread = threading.Thread(
        target=run_main_langgraph_pipeline,
        name="LangGraph-Thread",
        daemon=False
    )
    
    print("[INFO] 🔄 Starting concurrent execution...")
    start_time = time.time()
    
    # Start both pipelines
    langgraph_thread.start()
    
    # Run PydanticAI pipeline in async
    await run_pydantic_pipeline()
    
    # Wait for LangGraph thread to complete
    print("[INFO] ⏳ Waiting for LangGraph pipeline to complete...")
    langgraph_thread.join()
    
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 80)
    print("         🎉 CONCURRENT EXECUTION COMPLETED 🎉")
    print("=" * 80)
    print(f"  ⏱️  Total execution time: {duration:.2f} seconds")
    print("  ✅ Both pipelines executed concurrently")
    print("  🎬 Video generation optimized through parallel processing")
    print("  📊 Enhanced prayer generation with multiple media types")
    print("=" * 80)

if __name__ == "__main__":
    # Check if we're on Windows for uvloop
    try:
        import uvloop
        uvloop.install()
        print("[INFO] 🚀 uvloop installed for enhanced async performance")
    except ImportError:
        pass  # Windows or uvloop not available
    
    asyncio.run(main())
