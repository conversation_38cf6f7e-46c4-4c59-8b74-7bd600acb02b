{"name": "replicate-mcp", "version": "1.1.0", "description": "MCP server for Replicate.com with video generation capabilities", "main": "src/index.js", "scripts": {"start": "node src/index.js", "build": "mkdir -p build && cp -r src/* build/", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.0.0", "replicate": "^0.25.0", "zod": "^3.22.4", "dotenv": "^16.0.0"}, "engines": {"node": ">=16.0.0"}, "author": "", "license": "MIT"}