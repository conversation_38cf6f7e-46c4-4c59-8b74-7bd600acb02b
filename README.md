# Prayer Application

A modular application for generating prayers from selected spiritual traditions, creating unified prayers, converting them to audio, and generating visual prayer representations.

## Features

- Generate individual prayers from user-selected spiritual traditions (from 14 available options) using Llama 4 Scout model
- Combine prayers into a unified, comprehensive prayer using Llama 4 Maverick model
- Integrate quantum prayer concepts and divine names from various traditions
- Convert prayers to audio with emotional cues and stage directions
- Generate beautiful image prayers using Google Gemini Flash and fal.ai API
- Streamlined TTS pipeline using OpenAI's latest models
- Support for GPT-4o-mini-audio-preview for enhanced narration

## Project Structure

The project has been refactored into a modular architecture:

```
project/
├── main.py                    # Main entry point
├── graph_definition.py        # LangGraph workflow definition
├── models/                    # State models
│   ├── __init__.py
│   └── prayer_state.py        # PrayerState definition
├── nodes/                     # LangGraph workflow nodes
│   ├── __init__.py
│   ├── research_nodes.py      # Faith-specific research
│   ├── prayer_generation.py   # Individual prayer generation
│   ├── prayer_aggregation.py  # Prayer aggregation and unification
│   ├── gemini_image_prayer.py # Image prayer generation using Gemini and Replicate
│   └── tts_nodes.py           # TTS processing nodes
├── services/                  # External API services
│   ├── __init__.py
│   ├── openai_prayer_agent.py # Llama API for individual prayer generation
│   ├── gemini_service.py      # Google Gemini API for unified prayers
│   ├── openai_service.py      # OpenAI API for TTS optimization
│   └── mcp-servers/           # Model Context Protocol servers
│       ├── brave-groq-research/  # Faith research MCP server
│       └── fal-prayer-image/     # Visual prayer generation MCP server
├── utils/                     # Utility functions
│   ├── __init__.py
│   └── file_utils.py          # Common file operations
└── tts_service.js             # Node.js TTS service
```

## Requirements

- Python 3.8+
- Node.js 14.0+
- API Keys:
  - LLAMA_API_KEY (for individual prayer generation using Llama 4 Scout and Maverick models)
  - OPENAI_API_KEY (for TTS optimization and audio generation)
  - GEMINI_API_KEY (for unified prayer generation and image prayer scenes)
  - FAL_API_KEY (for image prayer generation)
  - GROQ_API_KEY (optional - only needed for brave-groq-research MCP server)
  - ELEVENLABS_API_KEY (optional, for high-quality TTS)
  - AZURE_API_KEY (optional, for Azure TTS)

## Installation

1. Clone the repository
2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Install Node.js dependencies:
   ```
   npm install
   ```
4. Set up your API keys in a `.env` file:
   ```
   LLAMA_API_KEY=your_llama_api_key
   OPENAI_API_KEY=your_openai_api_key
   GEMINI_API_KEY=your_gemini_api_key
   FAL_API_KEY=your_fal_api_key
   GROQ_API_KEY=your_groq_api_key
   ELEVENLABS_API_KEY=your_elevenlabs_api_key
   AZURE_API_KEY=your_azure_api_key
   AZURE_REGION=eastus
   ```

## Usage

Run the application:

```
python main.py
```

The application will:

1. Ask who to pray for and what spiritual wisdom to integrate
2. Ask which spiritual traditions you want to include (select from 14 options)
3. Ask if you want to generate image prayers
4. Research faith-specific terms and divine names for selected traditions
5. Generate individual prayers for each selected tradition
6. Create a unified prayer combining elements from all selected traditions
7. In parallel:
   - If requested, convert the prayer to audio with TTS using GPT-4o-mini-audio-preview
   - If requested, generate image prayers using Google Gemini 2.5 Flash and fal.ai
8. Display a summary of all generated outputs

## Image Prayer Generation

The application can now generate beautiful image prayers based on the unified prayer text:

1. Google Gemini 2.5 Flash extracts powerful, visual scenes from the unified prayer
2. Gemini creates detailed image prompts for each scene
3. fal.ai API generates high-quality images in 16:9 aspect ratio
4. Images are saved in the Saved_Prayers/Images directory

To test just the image prayer generation functionality:

```
python test_image_prayer.py
```

## TTS Options

The application now uses OpenAI exclusively for TTS with two main options:

1. **Standard TTS** - Using OpenAI's TTS-1 model with various voice options
2. **Enhanced Narration** - Using GPT-4o-mini-audio-preview for more natural, expressive narration

Voice options include:
- Alloy (neutral)
- Echo (male)
- Fable (male)
- Onyx (male)
- Nova (female)
- Shimmer (female)
- Sage (neutral)

## Stage Directions for TTS

The TTS optimization process adds stage directions in parentheses to guide the voice:

```
(whisper) This is spoken quietly.
(emphasis) This part is emphasized.
(gentle tone) This is spoken gently.
(pause) A brief pause occurs here.
```

## Quantum Prayer Framework

The application now incorporates a quantum prayer framework that integrates scientific understanding with spiritual practice:

1. **Quantum Entanglement**: Prayer establishes a connection between consciousness and target
2. **Divine Names**: Names from various traditions carry specific vibrational frequencies that amplify intentional effects
3. **Declarations**: "I AM" statements connect to fundamental creative consciousness
4. **Coherence**: Collective prayer creates coherent fields with magnified effects
5. **Quantum Mechanisms**: Prayer engages actual physics mechanisms at quantum levels

### Divine Names Integration

The application includes divine names from various traditions, categorized as:

- **Creator Names**: YHWH/Yahweh, Elohim, Allah, Brahman, Ahura Mazda, Great Spirit/Wakan Tanka, Waheguru
- **Divine Healing Names**: Jehovah Rapha, Ash-Shafi, Dhanvantari, Bhaisajyaguru, Green Tara
- **Divine Compassion Names**: Ar-Rahman, El Rachum, Avalokiteshvara/Guanyin, Karuna
- **Divine Protection Names**: Jehovah Nissi, Al-Hafiz, Durga, Mahakala
- **Divine Peace Names**: Jehovah Shalom, As-Salam, Shanti, Buddha
- **Divine Wisdom Names**: Hakadosh, Al-Hakim, Saraswati, Manjushri, Sophia

## Adding New Spiritual Traditions

To add a new spiritual tradition:

1. Add the tradition name to the `SPIRITUAL_MOVEMENTS` list in `main.py`
2. The research agent will automatically gather divine names and sacred terms for the new tradition
3. The new tradition will appear in the selection menu for users to choose

## Models and Endpoints

The application uses the following AI models and endpoints:

### Prayer Generation
- **Individual Prayers**: Llama-4-Scout-17B-16E-Instruct-FP8 via Llama API (https://api.llama.com/compat/v1)
- **Unified Prayer**: Llama-4-Maverick-17B-128E-Instruct-FP8 via Llama API (https://api.llama.com/compat/v1)

### Image Generation
- **Scene Extraction & Prompt Creation**: gemini-2.5-flash-preview-04-17 via Google Gemini API
- **Image Generation**: fal.ai API for high-quality 16:9 images

### Audio Generation
- **TTS Optimization**: OpenAI GPT-4o-mini for converting prayer text to TTS-friendly format
- **Audio Synthesis**: GPT-4o-mini-audio-preview for enhanced narration with emotional cues

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
