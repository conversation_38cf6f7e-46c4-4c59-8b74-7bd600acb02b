"""
Media Aggregation Node
Combines all generated media (images, audio, video) into a unified result
"""

import os
import logging
from typing import Dict, List, Any
from models.prayer_state import PrayerState

logger = logging.getLogger(__name__)

def media_aggregation_node(state: PrayerState) -> PrayerState:
    """
    Aggregate all generated media into a unified result structure
    """
    logger.info("Starting media aggregation")
    
    try:
        # Initialize media collection
        media_collection = {
            "prayer_text": state.get("unified_prayer_markdown", ""),
            "images": [],
            "audio": None,
            "videos": []
        }
        
        # Add image prayers if available
        if state.get("image_prayer_filepaths"):
            for img_path in state.get("image_prayer_filepaths", []):
                if os.path.exists(img_path):
                    media_collection["images"].append({
                        "path": img_path,
                        "type": "prayer_image"
                    })
        
        # Add audio if available
        if state.get("audio_filepath") and os.path.exists(state.get("audio_filepath")):
            media_collection["audio"] = {
                "path": state.get("audio_filepath"),
                "type": "prayer_audio"
            }
        
        # Add videos if available - prioritize local paths over URLs
        if state.get("generated_videos"):
            for video in state.get("generated_videos", []):
                video_entry = {
                    "scene": video.get("scene", "Unknown scene"),
                    "prompt": video.get("prompt", ""),
                    "model": video.get("model", "Unknown model")
                }
                
                # Prioritize local path if available
                if video.get("local_path") and os.path.exists(video.get("local_path")):
                    video_entry["path"] = video.get("local_path")
                    video_entry["url"] = video.get("video_url")  # Keep URL as backup
                elif video.get("video_url"):
                    video_entry["url"] = video.get("video_url")
                    video_entry["path"] = None
                    logger.warning(f"Video for scene '{video.get('scene')}' only has temporary URL, no local path")
                
                media_collection["videos"].append(video_entry)
        
        # Add single video if available
        if state.get("generated_video_local_path") and os.path.exists(state.get("generated_video_local_path")):
            # Check if this video is already in the collection
            if not any(v.get("path") == state.get("generated_video_local_path") for v in media_collection["videos"]):
                media_collection["videos"].append({
                    "path": state.get("generated_video_local_path"),
                    "url": state.get("generated_video"),
                    "scene": "Main prayer video",
                    "type": "prayer_video"
                })
        elif state.get("generated_video") and not media_collection["videos"]:
            # Only add URL if we don't have any videos with local paths
            media_collection["videos"].append({
                "url": state.get("generated_video"),
                "path": None,
                "scene": "Main prayer video",
                "type": "prayer_video"
            })
            logger.warning("Main prayer video only has temporary URL, no local path")
        
        # Update state with aggregated media
        state["media_collection"] = media_collection
        
        # Create summary of available media
        media_summary = []
        if media_collection["prayer_text"]:
            media_summary.append("Prayer text")
        if media_collection["images"]:
            media_summary.append(f"{len(media_collection['images'])} prayer images")
        if media_collection["audio"]:
            media_summary.append("Audio narration")
        if media_collection["videos"]:
            media_summary.append(f"{len(media_collection['videos'])} prayer videos")
        
        state["media_summary"] = ", ".join(media_summary)
        logger.info(f"Media aggregation complete: {state['media_summary']}")
        
    except Exception as e:
        logger.error(f"Error in media aggregation: {str(e)}")
        state.setdefault('execution_logs', []).append(f"Media aggregation error: {str(e)}")
    
    return state