import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';
import '../providers/prayer_provider.dart';
import '../widgets/faith_selection_grid.dart';

class CreatePrayerScreen extends ConsumerStatefulWidget {
  const CreatePrayerScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<CreatePrayerScreen> createState() => _CreatePrayerScreenState();
}

class _CreatePrayerScreenState extends ConsumerState<CreatePrayerScreen> {
  final _prayForController = TextEditingController();
  final _wisdomController = TextEditingController();
  String _selectedVoiceId = AppConstants.speakerVoices.first['id'] as String;
  bool _generateImages = true;
  bool _isLoading = false;
  
  @override
  void dispose() {
    _prayForController.dispose();
    _wisdomController.dispose();
    super.dispose();
  }
  
  // Generate a prayer
  Future<void> _generatePrayer() async {
    // Validate inputs
    if (_prayForController.text.trim().isEmpty) {
      _showError('Please enter what you want to pray for');
      return;
    }
    
    if (_wisdomController.text.trim().isEmpty) {
      _showError('Please enter what spiritual wisdom to integrate');
      return;
    }
    
    // Get selected faiths
    final selectedFaiths = ref.read(selectedFaithsProvider);
    if (selectedFaiths.isEmpty) {
      _showError('Please select at least one spiritual tradition');
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Start prayer generation
      await ref.read(prayerStateProvider.notifier).startPrayerGeneration(
        prayFor: _prayForController.text.trim(),
        wisdomToIntegrate: _wisdomController.text.trim(),
        selectedFaiths: selectedFaiths,
        generateImages: _generateImages,
        voiceId: _selectedVoiceId,
      );
      
      if (mounted) {
        // Get run ID and navigate to prayer console
        final runId = ref.read(prayerStateProvider.notifier).currentRunId;
        if (runId != null) {
          context.push('${AppConstants.prayerConsoleRoute}/$runId');
        } else {
          _showError('Failed to start prayer generation');
        }
      }
    } catch (e) {
      if (mounted) {
        _showError('Error: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  // Show error message
  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final selectedFaiths = ref.watch(selectedFaithsProvider);
    
    // Generate gradient based on selected faiths
    final gradientColors = selectedFaiths.isEmpty
        ? [theme.colorScheme.primary, theme.colorScheme.tertiary]
        : selectedFaiths
            .take(2)
            .map((faith) => 
                AppConstants.faithColors[faith] ?? theme.colorScheme.primary)
            .toList();
    
    if (gradientColors.length == 1) {
      gradientColors.add(gradientColors[0].withOpacity(0.7));
    }
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Prayer'),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: gradientColors,
            ),
          ),
        ),
      ),
      body: Container(
        // Background pattern - subtle spiritual symbols pattern
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('assets/images/pattern_light.png'),
            fit: BoxFit.cover,
            opacity: 0.05,
          ),
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Introduction card
                Card(
                  elevation: 2,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Create Your Spiritual Prayer',
                          style: theme.textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Your prayer will combine wisdom from multiple spiritual traditions into a cohesive, meaningful experience.',
                          style: theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                
                // Prayer focus
                Text(
                  'Prayer Focus',
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'What or who would you like to pray for?',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _prayForController,
                  decoration: InputDecoration(
                    hintText: 'E.g., healing for a friend, guidance in difficult times',
                    prefixIcon: const Icon(Icons.favorite),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 24),
                
                // Spiritual wisdom
                Text(
                  'Spiritual Wisdom',
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'What spiritual wisdom would you like to incorporate?',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _wisdomController,
                  decoration: InputDecoration(
                    hintText: 'E.g., compassion, peace, forgiveness, gratitude',
                    prefixIcon: const Icon(Icons.lightbulb),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 2,
                ),
                const SizedBox(height: 24),
                
                // Faith traditions
                Text(
                  'Spiritual Traditions',
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Select the traditions to include in your prayer',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 4),
                Text(
                  '${selectedFaiths.length} of ${AppConstants.faithTraditions.length} selected',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // Faith selection grid
                const FaithSelectionGrid(),
                const SizedBox(height: 24),
                
                // Voice selection
                Text(
                  'Voice Selection',
                  style: theme.textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  'Choose a voice for the audio version of your prayer',
                  style: theme.textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),
                
                // Voice selection cards
                SizedBox(
                  height: 130,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: AppConstants.speakerVoices.length,
                    itemBuilder: (context, index) {
                      final voice = AppConstants.speakerVoices[index];
                      final voiceId = voice['id'] as String;
                      final voiceName = voice['name'] as String;
                      final voiceGender = voice['gender'] as String;
                      final isSelected = _selectedVoiceId == voiceId;
                      
                      return Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: _buildVoiceCard(
                          context,
                          name: voiceName,
                          gender: voiceGender,
                          isSelected: isSelected,
                          onTap: () {
                            setState(() {
                              _selectedVoiceId = voiceId;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(height: 24),
                
                // Image toggle
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Visual Prayer',
                                style: theme.textTheme.titleLarge,
                              ),
                              Text(
                                'Generate visual representations of your prayer',
                                style: theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        Switch(
                          value: _generateImages,
                          onChanged: (value) {
                            setState(() {
                              _generateImages = value;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 32),
                
                // Create button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _generatePrayer,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator()
                        : const Text(
                            'Create Prayer',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // Voice selection card
  Widget _buildVoiceCard(
    BuildContext context, {
    required String name,
    required String gender,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 100,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer
              : theme.colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: isSelected
              ? Border.all(color: theme.colorScheme.primary, width: 2)
              : null,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant.withOpacity(0.2),
              ),
              child: Icon(
                gender == 'female'
                    ? Icons.record_voice_over
                    : gender == 'male'
                        ? Icons.mic
                        : Icons.hearing,
                color: isSelected
                    ? theme.colorScheme.onPrimary
                    : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              name,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              gender.capitalize(),
              style: theme.textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? theme.colorScheme.primary.withOpacity(0.8)
                    : theme.colorScheme.onSurface.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${substring(1)}";
  }
}
