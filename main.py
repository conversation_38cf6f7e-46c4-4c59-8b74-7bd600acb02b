"""
Prayer Application Main Module
Entry point for the prayer generation and TTS application.
"""

import os
import re
import requests
import json
import datetime
from pydantic import BaseModel, ValidationError, Field
from typing import Dict, List, Optional
import dotenv

# Load environment variables
dotenv.load_dotenv()

# Check for required environment variables
required_env_vars = ["XAI_API_KEY", "GROQ_API_KEY", "BRAVE_API_KEY"]
missing_env_vars = [var for var in required_env_vars if not os.environ.get(var)]
if missing_env_vars:
    print(f"[WARNING] Missing environment variables: {', '.join(missing_env_vars)}")
    print("[INFO] Creating .env file template with required variables...")
    
    # Create a template .env file if it doesn't exist
    if not os.path.exists(".env"):
        with open(".env", "w") as f:
            f.write("# Required environment variables for Prayer Application\n")
            f.write("XAI_API_KEY=your_xai_api_key_here\n")
            f.write("GROQ_API_KEY=your_groq_api_key_here\n")
            f.write("BRAVE_API_KEY=your_brave_search_api_key_here\n")
        print("[INFO] Created .env template file. Please fill in your API keys.")

# Import graph definition
# Use the async graph definition for video generation capabilities
try:
    from graph_definition_async import create_prayer_workflow
    print("[INFO] Using async graph definition with video generation capabilities.")
except ImportError:
    try:
        from graph_definition_new import create_prayer_workflow
        print("[INFO] Using new graph definition with sequential processing (no video).")
    except ImportError:
        from graph_definition import create_prayer_workflow
        print("[WARNING] Using original graph definition.")

# Import models
from models.prayer_state import PrayerState

# Import utility functions
from utils.file_utils import create_run_directory

# Pydantic model for input validation
class PrayerInput(BaseModel):
    pray_for: str = Field(..., min_length=1)
    wisdom_to_integrate: str = Field(..., min_length=1)
    selected_faiths: List[str] = Field(default=[])
    generate_images: bool = Field(default=False)
    generate_video: bool = Field(default=False)

# Declare spiritual movements
SPIRITUAL_MOVEMENTS = [
    'Christianity',
    'Islam',
    'Hinduism',
    'Buddhism',
    'Sikhism',
    'Judaism',
    'Bahá\'í Faith',
    'Confucianism',
    'Jainism',
    'Shinto',
    'Taoism',
    'Zoroastrianism',
    'Cherokee Spirituality',
    'Wicca'
]

def get_user_input() -> PrayerInput:
    """
    Get and validate user input for prayer generation.

    Returns:
        Validated PrayerInput object
    """
    while True:
        try:
            pray_for_input = input("Who or what should we pray for? ")
            wisdom_input = input("What spiritual wisdom should we integrate? ")

            # Display available spiritual traditions
            print("\nAvailable spiritual traditions:")
            for i, faith in enumerate(SPIRITUAL_MOVEMENTS, 1):
                print(f"{i}. {faith}")

            # Get faith selections with improved validation
            selected_faiths = []
            while not selected_faiths:
                faith_input = input("\nEnter the numbers of the faiths to include (comma-separated, or 'all' for all): ")

                if faith_input.lower().strip() == 'all':
                    print("[INFO] You selected ALL spiritual traditions.")
                    selected_faiths = SPIRITUAL_MOVEMENTS.copy()
                else:
                    try:
                        # Parse the comma-separated numbers
                        faith_indices = [int(idx.strip()) for idx in faith_input.split(',') if idx.strip()]
                        # Convert to faith names, handling out-of-range indices
                        selected_faiths = [SPIRITUAL_MOVEMENTS[idx-1] for idx in faith_indices
                                        if 1 <= idx <= len(SPIRITUAL_MOVEMENTS)]

                        if not selected_faiths:
                            print("[WARNING] No valid faith selections found. Please try again.")
                        else:
                            print(f"[INFO] You selected: {', '.join(selected_faiths)}")
                    except ValueError:
                        print("[WARNING] Invalid input format. Please use numbers separated by commas.")

            # Ask about image generation
            generate_images = False
            image_input = input("\nWould you like to generate image prayers? (yes/no): ")
            if image_input.lower().strip() in ["yes", "y", "true", "1"]:
                generate_images = True
                print("[INFO] Image prayer generation enabled.")
            else:
                print("[INFO] Image prayer generation disabled.")

            # Ask about video generation
            generate_video = False
            video_input = input("\nWould you like to generate prayer videos? (yes/no): ")
            if video_input.lower().strip() in ["yes", "y", "true", "1"]:
                generate_video = True
                print("[INFO] Video prayer generation enabled.")
            else:
                print("[INFO] Video prayer generation disabled.")

            # Validate with pydantic model
            validated_input = PrayerInput(
                pray_for=pray_for_input,
                wisdom_to_integrate=wisdom_input,
                selected_faiths=selected_faiths,
                generate_images=generate_images,
                generate_video=generate_video
            )
            return validated_input

        except ValidationError as e:
            print(f"\n[VALIDATION ERROR] Input is invalid:\n{e}\nPlease try again.")
        except Exception as e:
            print(f"\nAn unexpected error occurred during input: {e}")
            raise

def initialize_state(prayer_input: PrayerInput, run_output_dir: str) -> PrayerState:
    """
    Initialize the workflow state with user input.

    Args:
        prayer_input: Validated user input
        run_output_dir: Directory for this run's outputs

    Returns:
        Initialized PrayerState
    """
    pray_for = prayer_input.pray_for
    wisdom_to_integrate = prayer_input.wisdom_to_integrate
    selected_faiths = prayer_input.selected_faiths

    prayer_focus_theme = f"{pray_for}, integrating wisdom about {wisdom_to_integrate}"

    # Initialize only with the selected faiths
    initial_state = PrayerState(
        # Core prayer information
        prayer_focus_theme=prayer_focus_theme,
        search_context="",  # Will be populated by research node
        religious_terms={},  # Will be populated by research node
        faith_research={},  # Will be populated by research node

        # Workflow state tracking
        spiritual_movements_to_process=selected_faiths.copy(),  # Only process selected faiths
        individual_prayers={},
        current_movement=None,
        run_output_dir=run_output_dir,
        error_message=None,

        # Unified prayer fields
        combined_prayer_text=None,
        unified_prayer_markdown=None,
        unified_prayer_filename=None,
        unified_prayer_filepath=None,

        # TTS fields
        audio_conversion_requested=True,  # Always request audio conversion
        tts_confirmed=True,  # Auto-confirm TTS to ensure it runs
        tts_provider=None,
        tts_voice_id=None,
        tts_options=None,
        verbal_prayer_text=None,
        verbal_prayer_filepath=None,
        audio_filepath=None,

        # Image prayer fields
        image_prayer_requested=prayer_input.generate_images,
        image_prayer_generated=False,
        image_prayer_filepaths=None,

        # Video prayer fields
        generate_video=prayer_input.generate_video,
        sacred_scenes=None,
        generated_videos=None,
        generated_video=None,
        
        # Ensure generated_images field is also initialized for consistency
        generated_images=None,
        generated_audio=None
    )

    return initial_state

def display_execution_summary(final_state: Dict) -> None:
    """
    Display a summary of the workflow execution.

    Args:
        final_state: Final state of the workflow
    """
    print("\n" + "=" * 50)
    print("             EXECUTION SUMMARY             ")
    print("=" * 50)

    # Check for errors
    error_msg = final_state.get('error_message')
    if error_msg:
        print(f"\n⚠️ [ERROR] Workflow ended with error: {error_msg}")
        print("-" * 50)
        # Don't return early, show whatever was completed

    # Count processed faiths
    processed_faiths = final_state.get('individual_prayers', {})
    successful_individual_prayers = {k: v for k, v in processed_faiths.items() if v is not None and v != ""}
    failed_individual_prayers = {k: v for k, v in processed_faiths.items() if v is None or v == ""}

    print(f"\n📝 INDIVIDUAL PRAYERS")
    if successful_individual_prayers:
        print(f"Successfully generated prayers for {len(successful_individual_prayers)} faiths:")
        for faith in successful_individual_prayers:
            print(f"  ✅ {faith}")
    else:
        print("No individual prayers were successfully generated.")

    if failed_individual_prayers:
        print(f"Failed to generate prayers for {len(failed_individual_prayers)} faiths:")
        for faith in failed_individual_prayers:
            print(f"  ❌ {faith}")

    # Show unified prayer status
    print(f"\n🙏 UNIFIED PRAYER")
    unified_prayer_filepath = final_state.get('unified_prayer_filepath')
    if unified_prayer_filepath:
        print(f"Unified prayer saved to: {unified_prayer_filepath}")
    else:
        print("No unified prayer was generated")

    # Show audio status
    print(f"\n🔊 AUDIO PRAYER")
    audio_requested = final_state.get('audio_conversion_requested', False)
    generated_audio_file = final_state.get('generated_audio') # Use 'generated_audio'

    if generated_audio_file:
        print(f"Audio file generated: {generated_audio_file}")
        print(f"To listen to the prayer, open the audio file in your media player.")
    elif audio_requested:
        print("Audio conversion was requested but no audio file was generated.")
    else:
        print("Audio conversion was not requested.")

    # Show image prayer status
    print(f"\n🖼️ IMAGE PRAYERS")
    image_requested = final_state.get('image_prayer_requested', False)
    generated_image_files = final_state.get('generated_images', []) # Use 'generated_images'

    if generated_image_files:
        print(f"Generated {len(generated_image_files)} image prayers:")
        for i, image_path in enumerate(generated_image_files):
            print(f"  ✅ Image {i+1}: {image_path}")
        print(f"To view the images, open the image files in your image viewer.")
    elif image_requested:
        print("Image prayers were requested but none were generated.")
    else:
        print("Image prayer generation was not requested.")

    # Show video prayer status
    print(f"\n🎥 VIDEO PRAYERS")
    video_requested = final_state.get('generate_video', False)
    generated_video_file = final_state.get('generated_video')

    if generated_video_file:
        print(f"Video generated: {generated_video_file}")
        print(f"To view the video, open the video file or URL.")
    elif video_requested:
        print("Video generation was requested but no video was generated.")
    else:
        print("Video generation was not requested.")

    print("\n" + "=" * 50)

def main():
    """Main entry point for the application."""

    # Display welcome message
    print("\n====================================================")
    print("       🙏  SPIRITUAL PRAYER GENERATION SYSTEM  🙏")
    print("====================================================")
    print("\nThis application generates prayers from multiple spiritual traditions,")
    print("combines them into a unified prayer, converts to audio, and creates image prayers.")

    try:
        # Get user input
        prayer_input = get_user_input()

        pray_for = prayer_input.pray_for
        wisdom_to_integrate = prayer_input.wisdom_to_integrate
        selected_faiths = prayer_input.selected_faiths

        prayer_focus_theme = f"{pray_for}, integrating wisdom about {wisdom_to_integrate}"

        print(f"\nInitiating prayer generation focused on: '{prayer_focus_theme}'")
        print(f"Generating prayers for ONLY these traditions: {', '.join(selected_faiths)}")
        if prayer_input.generate_images:
            print("[INFO] Image prayer generation is enabled.")

        # Create run-specific output directory
        try:
            run_output_dir = create_run_directory()
            print(f"[INFO] Created run directory: {run_output_dir}")
        except Exception as e:
            print(f"[ERROR] Failed to create run directory: {e}")
            return

        # Initialize state
        initial_state = initialize_state(prayer_input, run_output_dir)

        # Create and execute the workflow
        print("\n--- Invoking LangGraph Workflow ---")
        import asyncio # Import asyncio for running async main
        app = create_prayer_workflow()
        final_state = None

        try:
            # Add debug info before running the pipeline
            print("[DEBUG] Initial state contents:")
            print(f"  - prayer_focus_theme: {initial_state.get('prayer_focus_theme')}")
            print(f"  - spiritual_movements: {len(initial_state.get('spiritual_movements_to_process', []))} movements")
            print(f"  - run_output_dir: {initial_state.get('run_output_dir')}")
            
            # Use astream for asynchronous execution
            async def run_workflow():
                nonlocal final_state # Declare final_state as nonlocal to modify it
                async for event in app.astream(initial_state, {"recursion_limit": 60}):
                    if "__end__" not in event:
                        latest_state_key = list(event.keys())[0]
                        print(f"[INFO] Processing: {latest_state_key}")
                        final_state = event[latest_state_key]
                return final_state # Return the final state from the async function

            final_state = asyncio.run(run_workflow()) # Run the async workflow

        except Exception as e:
            print(f"\n[ERROR] An error occurred during graph execution: {e}")
            import traceback
            traceback.print_exc()
            if final_state is None:
                final_state = initial_state
                if isinstance(final_state, dict):
                    final_state["error_message"] = str(e)
            # Ensure final_state is a dict for display_execution_summary
            if not isinstance(final_state, dict):
                final_state = final_state.dict() # Convert Pydantic object to dict if needed

        print("\n--- LangGraph Workflow Complete ---")

        # Display summary
        if final_state and isinstance(final_state, dict):
            display_execution_summary(final_state)
        else:
            print("\n[ERROR] Failed to get final state from graph execution.")

    except KeyboardInterrupt:
        print("\n\nPrayer generation canceled by user.")
    except Exception as e:
        print(f"\n[ERROR] An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()

    print("\n====================================================")
    print("                  Process Complete                  ")
    print("====================================================")

if __name__ == "__main__":
    main()
