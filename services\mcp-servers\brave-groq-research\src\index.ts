#!/usr/bin/env node
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ErrorCode,
  ListToolsRequestSchema,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';
import axios from 'axios';
import { Groq } from 'groq-sdk';

// Environment variables
const GROQ_API_KEY = process.env.GROQ_API_KEY;
const BRAVE_API_KEY = process.env.BRAVE_API_KEY;

if (!GROQ_API_KEY) {
  throw new Error('GROQ_API_KEY environment variable is required');
}

// API endpoints and models
const BRAVE_SEARCH_URL = "https://api.search.brave.com/res/v1/web/search";
const GROQ_MODELS = {
  default: "llama-3.3-70b-versatile",
  mixtral: "qwen-qwq-32b",
  fast: "llama-3.2-3b-preview"
};

class BraveGroqResearchServer {
  private server: Server;
  private groqClient: Groq;
  private model: string;

  constructor() {
    this.server = new Server(
      {
        name: 'brave-groq-research',
        version: '0.1.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.groqClient = new Groq({ apiKey: GROQ_API_KEY });
    this.model = GROQ_MODELS.default;

    this.setupToolHandlers();
    
    // Error handling
    this.server.onerror = (error) => console.error('[MCP Error]', error);
    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: [
        {
          name: 'faith_research',
          description: 'Research specific faith traditions using Groq and Brave Search',
          inputSchema: {
            type: 'object',
            properties: {
              faith: {
                type: 'string',
                description: 'Name of the faith tradition'
              },
              prayer_focus: {
                type: 'string',
                description: 'Focus of the prayer'
              },
              wisdom_focus: {
                type: 'string',
                description: 'Spiritual wisdom to integrate'
              },
              model_type: {
                type: 'string',
                enum: ['default', 'mixtral', 'fast'],
                description: 'Groq model to use'
              }
            },
            required: ['faith', 'prayer_focus', 'wisdom_focus']
          }
        },
        {
          name: 'general_research',
          description: 'Get general spiritual context using Groq and Brave Search',
          inputSchema: {
            type: 'object',
            properties: {
              prayer_focus: {
                type: 'string',
                description: 'Focus of the prayer'
              },
              wisdom_focus: {
                type: 'string',
                description: 'Spiritual wisdom to integrate'
              },
              model_type: {
                type: 'string',
                enum: ['default', 'mixtral', 'fast'],
                description: 'Groq model to use'
              }
            },
            required: ['prayer_focus', 'wisdom_focus']
          }
        }
      ]
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      switch (request.params.name) {
        case 'faith_research':
          return this.handleFaithResearch(request.params.arguments);
        case 'general_research':
          return this.handleGeneralResearch(request.params.arguments);
        default:
          throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
      }
    });
  }

  private async searchBrave(query: string, count: number = 5): Promise<any[]> {
    if (!BRAVE_API_KEY) {
      console.warn("[WARNING] Brave Search API key not available. Skipping web search.");
      return [];
    }

    try {
      const response = await axios.get(BRAVE_SEARCH_URL, {
        headers: {
          'Accept': 'application/json',
          'X-Subscription-Token': BRAVE_API_KEY
        },
        params: {
          q: query,
          count: count,
          search_lang: 'en'
        }
      });

      return response.data.web?.results?.map((result: any) => ({
        title: result.title,
        description: result.description,
        url: result.url
      })) || [];

    } catch (error) {
      console.error('[ERROR] Brave Search API error:', error);
      return [];
    }
  }

  private async makeGroqRequest(systemPrompt: string, userPrompt: string): Promise<string | null> {
    try {
      const completion = await this.groqClient.chat.completions.create({
        model: this.model,
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        temperature: 0.7,
        max_tokens: 4096,
        top_p: 1,
        stream: false
      });

      return completion.choices[0]?.message?.content?.trim() || null;

    } catch (error) {
      console.error('[ERROR] Groq API error:', error);
      return null;
    }
  }

  private async handleFaithResearch(args: any) {
    if (args.model_type && (args.model_type in GROQ_MODELS)) {
      this.model = GROQ_MODELS[args.model_type as keyof typeof GROQ_MODELS];
    }

    // Gather web search results
    const searchQuery = `${args.faith} ${args.prayer_focus} prayer ${args.wisdom_focus} religious tradition`;
    const webResults = await this.searchBrave(searchQuery);
    
    const webContext = webResults.map(result => 
      `Source: ${result.url}\nTitle: ${result.title}\nDescription: ${result.description}`
    ).join('\n\n');

    const systemPrompt = `You are an expert scholar and practitioner of ${args.faith} with deep knowledge of its texts, traditions, prayers, and spiritual concepts. Provide authentic, respectful, and accurate information about ${args.faith} that would help create a genuine prayer in this tradition.\n\nAdditional context from reliable sources:\n${webContext}`;

    const userPrompt = `For a prayer focused on '${args.prayer_focus}' that integrates wisdom about '${args.wisdom_focus}', provide detailed information specific to ${args.faith} in this EXACT format:

DIVINE_NAMES:
- [name1]
- [name2]
- [name3]
- [name4]
- [name5]

SACRED_TERMS:
- [term1]
- [term2]
- [term3]
- [term4]
- [term5]

PRAYER_STRUCTURE:
[Brief description of how prayers are typically structured in this tradition, including any opening/closing formulas]

SCRIPTURAL_REFERENCES:
- [quote1]
- [quote2]
- [quote3]

CULTURAL_CONTEXT:
[Brief description of cultural/historical context relevant to this prayer theme in this tradition]

It is CRITICAL to follow this exact format with these exact section headings for proper parsing.`;

    const result = await this.makeGroqRequest(systemPrompt, userPrompt);
    
    return {
      content: [
        {
          type: 'text',
          text: result || 'Failed to retrieve research data.'
        }
      ]
    };
  }

  private async handleGeneralResearch(args: any) {
    if (args.model_type && (args.model_type in GROQ_MODELS)) {
      this.model = GROQ_MODELS[args.model_type as keyof typeof GROQ_MODELS];
    }

    const searchQuery = `${args.prayer_focus} ${args.wisdom_focus} spiritual wisdom interfaith prayer`;
    const webResults = await this.searchBrave(searchQuery);
    
    const webContext = webResults.map(result => 
      `Source: ${result.url}\nTitle: ${result.title}\nDescription: ${result.description}`
    ).join('\n\n');

    const systemPrompt = `You are a spiritual research assistant with deep knowledge of world religions and spiritual traditions. Provide comprehensive, nuanced context that draws from multiple faith traditions.\n\nAdditional context from reliable sources:\n${webContext}`;

    const userPrompt = `Research and provide deep spiritual context for a prayer focused on '${args.prayer_focus}' that integrates wisdom about '${args.wisdom_focus}'. Include universal spiritual principles, shared values across traditions, and meaningful insights that would enrich a multi-faith prayer. Focus on depth, authenticity, and spiritual wisdom that transcends individual traditions.`;

    const result = await this.makeGroqRequest(systemPrompt, userPrompt);
    
    return {
      content: [
        {
          type: 'text',
          text: result || 'Failed to retrieve research data.'
        }
      ]
    };
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Brave-Groq Research MCP server running on stdio');
  }
}

const server = new BraveGroqResearchServer();
server.run().catch(console.error);
