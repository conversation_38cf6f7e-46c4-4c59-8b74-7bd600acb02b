"""
File Utilities
Common file handling operations for the prayer application.
"""

import os
import datetime
import re
import aiohttp
import asyncio
import requests
from datetime import datetime
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

def sanitize_filename(filename):
    """
    Removes invalid characters and replaces spaces for filenames.

    Args:
        filename: Original filename to sanitize

    Returns:
        Sanitized filename
    """
    # Remove invalid characters (adjust based on OS if needed)
    sanitized = re.sub(r'[\\/*?:"<>|]', "", filename)
    # Replace spaces with underscores
    sanitized = sanitized.replace(" ", "_")
    # Limit length (optional)
    return sanitized[:100]  # Limit to 100 chars

def create_run_directory() -> str:
    """
    Create a timestamped directory for the current prayer run
    
    Returns:
        Path to the created directory
    """
    # Create base directory if it doesn't exist
    base_dir = "Saved_Prayers"
    os.makedirs(base_dir, exist_ok=True)
    
    # Create timestamped directory
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = os.path.join(base_dir, f"Run_{timestamp}")
    os.makedirs(run_dir, exist_ok=True)
    
    return run_dir

def save_individual_prayer(filepath, prayer_text, prayer_focus_theme, religion):
    """
    Save an individual prayer to a file.

    Args:
        filepath: Path where to save the prayer
        prayer_text: The prayer content
        prayer_focus_theme: Theme of the prayer
        religion: Religious tradition of the prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Prayer for: {prayer_focus_theme}\n")
            f.write(f"# Spiritual Movement: {religion}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(prayer_text)
        print(f"   [INFO] Prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save prayer to {filepath}: {e}")
        return False

def save_unified_prayer(filepath, prayer_text, prayer_focus_theme, model_name):
    """
    Save a unified prayer to a file.

    Args:
        filepath: Path where to save the prayer
        prayer_text: The prayer content
        prayer_focus_theme: Theme of the prayer
        model_name: Name of the model that generated the prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Unified Prayer\n")
            f.write(f"# Focus Theme: {prayer_focus_theme}\n")
            f.write(f"# Generated by: {model_name}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(prayer_text if prayer_text else "# Generation failed or produced empty content.")
        print(f"   [INFO] Unified prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save unified prayer to {filepath}: {e}")
        return False

def save_verbal_prayer(filepath, verbal_text, source_filepath, model_name):
    """
    Save a verbal prayer with TTS annotations to a file.

    Args:
        filepath: Path where to save the verbal prayer
        verbal_text: The verbal prayer content with TTS annotations
        source_filepath: Path to the source prayer file
        model_name: Name of the model that generated the verbal prayer

    Returns:
        True if successful, False otherwise
    """
    try:
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(f"# Verbal Prayer (TTS-Optimized)\n")
            f.write(f"# Generated from: {source_filepath}\n")
            f.write(f"# Model used: {model_name}\n")
            f.write(f"# Generated at: {datetime.datetime.now().isoformat()}\n\n")
            f.write(verbal_text)
        print(f"   [INFO] Verbal prayer saved to {filepath}")
        return True
    except Exception as e:
        print(f"   [ERROR] Failed to save verbal prayer to {filepath}: {e}")
        return False

def read_prayer_file(filepath, skip_headers=True):
    """
    Read a prayer file, optionally skipping header lines and cleaning up the content.

    Args:
        filepath: Path to the prayer file
        skip_headers: Whether to skip lines starting with "#"

    Returns:
        Prayer text content, cleaned and optimized
    """
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            if skip_headers:
                lines = f.readlines()
                # Skip header lines and filter out empty lines
                prayer_content = "".join(line for line in lines if not line.strip().startswith("#") and line.strip())

                # Clean up the content
                cleaned_content = prayer_content.strip()

                # Remove excessive newlines (more than 2 consecutive)
                cleaned_content = re.sub(r'\n{3,}', '\n\n', cleaned_content)

                # Remove any trailing whitespace or newlines
                cleaned_content = cleaned_content.rstrip()

                return cleaned_content
            else:
                return f.read().strip()
    except Exception as e:
        print(f"   [ERROR] Failed to read prayer file {filepath}: {e}")
        return ""

# Create directory structure for videos
def ensure_video_directory(prayer_id: Optional[str] = None) -> str:
    """
    Ensure the video directory exists and return its path
    
    Args:
        prayer_id: Optional prayer identifier for subdirectory
        
    Returns:
        Path to the video directory
    """
    base_dir = os.path.join("Saved_Prayers", "Videos")
    
    if prayer_id:
        video_dir = os.path.join(base_dir, prayer_id)
    else:
        video_dir = base_dir
        
    os.makedirs(video_dir, exist_ok=True)
    return video_dir

async def download_video_async(video_url: str, prayer_id: Optional[str] = None, 
                              scene_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Asynchronously download a video from a URL and save it locally
    
    Args:
        video_url: URL of the video to download
        prayer_id: Optional prayer identifier
        scene_name: Optional scene name for the video
        
    Returns:
        Dict with status, local_path, and error message if applicable
    """
    if not video_url:
        return {"success": False, "error": "No video URL provided", "local_path": None}
    
    try:
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename with context if available
        if scene_name:
            safe_scene_name = "".join(c if c.isalnum() else "_" for c in scene_name)
            filename = f"{timestamp}_{safe_scene_name[:30]}.mp4"
        else:
            filename = f"{timestamp}_prayer_video.mp4"
            
        # Ensure directory exists
        video_dir = ensure_video_directory(prayer_id)
        local_path = os.path.join(video_dir, filename)
        
        # Download the video
        async with aiohttp.ClientSession() as session:
            async with session.get(video_url) as response:
                if response.status != 200:
                    return {
                        "success": False, 
                        "error": f"Failed to download video: HTTP {response.status}",
                        "local_path": None
                    }
                
                with open(local_path, 'wb') as f:
                    while True:
                        chunk = await response.content.read(1024 * 1024)  # 1MB chunks
                        if not chunk:
                            break
                        f.write(chunk)
        
        logger.info(f"Video downloaded successfully to {local_path}")
        return {"success": True, "local_path": local_path, "original_url": video_url}
        
    except Exception as e:
        logger.error(f"Error downloading video: {str(e)}")
        return {"success": False, "error": str(e), "local_path": None}

def download_video_sync(video_url: str, prayer_id: Optional[str] = None,
                       scene_name: Optional[str] = None) -> Dict[str, Any]:
    """
    Synchronously download a video from a URL and save it locally
    
    Args:
        video_url: URL of the video to download
        prayer_id: Optional prayer identifier
        scene_name: Optional scene name for the video
        
    Returns:
        Dict with status, local_path, and error message if applicable
    """
    if not video_url:
        return {"success": False, "error": "No video URL provided", "local_path": None}
    
    try:
        # Create timestamp for unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename with context if available
        if scene_name:
            safe_scene_name = "".join(c if c.isalnum() else "_" for c in scene_name)
            filename = f"{timestamp}_{safe_scene_name[:30]}.mp4"
        else:
            filename = f"{timestamp}_prayer_video.mp4"
            
        # Ensure directory exists
        video_dir = ensure_video_directory(prayer_id)
        local_path = os.path.join(video_dir, filename)
        
        # Download the video
        response = requests.get(video_url, stream=True)
        if response.status_code != 200:
            return {
                "success": False, 
                "error": f"Failed to download video: HTTP {response.status_code}",
                "local_path": None
            }
            
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=1024 * 1024):  # 1MB chunks
                if chunk:
                    f.write(chunk)
        
        logger.info(f"Video downloaded successfully to {local_path}")
        return {"success": True, "local_path": local_path, "original_url": video_url}
        
    except Exception as e:
        logger.error(f"Error downloading video: {str(e)}")
        return {"success": False, "error": str(e), "local_path": None}
