/**
 * Grok Prayer Image MCP Server
 * 
 * This MCP server provides a visual prayer generation service that combines
 * spiritual analysis using XAI's language model and image generation
 * with Grok 2 Vision for spiritual interpretation of the generated imagery.
 */

const { MCPServer } = require('modelcontextprotocol');
const express = require('express');
const axios = require('axios');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs').promises; // Use promises version of fs
const https = require('https'); // For downloading images
const http = require('http'); // For downloading images

// Load environment variables from parent project's .env file
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

// API Keys
const XAI_API_KEY = process.env.XAI_API_KEY;
if (!XAI_API_KEY) {
  console.error("ERROR: XAI_API_KEY environment variable is not set. Image generation will fail.");
}

// Constants
const MCP_PORT = 7777; // Port for this MCP server
const XAI_IMAGE_API_URL = "https://api.x.ai/v1/images/generate";
const IMAGE_OUTPUT_DIR = path.resolve(__dirname, '../../../Saved_Prayers/Images'); // Save images in parent project

// Ensure the output directory exists
async function ensureImageDirectory() {
  try {
    await fs.mkdir(IMAGE_OUTPUT_DIR, { recursive: true });
    console.log(`Image output directory ensured: ${IMAGE_OUTPUT_DIR}`);
  } catch (error) {
    console.error(`Error creating image output directory ${IMAGE_OUTPUT_DIR}:`, error);
    // Exit if we can't create the directory, as saving will fail
    process.exit(1);
  }
}

// Setup the Express app for MCP
const app = express();
app.use(cors());
app.use(express.json()); // No need for large limit for this tool

// Initialize the MCP server
const mcpServer = new MCPServer({
  name: 'grok-prayer-image',
  description: 'Generates images using the XAI/Grok image generation API.',
  version: '1.1.0', // Updated version
});

// Helper function to download image
const downloadImage = (url, dest) => {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const request = protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image. Status Code: ${response.statusCode}`));
        return;
      }
      // Create stream only after confirming status code
      const file = require('fs').createWriteStream(dest); // Use synchronous fs here for stream creation
      response.pipe(file);
      file.on('finish', () => {
        file.close(resolve); // Use callback version of close
      });
    });
    request.on('error', (err) => {
      // Attempt to remove partial file on error
      require('fs').unlink(dest, () => reject(err)); // Use synchronous fs here
    });
  });
};

// Register the 'generate_image' tool
mcpServer.registerTool({
  name: 'generate_image',
  description: 'Generates an image based on a prompt using XAI API and saves it.',
  inputSchema: {
    type: 'object',
    properties: {
      prompt: {
        type: 'string',
        description: 'The text prompt for image generation.'
      },
      output_filename: {
        type: 'string',
        description: 'The desired filename for the output image (e.g., image.png).'
      },
      width: {
        type: 'number',
        description: 'Image width (e.g., 1024).',
        default: 1024
      },
      height: {
        type: 'number',
        description: 'Image height (e.g., 1024).',
        default: 1024
      }
      // Add other potential XAI parameters if needed (e.g., n, quality)
    },
    required: ['prompt', 'output_filename']
  },
  handler: async (args) => {
    console.log(`Received generate_image request with prompt: ${args.prompt.substring(0, 50)}...`);
    const { prompt, output_filename, width = 1024, height = 1024 } = args;

    if (!XAI_API_KEY) {
      return { status: 'error', error: 'XAI_API_KEY is not configured on the server.' };
    }

    const headers = {
      "Authorization": `Bearer ${XAI_API_KEY}`,
      "Content-Type": "application/json"
    };

    // Construct payload - ensure size format is correct if API requires "widthxheight"
    const payload = {
      model: "grok-2-image-1212", // Assuming this is the correct model
      prompt: prompt,
      n: 1,
      size: `${width}x${height}` // Common format, adjust if needed
    };

    try {
      // 1. Call XAI Image Generation API
      console.log(`Calling XAI API: ${XAI_IMAGE_API_URL}`);
      const apiResponse = await axios.post(XAI_IMAGE_API_URL, payload, { headers: headers, timeout: 90000 }); // 90s timeout

      if (apiResponse.status !== 200 || !apiResponse.data || !apiResponse.data.data || apiResponse.data.data.length === 0 || !apiResponse.data.data[0].url) {
        console.error('Invalid response from XAI API:', apiResponse.data);
        return { status: 'error', error: 'Invalid or empty response from XAI image generation API.', details: apiResponse.data };
      }

      const imageUrl = apiResponse.data.data[0].url;
      console.log(`XAI API returned image URL: ${imageUrl}`);

      // 2. Download the image
      const outputPath = path.join(IMAGE_OUTPUT_DIR, output_filename);
      console.log(`Attempting to download image to: ${outputPath}`);
      await downloadImage(imageUrl, outputPath);
      console.log(`Successfully downloaded and saved image to ${outputPath}`);

      // 3. Return success with the path
      return { status: 'success', image_path: outputPath };

    } catch (error) {
      console.error('Error in generate_image handler:', error);
      let errorMessage = 'Failed to generate or save image.';
      if (axios.isAxiosError(error)) {
        errorMessage = `XAI API request failed: ${error.message}`;
        if (error.response) {
          errorMessage += ` Status: ${error.response.status}, Data: ${JSON.stringify(error.response.data)}`;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      return { status: 'error', error: errorMessage };
    }
  }
});

// Register the MCP info resource (optional but good practice)
mcpServer.registerResource({
  name: 'server_info',
  description: 'Information about the Grok Prayer Image server',
  handler: async () => {
    return {
      name: 'Grok Prayer Image MCP Server',
      version: '1.1.0',
      status: XAI_API_KEY ? 'API Key Configured' : 'API Key MISSING',
      image_output_dir: IMAGE_OUTPUT_DIR,
      tools: ['generate_image']
    };
  }
});

// Start the MCP server
async function startServer() {
  await ensureImageDirectory(); // Make sure directory exists before starting server

  const server = app.listen(MCP_PORT, () => {
    console.log(`Grok Prayer Image MCP server (v1.1.0) listening on port ${MCP_PORT}`);
    console.log(`Registered tool: generate_image`);
    console.log(`Image output directory: ${IMAGE_OUTPUT_DIR}`);
    if (!XAI_API_KEY) {
      console.warn("WARNING: XAI_API_KEY is not set. The 'generate_image' tool will return errors.");
    }
  });

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nShutting down MCP server...');
    server.close(() => {
      console.log('MCP server stopped.');
      process.exit(0);
    });
  });

  process.on('SIGTERM', () => {
    console.log('Shutting down MCP server...');
    server.close(() => {
      console.log('MCP server stopped.');
      process.exit(0);
    });
  });
}

startServer();

// Export the app for potential testing (optional)
module.exports = { app };
