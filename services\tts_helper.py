"""
TTS Helper Module
Provides enhanced functions for Text-to-Speech functionality, including utility functions
to bridge Python and Node.js components and ensure proper setup.
"""

import os
import sys
import json
import tempfile
import platform
import subprocess
from typing import Dict, Optional
from pathlib import Path

def check_tts_environment() -> Dict:
    """
    Check if the environment is properly set up for TTS functionality.
    
    Returns:
        Dictionary with status information
    """
    status = {
        "node_available": False,
        "node_version": None,
        "npm_available": False,
        "npm_version": None,
        "tts_script_found": False,
        "tts_script_path": None,
        "dependencies_installed": False,
        "api_keys": {
            "openai": False,
            "elevenlabs": False,
            "azure": False
        },
        "overall_status": False,
        "issues": []
    }
    
    print("\n--- Checking TTS Environment ---")
    
    # Check for Node.js
    try:
        node_result = subprocess.run(
            ["node", "--version"], 
            capture_output=True, 
            text=True
        )
        if node_result.returncode == 0:
            status["node_available"] = True
            status["node_version"] = node_result.stdout.strip()
            print(f"[✓] Node.js is available (version: {status['node_version']})")
        else:
            status["issues"].append("Node.js is not available")
            print(f"[✗] Node.js is not available: {node_result.stderr.strip()}")
    except FileNotFoundError:
        status["issues"].append("Node.js is not installed or not in PATH")
        print("[✗] Node.js is not installed or not in PATH")
    
    # Check for npm
    try:
        npm_result = subprocess.run(
            ["npm", "--version"], 
            capture_output=True, 
            text=True
        )
        if npm_result.returncode == 0:
            status["npm_available"] = True
            status["npm_version"] = npm_result.stdout.strip()
            print(f"[✓] npm is available (version: {status['npm_version']})")
        else:
            status["issues"].append("npm is not available")
            print(f"[✗] npm is not available: {npm_result.stderr.strip()}")
    except FileNotFoundError:
        status["issues"].append("npm is not installed or not in PATH")
        print("[✗] npm is not installed or not in PATH")
    
    # Find the TTS script
    project_root = find_project_root()
    if project_root:
        tts_script_path = os.path.join(project_root, "tts_service.js")
        if os.path.exists(tts_script_path):
            status["tts_script_found"] = True
            status["tts_script_path"] = tts_script_path
            print(f"[✓] TTS script found at: {tts_script_path}")
        else:
            status["issues"].append("TTS script not found")
            print(f"[✗] TTS script not found at expected path: {tts_script_path}")
    else:
        status["issues"].append("Project root not found")
        print("[✗] Project root not found, cannot locate TTS script")
    
    # Check for dependencies if script found and npm available
    if status["tts_script_found"] and status["npm_available"]:
        package_json_path = os.path.join(project_root, "package.json")
        package_lock_path = os.path.join(project_root, "package-lock.json")
        node_modules_path = os.path.join(project_root, "node_modules")
        
        if os.path.exists(node_modules_path) and os.path.exists(package_lock_path):
            # Check for specific required modules
            axios_path = os.path.join(node_modules_path, "axios")
            dotenv_path = os.path.join(node_modules_path, "dotenv")
            
            if os.path.exists(axios_path) and os.path.exists(dotenv_path):
                status["dependencies_installed"] = True
                print("[✓] Node.js dependencies installed")
            else:
                status["issues"].append("Some Node.js dependencies are missing")
                print("[✗] Some Node.js dependencies are missing (axios or dotenv)")
        else:
            status["issues"].append("Node.js dependencies not installed")
            print("[✗] Node.js dependencies not installed")
            print(f"    Run 'npm install' in {project_root} to install dependencies")
    
    # Check for API keys
    if os.getenv("OPENAI_API_KEY"):
        status["api_keys"]["openai"] = True
        print("[✓] OpenAI API key found in environment")
    else:
        status["issues"].append("OpenAI API key not found")
        print("[✗] OpenAI API key not found in environment (.env file)")

    if os.getenv("ELEVENLABS_API_KEY"):
        status["api_keys"]["elevenlabs"] = True
        print("[✓] ElevenLabs API key found in environment")
    else:
        print("[i] ElevenLabs API key not found (optional)")

    if os.getenv("AZURE_API_KEY"):
        status["api_keys"]["azure"] = True
        print("[✓] Azure API key found in environment")
    else:
        print("[i] Azure API key not found (optional)")
    
    # Set overall status
    if (status["node_available"] and 
        status["tts_script_found"] and 
        status["dependencies_installed"] and 
        status["api_keys"]["openai"]):
        status["overall_status"] = True
        print("\n[✓] TTS environment is properly set up")
    else:
        print("\n[✗] TTS environment has issues that need to be resolved")
        print("    Issues to fix:")
        for issue in status["issues"]:
            print(f"    - {issue}")
    
    return status

def install_tts_dependencies(project_root: str = None) -> bool:
    """
    Install Node.js dependencies for TTS functionality.
    
    Args:
        project_root: Project root directory (optional)
        
    Returns:
        True if dependencies were installed successfully
    """
    if project_root is None:
        project_root = find_project_root()
        if not project_root:
            print("[ERROR] Could not find project root directory")
            return False
    
    print(f"\n--- Installing TTS Dependencies in {project_root} ---")
    
    try:
        process = subprocess.run(
            ["npm", "install"], 
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        if process.returncode == 0:
            print("[✓] Dependencies installed successfully")
            return True
        else:
            print(f"[✗] Failed to install dependencies: {process.stderr}")
            return False
    except Exception as e:
        print(f"[✗] Error installing dependencies: {str(e)}")
        return False

def find_project_root() -> Optional[str]:
    """
    Find the project root directory by looking for key files.
    
    Returns:
        Project root directory path or None if not found
    """
    # Start with the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Check if this file itself is in the project root
    if any(os.path.exists(os.path.join(current_dir, file)) for file in ["package.json", "main.py", "tts_service.js"]):
        return current_dir
    
    # Go up one level if we're in a subdirectory (like services/ or utils/)
    parent_dir = os.path.dirname(current_dir)
    if any(os.path.exists(os.path.join(parent_dir, file)) for file in ["package.json", "main.py", "tts_service.js"]):
        return parent_dir
    
    # Try one more level up
    grandparent_dir = os.path.dirname(parent_dir)
    if any(os.path.exists(os.path.join(grandparent_dir, file)) for file in ["package.json", "main.py", "tts_service.js"]):
        return grandparent_dir
    
    # Check environmental variables or common locations as fallback
    env_project_root = os.getenv("PRAYER_PROJECT_ROOT")
    if env_project_root and os.path.exists(env_project_root):
        if any(os.path.exists(os.path.join(env_project_root, file)) for file in ["package.json", "main.py", "tts_service.js"]):
            return env_project_root
    
    # As a last resort, search directories
    return search_for_project_root()

def search_for_project_root() -> Optional[str]:
    """
    Search for project root in likely locations.
    
    Returns:
        Project root directory path or None if not found
    """
    # Common locations to check
    home_dir = str(Path.home())
    documents_dir = os.path.join(home_dir, "Documents")
    onedrive_dir = os.path.join(home_dir, "OneDrive", "Documents")
    
    locations = [
        os.getcwd(),  # Current working directory
        documents_dir,
        onedrive_dir
    ]
    
    # The user mentioned this path specifically
    specific_path = os.path.join("C:", os.sep, "Users", "highe", "OneDrive", "Documents", "GroqPrayerPromptChainAgt1")
    locations.insert(0, specific_path)  # Try this first
    
    for location in locations:
        if not os.path.exists(location):
            continue
            
        # Check if this directory has our key files
        if any(os.path.exists(os.path.join(location, file)) for file in ["package.json", "tts_service.js"]):
            return location
            
        # Check immediate subdirectories
        for subdir in os.listdir(location):
            subdir_path = os.path.join(location, subdir)
            if not os.path.isdir(subdir_path):
                continue
                
            if any(os.path.exists(os.path.join(subdir_path, file)) for file in ["package.json", "tts_service.js"]):
                return subdir_path
    
    return None

def test_tts_functionality(test_text: str = None) -> bool:
    """
    Test TTS functionality with a sample text.
    
    Args:
        test_text: Text to convert to speech (optional)
        
    Returns:
        True if the test was successful
    """
    if test_text is None:
        test_text = "(gentle) Hello. This is a test of the text-to-speech functionality."
    
    print("\n--- Testing TTS Functionality ---")
    
    # Find the project root
    project_root = find_project_root()
    if not project_root:
        print("[ERROR] Could not find project root directory")
        return False
    
    # Create a temporary file for the test text
    try:
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(test_text)
    
        print(f"[INFO] Created temporary file: {temp_file_path}")
        
        # Generate temporary output path
        output_path = os.path.splitext(temp_file_path)[0] + "_output.mp3"
        
        # Find TTS script
        tts_script_path = os.path.join(project_root, "tts_service.js")
        if not os.path.exists(tts_script_path):
            print(f"[ERROR] TTS script not found at: {tts_script_path}")
            return False
        
        # Prepare command
        cmd = [
            "node",
            tts_script_path,
            temp_file_path,
            "openai",
            "alloy",
            output_path,
            json.dumps({"model": "tts-1"})
        ]
        
        print(f"[INFO] Executing: {' '.join(cmd)}")
        
        # Run the command
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_root
        )
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print(f"[INFO] Command executed successfully")
            
            # Check for success message in stdout
            success = False
            for line in stdout.strip().split('\n'):
                if line.startswith('{') and line.endswith('}'):
                    try:
                        result = json.loads(line)
                        if result.get("success", False):
                            success = True
                            print(f"[✓] TTS test successful. Audio saved to: {result.get('outputPath')}")
                            break
                    except json.JSONDecodeError:
                        pass
            
            if not success:
                print(f"[✗] TTS test failed to produce valid output: {stdout}")
                if stderr:
                    print(f"[ERROR] Error output: {stderr}")
                return False
                
            return True
        else:
            print(f"[✗] TTS test failed with exit code {process.returncode}")
            print(f"[ERROR] Error output: {stderr}")
            return False
            
    except Exception as e:
        print(f"[ERROR] An error occurred during TTS test: {str(e)}")
        return False
    finally:
        # Clean up temporary file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
            print(f"[INFO] Removed temporary file: {temp_file_path}")

def fix_tts_environment() -> bool:
    """
    Fix common issues with the TTS environment.
    
    Returns:
        True if fixes were successful
    """
    print("\n--- Fixing TTS Environment ---")
    
    # Check current status
    status = check_tts_environment()
    
    if status["overall_status"]:
        print("[✓] No issues found with TTS environment")
        return True
    
    # Find project root
    project_root = find_project_root()
    if not project_root:
        print("[ERROR] Could not find project root directory")
        return False
    
    fixes_applied = False
    
    # Fix missing dependencies
    if not status["dependencies_installed"] and status["npm_available"]:
        print("[INFO] Installing missing dependencies...")
        if install_tts_dependencies(project_root):
            fixes_applied = True
    
    # Check for missing API keys in .env file
    env_file_path = os.path.join(project_root, ".env")
    needs_env_update = False
    env_lines = []
    
    if os.path.exists(env_file_path):
        with open(env_file_path, "r") as env_file:
            env_lines = env_file.readlines()
    
        # Check for missing API keys
        has_openai = any(line.strip().startswith("OPENAI_API_KEY=") for line in env_lines)
        if not has_openai and not status["api_keys"]["openai"]:
            print("[INFO] Adding placeholder for OPENAI_API_KEY to .env file")
            env_lines.append("OPENAI_API_KEY=your_openai_api_key_here\n")
            needs_env_update = True
        
        has_elevenlabs = any(line.strip().startswith("ELEVENLABS_API_KEY=") for line in env_lines)
        if not has_elevenlabs and not status["api_keys"]["elevenlabs"]:
            print("[INFO] Adding placeholder for ELEVENLABS_API_KEY to .env file")
            env_lines.append("ELEVENLABS_API_KEY=your_elevenlabs_api_key_here\n")
            needs_env_update = True
        
        has_azure = any(line.strip().startswith("AZURE_API_KEY=") for line in env_lines)
        if not has_azure and not status["api_keys"]["azure"]:
            print("[INFO] Adding placeholder for AZURE_API_KEY to .env file")
            env_lines.append("AZURE_API_KEY=your_azure_api_key_here\n")
            needs_env_update = True
    else:
        # Create new .env file
        print("[INFO] Creating new .env file with API key placeholders")
        env_lines = [
            "# API Keys for Prayer Application\n",
            "GROQ_API_KEY=your_groq_api_key_here\n",
            "OPENAI_API_KEY=your_openai_api_key_here\n",
            "GOOGLE_API_KEY=your_google_api_key_here\n",
            "PERPLEXITY_API_KEY=your_perplexity_api_key_here\n",
            "ELEVENLABS_API_KEY=your_elevenlabs_api_key_here\n",
            "AZURE_API_KEY=your_azure_api_key_here\n",
            "AZURE_REGION=eastus\n"
        ]
        needs_env_update = True
    
    if needs_env_update:
        with open(env_file_path, "w") as env_file:
            env_file.writelines(env_lines)
        print(f"[✓] Updated .env file at {env_file_path}")
        print("    Please add your actual API keys to the .env file")
        fixes_applied = True
    
    if fixes_applied:
        print("[INFO] Fixes applied. Checking environment again...")
        new_status = check_tts_environment()
        
        if new_status["overall_status"]:
            print("[✓] TTS environment is now properly set up")
            return True
        else:
            print("[!] Some issues still remain with the TTS environment")
            return False
    else:
        print("[!] No automatic fixes were applied")
        return False

def generate_test_prayer_with_tts():
    """Generate a test prayer and convert it to audio to verify the whole pipeline"""
    print("\n--- Generating Test Prayer with TTS ---")
    
    # Generate a simple test prayer
    test_prayer = """
# A Simple Test Prayer

(gentle, reverent) Divine Spirit of Compassion and Wisdom, we come before you with open hearts.

(pause) We seek your guidance in this moment of connection.

(emphasis) May we be instruments of peace and understanding in a world that needs healing.

(soft) Grant us clarity of mind, warmth of heart, and strength of purpose.

(rising intensity) Let us embrace each day with gratitude and each challenge as an opportunity for growth.

(whisper) In your sacred presence, we find peace. Amen.
"""
    
    # Save to a temporary file
    try:
        project_root = find_project_root()
        if not project_root:
            print("[ERROR] Could not find project root directory")
            return False
            
        saved_prayers_dir = os.path.join(project_root, "Saved_Prayers")
        os.makedirs(saved_prayers_dir, exist_ok=True)
        
        test_prayer_path = os.path.join(saved_prayers_dir, "Test_Prayer.txt")
        with open(test_prayer_path, "w") as f:
            f.write(test_prayer)
            
        print(f"[INFO] Saved test prayer to {test_prayer_path}")
        
        # Convert to audio
        tts_script_path = os.path.join(project_root, "tts_service.js")
        output_path = os.path.join(saved_prayers_dir, "Test_Prayer_Audio.mp3")
        
        # Prepare command
        cmd = [
            "node",
            tts_script_path,
            test_prayer_path,
            "openai",
            "alloy",
            output_path,
            json.dumps({"model": "tts-1"})
        ]
        
        print(f"[INFO] Executing TTS conversion: {' '.join(cmd)}")
        
        # Run the command
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_root
        )
        
        stdout, stderr = process.communicate()
        
        if process.returncode == 0:
            print(f"[INFO] TTS command executed successfully")
            
            # Check for success message in stdout
            success = False
            for line in stdout.strip().split('\n'):
                if line.startswith('{') and line.endswith('}'):
                    try:
                        result = json.loads(line)
                        if result.get("success", False):
                            success = True
                            print(f"[✓] Test prayer converted to audio successfully!")
                            print(f"    Audio saved to: {result.get('outputPath')}")
                            return True
                    except json.JSONDecodeError:
                        pass
            
            if not success:
                print(f"[✗] TTS conversion failed to produce valid output")
                if stdout:
                    print(f"[INFO] Command output: {stdout}")
                if stderr:
                    print(f"[ERROR] Error output: {stderr}")
                return False
        else:
            print(f"[✗] TTS conversion failed with exit code {process.returncode}")
            if stderr:
                print(f"[ERROR] Error output: {stderr}")
            return False
            
    except Exception as e:
        print(f"[ERROR] An error occurred during test prayer generation: {str(e)}")
        return False

if __name__ == "__main__":
    """Run module as script to perform TTS environment check and fixes"""
    print("=== Prayer Application TTS Helper ===")
    
    # Check command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "check":
            check_tts_environment()
        elif command == "fix":
            fix_tts_environment()
        elif command == "test":
            test_tts_functionality()
        elif command == "prayer":
            generate_test_prayer_with_tts()
        elif command == "install":
            install_tts_dependencies()
        else:
            print(f"Unknown command: {command}")
            print("Available commands: check, fix, test, prayer, install")
    else:
        # Default: run check and fix if needed
        status = check_tts_environment()
        if not status["overall_status"]:
            print("\nWould you like to attempt to fix these issues? (y/n)")
            choice = input().strip().lower()
            if choice == 'y':
                fix_tts_environment()