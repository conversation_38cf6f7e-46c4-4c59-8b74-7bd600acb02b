"""
OpenAI API Service
Handles interactions with the OpenAI API for TTS text optimization and audio generation.
"""

import os
import openai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key
openai_api_key = os.environ.get("OPENAI_API_KEY")
if not openai_api_key:
    print("[ERROR] OPENAI_API_KEY not found in environment variables. OpenAI services will fail.")
else:
    openai.api_key = openai_api_key

def generate_verbal_text(prayer_text):
    """
    Generates TTS-optimized text with emotional cues using OpenAI.

    Args:
        prayer_text: Original prayer text to optimize

    Returns:
        TTS-optimized text with stage directions
    """
    print("\nGenerating Verbal Prayer Text using OpenAI model...")

    if not prayer_text:
        print("   [WARNING] No prayer text provided to convert.")
        return "[ERROR] No prayer text provided for conversion."

    if not openai_api_key:
        error = "Cannot generate verbal prayer text: OpenAI API Key not configured."
        print(f"   [ERROR] {error}")
        return f"[ERROR] {error}"

    # Construct the prompt for TTS optimization
    prompt = (
        f"Rewrite this prayer for audio narration. Add emotional cues in parentheses where appropriate.\n\n"
        f"{prayer_text}\n\n"
        f"Important: Return ONLY the optimized prayer text with cues. No explanations or additional text."
    )

    try:
        print("   [INFO] Calling OpenAI with TTS optimization prompt...")

        response = openai.chat.completions.create(
            model="gpt-4o-mini",  # Using the latest mini model
            messages=[
                {"role": "system", "content": "You are a spiritual voice coach that optimizes text for spoken prayer.\n\nGUIDELINES:\n1. Add emotional cues in parentheses ONLY where they enhance the prayer\n2. Place cues on their own line BEFORE the text they affect\n3. Use only these cues: (pause), (whisper), (emphasis), (reverent), (solemn)\n4. Format cues like this:\n   (pause)\n   This text will be spoken after a pause.\n\n   (whisper)\n   This text will be whispered.\n5. DO NOT add any text at the beginning or end of the prayer\n6. DO NOT add any explanations, notes, or commentary\n7. DO NOT add any closing statements like 'Amen' unless it's in the original\n8. Preserve the original meaning and spiritual essence\n9. Keep the same paragraph structure as the original\n10. End exactly where the original prayer ends - no additional content"},
                {"role": "user", "content": prompt}
            ]
        )

        verbal_text = response.choices[0].message.content.strip()
        print("   [INFO] Successfully generated TTS-optimized text using OpenAI.")
        return verbal_text

    except Exception as e:
        error_msg = f"Error generating verbal prayer text with OpenAI: {str(e)}"
        print(f"   [ERROR] {error_msg}")
        return f"[ERROR] {error_msg}"
