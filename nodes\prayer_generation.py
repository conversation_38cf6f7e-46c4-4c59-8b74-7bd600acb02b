import logging
import os
import datetime
from typing import Dict, List, Optional
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import requests # Added this import

# Import the OpenAI Prayer Agent
from services.openai_prayer_agent import OpenAIPrayerAgent

logger = logging.getLogger(__name__)

def prepare_next_movement_node(state: Dict) -> Dict:
    """
    Prepare the next spiritual movement/faith to process.

    Args:
        state: Current workflow state

    Returns:
        Updated state with the next movement to process
    """
    print("\nNode: Preparing next spiritual movement...")

    # Get movements still to process
    movements_to_process = state.get('spiritual_movements_to_process', [])
    individual_prayers = state.get('individual_prayers', {})
    prayer_focus_theme = state.get('prayer_focus_theme', '')
    run_output_dir = state.get('run_output_dir', '')

    if not movements_to_process:
        print("   [WARNING] No more spiritual movements to process.")
        return {
            "current_movement": None,
            "spiritual_movements_to_process": [],
            "individual_prayers": individual_prayers,
            "prayer_focus_theme": prayer_focus_theme,
            "run_output_dir": run_output_dir
        }

    # Select the next movement (first one in the list)
    next_movement = movements_to_process[0]

    # Remove it from the list to process
    updated_movements = movements_to_process[1:]

    print(f"   [INFO] Next movement to process: {next_movement}")
    print(f"   [INFO] {len(updated_movements)} movements remaining after this.")

    # Update state
    return {
        "current_movement": next_movement,
        "spiritual_movements_to_process": updated_movements,
        "individual_prayers": individual_prayers,
        "prayer_focus_theme": prayer_focus_theme,
        "run_output_dir": run_output_dir
    }

def generate_single_prayer_node(state: Dict) -> Dict:
    """
    Generate a prayer for the current spiritual movement.

    Args:
        state: Current workflow state

    Returns:
        Updated state with the generated prayer
    """
    print("\nNode: Generating prayer for current movement...")

    current_movement = state.get('current_movement')
    spiritual_movements_to_process = state.get('spiritual_movements_to_process', [])
    individual_prayers = state.get('individual_prayers', {})

    if not current_movement:
        err_msg = "Cannot generate prayer: No current movement specified."
        print(f"   [ERROR] {err_msg}")
        return {
            "error_message": err_msg,
            "current_movement": current_movement,
            "spiritual_movements_to_process": spiritual_movements_to_process,
            "individual_prayers": individual_prayers
        }

    prayer_focus_theme = state.get('prayer_focus_theme', '')
    # Derive wisdom_focus similarly to research node for consistency
    wisdom_focus = prayer_focus_theme

    if not prayer_focus_theme:
        err_msg = "Cannot generate prayer: Prayer focus theme not specified."
        print(f"   [ERROR] {err_msg}")
        return {
            "error_message": err_msg,
            "current_movement": current_movement,
            "spiritual_movements_to_process": spiritual_movements_to_process,
            "individual_prayers": individual_prayers
        }

    print(f"   [INFO] Generating prayer for {current_movement} focused on: {prayer_focus_theme} using OpenAI Agent...")

    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_exponential(multiplier=1, min=4, max=10),  # Exponential backoff
        retry=retry_if_exception_type(requests.exceptions.ConnectionError) # Retry on connection errors
    )
    def _generate_prayer_with_retry(faith, prayer_focus, wisdom_focus):
        prayer_agent = OpenAIPrayerAgent(model_name="Llama-4-Scout-17B-16E-Instruct-FP8")
        print(f"   [DEBUG] Using model: {prayer_agent.model_name}")
        print(f"   [DEBUG] Prayer focus: {prayer_focus_theme[:50]}...")
        return prayer_agent.generate_individual_prayer(
            faith=faith,
            prayer_focus=prayer_focus,
            wisdom_focus=wisdom_focus
        )

    try:
        prayer_text = _generate_prayer_with_retry(current_movement, prayer_focus_theme, wisdom_focus)

        if not prayer_text:
            raise ValueError("OpenAI Prayer Agent returned empty content.")

        print(f"   [INFO] Successfully generated prayer for {current_movement}.")
        individual_prayers[current_movement] = prayer_text

        return {
            "individual_prayers": individual_prayers,
            "current_movement": current_movement,
            "error_message": None,  # Explicitly clear any error
            "spiritual_movements_to_process": spiritual_movements_to_process
        }
    except Exception as e:
        err_msg = f"Failed to generate prayer for {current_movement}: {str(e)}"
        print(f"   [ERROR] {err_msg}")
        # Store an empty string or None for the failed prayer to indicate it wasn't generated
        individual_prayers[current_movement] = None 
        return {
            "error_message": err_msg,
            "current_movement": current_movement,
            "spiritual_movements_to_process": spiritual_movements_to_process,
            "individual_prayers": individual_prayers
        }

def save_individual_prayer_node(state: Dict) -> Dict:
    """
    Save the generated prayer for the current movement.

    Args:
        state: Current workflow state

    Returns:
        Updated state with saved prayer information
    """
    print("\nNode: Saving individual prayer...")

    current_movement = state.get('current_movement')
    individual_prayers = state.get('individual_prayers', {}) # Get the dictionary of prayers
    run_output_dir = state.get('run_output_dir')
    spiritual_movements_to_process = state.get('spiritual_movements_to_process', [])

    if not current_movement:
        err_msg = "Cannot save prayer: No current movement specified."
        print(f"   [ERROR] {err_msg}")
        return {
            "error_message": err_msg,
            "individual_prayers": individual_prayers,
            "spiritual_movements_to_process": spiritual_movements_to_process
        }

    # Correctly retrieve the prayer for the current movement from the dictionary
    generated_prayer = individual_prayers.get(current_movement)

    if generated_prayer is None: # Check if prayer exists for this movement (could be None or empty string)
        print(f"   [WARNING] No prayer text found in state for {current_movement}. Skipping save.")
        # Ensure the key exists even if empty, consistent with previous logic
        if current_movement not in individual_prayers:
             individual_prayers[current_movement] = ""
        return {
            "individual_prayers": individual_prayers,
            "current_movement": current_movement,
            "spiritual_movements_to_process": spiritual_movements_to_process
            # Return generated_prayer as None since we didn't save anything new here
            # "generated_prayer": None # This key isn't part of the state schema anyway
        }

    try:
        print(f"   [INFO] Saving prayer for {current_movement}...")
        # Save to the run directory if available
        if run_output_dir and os.path.isdir(run_output_dir):
            filename = f"{current_movement}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            filepath = os.path.join(run_output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(generated_prayer)

            print(f"   [INFO] Saved {current_movement} prayer to {filepath}")

        # Add to the collection of prayers in state
        individual_prayers[current_movement] = generated_prayer

        # Return relevant state fields
        # No need to return 'generated_prayer' as it's not a direct state field
        return {
            "individual_prayers": individual_prayers, # Pass updated dict along
            "current_movement": current_movement,
            "spiritual_movements_to_process": spiritual_movements_to_process
        }
    except Exception as e:
        err_msg = f"Failed to save prayer for {current_movement}: {str(e)}"
        print(f"   [ERROR] {err_msg}")
        return {
            "error_message": err_msg,
            "individual_prayers": individual_prayers,
            "spiritual_movements_to_process": spiritual_movements_to_process
        }

def should_continue_generating(state: Dict) -> str:
    """
    Determine if there are more spiritual movements to process.

    Args:
        state: Current workflow state

    Returns:
        Next workflow edge to follow
    """
    # Check if there are more movements to process
    movements_remaining = state.get('spiritual_movements_to_process', [])

    if movements_remaining:
        print(f"\nEdge: {len(movements_remaining)} movements remaining, continuing generation.")
        return "continue_generation"

    print("\nEdge: No more movements to process, moving to aggregation.")
    # Always proceed to aggregation if no more movements, even if errors occurred
    return "aggregate"
