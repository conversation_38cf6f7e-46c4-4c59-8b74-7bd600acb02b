import 'package:equatable/equatable.dart';

// This class represents the state of a prayer generation process
// It aligns with the Python state model from your backend
class PrayerState extends Equatable {
  // Core prayer information
  final String? prayerFocusTheme;
  final String? searchContext;
  final Map<String, dynamic>? religiousTerms;
  final Map<String, dynamic>? faithResearch;

  // Workflow state tracking
  final List<String>? spiritualMovementsToProcess;
  final Map<String, String>? individualPrayers;
  final String? currentMovement;
  final String? runOutputDir;
  final String? errorMessage;
  final String? currentStatus;
  final int? progressPercentage;
  final String? statusDetails;

  // Unified prayer fields
  final String? combinedPrayerText;
  final String? unifiedPrayerMarkdown;
  final String? unifiedPrayerFilename;
  final String? unifiedPrayerFilepath;

  // TTS fields
  final bool? audioConversionRequested;
  final bool? ttsConfirmed;
  final String? ttsProvider;
  final String? ttsVoiceId;
  final Map<String, dynamic>? ttsOptions;
  final String? verbalPrayerText;
  final String? verbalPrayerFilepath;
  final String? audioFilepath;

  // Image prayer fields
  final bool? imagePrayerRequested;
  final bool? imagePrayerGenerated;
  final List<String>? imagePrayerFilepaths;
  
  // Token counts for monitoring AI model usage
  final int? totalTokensUsed;
  final Map<String, int>? tokensByStage;

  // Constructor
  const PrayerState({
    this.prayerFocusTheme,
    this.searchContext,
    this.religiousTerms,
    this.faithResearch,
    this.spiritualMovementsToProcess,
    this.individualPrayers,
    this.currentMovement,
    this.runOutputDir,
    this.errorMessage,
    this.currentStatus,
    this.progressPercentage,
    this.statusDetails,
    this.combinedPrayerText,
    this.unifiedPrayerMarkdown,
    this.unifiedPrayerFilename,
    this.unifiedPrayerFilepath,
    this.audioConversionRequested,
    this.ttsConfirmed,
    this.ttsProvider,
    this.ttsVoiceId,
    this.ttsOptions,
    this.verbalPrayerText,
    this.verbalPrayerFilepath,
    this.audioFilepath,
    this.imagePrayerRequested,
    this.imagePrayerGenerated,
    this.imagePrayerFilepaths,
    this.totalTokensUsed,
    this.tokensByStage,
  });

  // Create from JSON
  factory PrayerState.fromJson(Map<String, dynamic> json) {
    return PrayerState(
      prayerFocusTheme: json['prayer_focus_theme'] as String?,
      searchContext: json['search_context'] as String?,
      religiousTerms: json['religious_terms'] as Map<String, dynamic>?,
      faithResearch: json['faith_research'] as Map<String, dynamic>?,
      spiritualMovementsToProcess: 
          (json['spiritual_movements_to_process'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      individualPrayers: 
          (json['individual_prayers'] as Map<String, dynamic>?)
              ?.map((k, v) => MapEntry(k, v as String)),
      currentMovement: json['current_movement'] as String?,
      runOutputDir: json['run_output_dir'] as String?,
      errorMessage: json['error_message'] as String?,
      currentStatus: json['current_status'] as String?,
      progressPercentage: json['progress_percentage'] as int?,
      statusDetails: json['status_details'] as String?,
      combinedPrayerText: json['combined_prayer_text'] as String?,
      unifiedPrayerMarkdown: json['unified_prayer_markdown'] as String?,
      unifiedPrayerFilename: json['unified_prayer_filename'] as String?,
      unifiedPrayerFilepath: json['unified_prayer_filepath'] as String?,
      audioConversionRequested: json['audio_conversion_requested'] as bool?,
      ttsConfirmed: json['tts_confirmed'] as bool?,
      ttsProvider: json['tts_provider'] as String?,
      ttsVoiceId: json['tts_voice_id'] as String?,
      ttsOptions: json['tts_options'] as Map<String, dynamic>?,
      verbalPrayerText: json['verbal_prayer_text'] as String?,
      verbalPrayerFilepath: json['verbal_prayer_filepath'] as String?,
      audioFilepath: json['audio_filepath'] as String?,
      imagePrayerRequested: json['image_prayer_requested'] as bool?,
      imagePrayerGenerated: json['image_prayer_generated'] as bool?,
      imagePrayerFilepaths: 
          (json['image_prayer_filepaths'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList(),
      totalTokensUsed: json['total_tokens_used'] as int?,
      tokensByStage: json['tokens_by_stage'] as Map<String, int>?,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'prayer_focus_theme': prayerFocusTheme,
      'search_context': searchContext,
      'religious_terms': religiousTerms,
      'faith_research': faithResearch,
      'spiritual_movements_to_process': spiritualMovementsToProcess,
      'individual_prayers': individualPrayers,
      'current_movement': currentMovement,
      'run_output_dir': runOutputDir,
      'error_message': errorMessage,
      'current_status': currentStatus,
      'progress_percentage': progressPercentage,
      'status_details': statusDetails,
      'combined_prayer_text': combinedPrayerText,
      'unified_prayer_markdown': unifiedPrayerMarkdown,
      'unified_prayer_filename': unifiedPrayerFilename,
      'unified_prayer_filepath': unifiedPrayerFilepath,
      'audio_conversion_requested': audioConversionRequested,
      'tts_confirmed': ttsConfirmed,
      'tts_provider': ttsProvider,
      'tts_voice_id': ttsVoiceId,
      'tts_options': ttsOptions,
      'verbal_prayer_text': verbalPrayerText,
      'verbal_prayer_filepath': verbalPrayerFilepath,
      'audio_filepath': audioFilepath,
      'image_prayer_requested': imagePrayerRequested,
      'image_prayer_generated': imagePrayerGenerated,
      'image_prayer_filepaths': imagePrayerFilepaths,
      'total_tokens_used': totalTokensUsed,
      'tokens_by_stage': tokensByStage,
    };
  }

  // Copy with method for immutability
  PrayerState copyWith({
    String? prayerFocusTheme,
    String? searchContext,
    Map<String, dynamic>? religiousTerms,
    Map<String, dynamic>? faithResearch,
    List<String>? spiritualMovementsToProcess,
    Map<String, String>? individualPrayers,
    String? currentMovement,
    String? runOutputDir,
    String? errorMessage,
    String? currentStatus,
    int? progressPercentage,
    String? statusDetails,
    String? combinedPrayerText,
    String? unifiedPrayerMarkdown,
    String? unifiedPrayerFilename,
    String? unifiedPrayerFilepath,
    bool? audioConversionRequested,
    bool? ttsConfirmed,
    String? ttsProvider,
    String? ttsVoiceId,
    Map<String, dynamic>? ttsOptions,
    String? verbalPrayerText,
    String? verbalPrayerFilepath,
    String? audioFilepath,
    bool? imagePrayerRequested,
    bool? imagePrayerGenerated,
    List<String>? imagePrayerFilepaths,
    int? totalTokensUsed,
    Map<String, int>? tokensByStage,
  }) {
    return PrayerState(
      prayerFocusTheme: prayerFocusTheme ?? this.prayerFocusTheme,
      searchContext: searchContext ?? this.searchContext,
      religiousTerms: religiousTerms ?? this.religiousTerms,
      faithResearch: faithResearch ?? this.faithResearch,
      spiritualMovementsToProcess: spiritualMovementsToProcess ?? this.spiritualMovementsToProcess,
      individualPrayers: individualPrayers ?? this.individualPrayers,
      currentMovement: currentMovement ?? this.currentMovement,
      runOutputDir: runOutputDir ?? this.runOutputDir,
      errorMessage: errorMessage ?? this.errorMessage,
      currentStatus: currentStatus ?? this.currentStatus,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      statusDetails: statusDetails ?? this.statusDetails,
      combinedPrayerText: combinedPrayerText ?? this.combinedPrayerText,
      unifiedPrayerMarkdown: unifiedPrayerMarkdown ?? this.unifiedPrayerMarkdown,
      unifiedPrayerFilename: unifiedPrayerFilename ?? this.unifiedPrayerFilename,
      unifiedPrayerFilepath: unifiedPrayerFilepath ?? this.unifiedPrayerFilepath,
      audioConversionRequested: audioConversionRequested ?? this.audioConversionRequested,
      ttsConfirmed: ttsConfirmed ?? this.ttsConfirmed,
      ttsProvider: ttsProvider ?? this.ttsProvider,
      ttsVoiceId: ttsVoiceId ?? this.ttsVoiceId,
      ttsOptions: ttsOptions ?? this.ttsOptions,
      verbalPrayerText: verbalPrayerText ?? this.verbalPrayerText,
      verbalPrayerFilepath: verbalPrayerFilepath ?? this.verbalPrayerFilepath,
      audioFilepath: audioFilepath ?? this.audioFilepath,
      imagePrayerRequested: imagePrayerRequested ?? this.imagePrayerRequested,
      imagePrayerGenerated: imagePrayerGenerated ?? this.imagePrayerGenerated,
      imagePrayerFilepaths: imagePrayerFilepaths ?? this.imagePrayerFilepaths,
      totalTokensUsed: totalTokensUsed ?? this.totalTokensUsed,
      tokensByStage: tokensByStage ?? this.tokensByStage,
    );
  }

  // Helper method to check if prayer generation is complete
  bool get isCompleted => 
      currentStatus == 'completed' || 
      (unifiedPrayerFilepath != null && 
      (audioConversionRequested == false || audioFilepath != null) &&
      (imagePrayerRequested == false || imagePrayerGenerated == true));

  // Check if there is an error
  bool get hasError => errorMessage != null && errorMessage!.isNotEmpty;

  // Get the current stage
  String get currentStage {
    if (currentStatus == null) return 'Not Started';
    
    switch (currentStatus) {
      case 'researching':
        return 'Researching Spiritual Terms';
      case 'generating':
        return 'Generating Individual Prayers';
      case 'aggregating':
        return 'Creating Unified Prayer';
      case 'audio':
        return 'Converting to Audio';
      case 'images':
        return 'Creating Visual Prayers';
      case 'completed':
        return 'Completed';
      case 'error':
        return 'Error';
      default:
        return currentStatus!;
    }
  }

  // Get progress as percentage (0-100)
  int get progress {
    if (progressPercentage != null) return progressPercentage!;
    
    if (currentStatus == null) return 0;
    if (currentStatus == 'completed') return 100;
    if (currentStatus == 'error') return 0;
    
    // Map stages to approximate percentages
    switch (currentStatus) {
      case 'researching':
        return 10;
      case 'generating':
        // Calculate based on how many faiths have been processed
        final totalFaiths = spiritualMovementsToProcess?.length ?? 1;
        final processedFaiths = individualPrayers?.length ?? 0;
        return 10 + ((processedFaiths / totalFaiths) * 40).round();
      case 'aggregating':
        return 50;
      case 'audio':
        return audioFilepath != null ? 80 : 70;
      case 'images':
        return imagePrayerGenerated == true ? 90 : 80;
      default:
        return 0;
    }
  }

  @override
  List<Object?> get props => [
    prayerFocusTheme,
    searchContext,
    religiousTerms,
    faithResearch,
    spiritualMovementsToProcess,
    individualPrayers,
    currentMovement,
    runOutputDir,
    errorMessage,
    currentStatus,
    progressPercentage,
    statusDetails,
    combinedPrayerText,
    unifiedPrayerMarkdown,
    unifiedPrayerFilename,
    unifiedPrayerFilepath,
    audioConversionRequested,
    ttsConfirmed,
    ttsProvider,
    ttsVoiceId,
    ttsOptions,
    verbalPrayerText,
    verbalPrayerFilepath,
    audioFilepath,
    imagePrayerRequested,
    imagePrayerGenerated,
    imagePrayerFilepaths,
    totalTokensUsed,
    tokensByStage,
  ];
}
