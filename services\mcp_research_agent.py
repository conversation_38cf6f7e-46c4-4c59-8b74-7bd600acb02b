"""
MCP Research Agent Service using brave-groq-research MCP server
Provides enhanced research capabilities for rare and tribal faiths.
"""

import os
import json
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class MCPResearchAgent:
    """
    Enhanced Research agent that uses the brave-groq-research MCP server
    to gather information about rare and tribal faiths.
    """

    def __init__(self, debug_mode=False):
        """
        Initialize the MCP research agent.

        Args:
            debug_mode: Whether to output additional debugging information
        """
        self.debug_mode = debug_mode
        print(f"[INFO] Initialized MCPResearchAgent")

    def research_faith_specific(self, faith: str, prayer_focus: str, wisdom_focus: str) -> Dict:
        """
        Perform enhanced faith-specific research with structured output format.

        Args:
            faith: Name of the faith tradition
            prayer_focus: Focus of the prayer
            wisdom_focus: Spiritual wisdom to integrate

        Returns:
            Dictionary containing research results for the specific faith
        """
        try:
            from mcp import use_mcp_tool
            
            print(f"[INFO] Researching {faith} using MCP server...")
            result = use_mcp_tool(
                server_name="brave-groq-research",
                tool_name="faith_research",
                arguments={
                    "faith": faith,
                    "prayer_focus": prayer_focus,
                    "wisdom_focus": wisdom_focus,
                    "model_type": "default"  # Using the most capable model
                }
            )
            
            if not result or not result.get("content"):
                print(f"[WARNING] No research data obtained for {faith}")
                return self._get_empty_research()
                
            # Parse the response into structured data
            research_data = self._parse_faith_research(result["content"][0]["text"], faith)
            
            # Display research results
            print(f"[INFO] Research results for {faith}:")
            print(f"  - Divine Names: {len(research_data['divine_names'])} items")
            print(f"  - Sacred Terms: {len(research_data['sacred_terms'])} items")
            print(f"  - Prayer Structure: {'Yes' if research_data['prayer_structure'] else 'No'}")
            print(f"  - Scriptural References: {len(research_data['scriptural_references'])} items")
            print(f"  - Cultural Context: {'Yes' if research_data['cultural_context'] else 'No'}")
            
            return research_data

        except Exception as e:
            print(f"[ERROR] Error during MCP research for {faith}: {str(e)}")
            return self._get_empty_research()

    def get_general_context(self, prayer_focus: str, wisdom_focus: str) -> str:
        """Get general spiritual context for the prayer focus and wisdom."""
        try:
            from mcp import use_mcp_tool
            
            print(f"[INFO] Getting general context using MCP server...")
            result = use_mcp_tool(
                server_name="brave-groq-research",
                tool_name="general_research",
                arguments={
                    "prayer_focus": prayer_focus,
                    "wisdom_focus": wisdom_focus,
                    "model_type": "default"
                }
            )
            
            if not result or not result.get("content"):
                print("[WARNING] No general context obtained")
                return ""
                
            return result["content"][0]["text"]

        except Exception as e:
            print(f"[ERROR] Error getting general context: {str(e)}")
            return ""

    def _get_empty_research(self) -> Dict:
        """Return an empty research data structure."""
        return {
            "divine_names": [],
            "sacred_terms": [],
            "prayer_structure": "",
            "scriptural_references": [],
            "cultural_context": ""
        }

    def _parse_faith_research(self, raw_text: str, faith: str) -> Dict:
        """Parse the faith-specific research response into structured data."""
        if self.debug_mode:
            print(f"[DEBUG] Parsing raw text for {faith}:\n{raw_text}")
            
        research_data = self._get_empty_research()
        
        try:
            # Split the text into sections
            sections = raw_text.split("\n\n")
            current_section = None
            
            for section in sections:
                section = section.strip()
                if not section:
                    continue
                    
                # Identify section type
                if section.startswith("DIVINE_NAMES:"):
                    current_section = "divine_names"
                    items = section.replace("DIVINE_NAMES:", "").strip().split("\n")
                    research_data["divine_names"] = [item.strip("- ") for item in items if item.strip("- ")]
                    
                elif section.startswith("SACRED_TERMS:"):
                    current_section = "sacred_terms"
                    items = section.replace("SACRED_TERMS:", "").strip().split("\n")
                    research_data["sacred_terms"] = [item.strip("- ") for item in items if item.strip("- ")]
                    
                elif section.startswith("PRAYER_STRUCTURE:"):
                    current_section = "prayer_structure"
                    research_data["prayer_structure"] = section.replace("PRAYER_STRUCTURE:", "").strip()
                    
                elif section.startswith("SCRIPTURAL_REFERENCES:"):
                    current_section = "scriptural_references"
                    items = section.replace("SCRIPTURAL_REFERENCES:", "").strip().split("\n")
                    research_data["scriptural_references"] = [item.strip("- ") for item in items if item.strip("- ")]
                    
                elif section.startswith("CULTURAL_CONTEXT:"):
                    current_section = "cultural_context"
                    research_data["cultural_context"] = section.replace("CULTURAL_CONTEXT:", "").strip()
            
        except Exception as e:
            print(f"[ERROR] Error parsing research data for {faith}: {str(e)}")
            
        return research_data

    def get_complete_research(self, prayer_focus: str, wisdom_focus: str, faiths: List[str]) -> Tuple[str, Dict[str, Dict]]:
        """Get complete research data including general context and faith-specific research."""
        print(f"\n[INFO] Starting comprehensive MCP research for prayer on '{prayer_focus}' with wisdom '{wisdom_focus}'")

        # Get general context
        general_context = self.get_general_context(prayer_focus, wisdom_focus)

        # Get faith-specific research
        faith_research = {}
        for faith in faiths:
            print(f"[INFO] Researching {faith}...")
            research_data = self.research_faith_specific(faith, prayer_focus, wisdom_focus)
            faith_research[faith] = research_data

        return general_context, faith_research
