import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/prayer_state.dart';
import '../services/prayer_service.dart';

// Provider for the prayer service
final prayerServiceProvider = Provider<PrayerService>((ref) {
  return PrayerService();
});

// Provider for the prayer state
final prayerStateProvider = StateNotifierProvider<PrayerStateNotifier, AsyncValue<PrayerState?>>((ref) {
  final prayerService = ref.watch(prayerServiceProvider);
  return PrayerStateNotifier(prayerService);
});

// Notifier to manage prayer state
class PrayerStateNotifier extends StateNotifier<AsyncValue<PrayerState?>> {
  final PrayerService _prayerService;
  String? _currentRunId;
  StreamSubscription<PrayerState>? _stateSubscription;
  
  PrayerStateNotifier(this._prayerService) : super(const AsyncValue.loading());
  
  // Get current run ID
  String? get currentRunId => _currentRunId;
  
  // Start prayer generation
  Future<void> startPrayerGeneration({
    required String prayFor,
    required String wisdomToIntegrate,
    required List<String> selectedFaiths,
    bool generateImages = false,
    String? voiceId,
  }) async {
    try {
      // Reset state
      state = const AsyncValue.loading();
      
      // Start new prayer generation
      _currentRunId = await _prayerService.generatePrayer(
        prayFor: prayFor,
        wisdomToIntegrate: wisdomToIntegrate,
        selectedFaiths: selectedFaiths,
        generateImages: generateImages,
        voiceId: voiceId,
      );
      
      // Start streaming updates
      _startStreamingUpdates();
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Start streaming prayer state updates
  void _startStreamingUpdates() {
    if (_currentRunId == null) return;
    
    // Cancel any existing subscription
    _stateSubscription?.cancel();
    
    // Subscribe to prayer state stream
    _stateSubscription = _prayerService
        .streamPrayerStatus(_currentRunId!)
        .listen(
          (prayerState) {
            state = AsyncValue.data(prayerState);
            
            // Check if prayer generation is complete
            if (prayerState.isCompleted) {
              _stateSubscription?.cancel();
            }
          },
          onError: (error, stackTrace) {
            state = AsyncValue.error(error, stackTrace);
          },
        );
  }
  
  // Get latest prayer status
  Future<void> refreshPrayerStatus() async {
    if (_currentRunId == null) return;
    
    try {
      final prayerState = await _prayerService.getPrayerStatus(_currentRunId!);
      state = AsyncValue.data(prayerState);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Cancel prayer generation
  void cancelPrayerGeneration() {
    _stateSubscription?.cancel();
    _currentRunId = null;
    state = const AsyncValue.data(null);
  }
  
  // Load existing prayer by ID
  Future<void> loadPrayer(String runId) async {
    try {
      state = const AsyncValue.loading();
      _currentRunId = runId;
      
      final prayerState = await _prayerService.getPrayerStatus(runId);
      state = AsyncValue.data(prayerState);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
  
  // Get URL for prayer audio
  String getAudioUrl(String? audioPath) {
    if (audioPath == null) return '';
    return _prayerService.getAudioUrl(audioPath);
  }
  
  // Get URL for prayer image
  String getImageUrl(String? imagePath) {
    if (imagePath == null) return '';
    return _prayerService.getImageUrl(imagePath);
  }
  
  @override
  void dispose() {
    _stateSubscription?.cancel();
    super.dispose();
  }
}

// Provider for recently generated prayers
final recentPrayersProvider = StateNotifierProvider<RecentPrayersNotifier, List<Map<String, dynamic>>>((ref) {
  return RecentPrayersNotifier();
});

// Notifier to manage recently generated prayers
class RecentPrayersNotifier extends StateNotifier<List<Map<String, dynamic>>> {
  RecentPrayersNotifier() : super([]) {
    _loadRecentPrayers();
  }
  
  // Load recent prayers from storage
  Future<void> _loadRecentPrayers() async {
    // This would typically load from SharedPreferences or other storage
    // For now, we'll use a mock implementation
    state = [
      {
        'id': 'prayer1',
        'title': 'Prayer for Peace',
        'faiths': ['Christianity', 'Islam', 'Buddhism'],
        'date': DateTime.now().subtract(const Duration(days: 1)).toIso8601String(),
        'hasAudio': true,
        'hasImages': true,
      },
      {
        'id': 'prayer2',
        'title': 'Prayer for Healing',
        'faiths': ['Hinduism', 'Sikhism'],
        'date': DateTime.now().subtract(const Duration(days: 3)).toIso8601String(),
        'hasAudio': true,
        'hasImages': false,
      },
    ];
  }
  
  // Add a prayer to recent prayers
  void addPrayer(Map<String, dynamic> prayer) {
    // Check if prayer already exists
    final existingIndex = state.indexWhere((p) => p['id'] == prayer['id']);
    
    if (existingIndex >= 0) {
      // Update existing prayer
      final updatedPrayers = List<Map<String, dynamic>>.from(state);
      updatedPrayers[existingIndex] = prayer;
      state = updatedPrayers;
    } else {
      // Add new prayer
      state = [prayer, ...state];
    }
    
    // Save to storage (would typically use SharedPreferences)
  }
  
  // Remove a prayer from recent prayers
  void removePrayer(String prayerId) {
    state = state.where((prayer) => prayer['id'] != prayerId).toList();
    
    // Save to storage (would typically use SharedPreferences)
  }
}

// Provider for selected faith traditions
final selectedFaithsProvider = StateNotifierProvider<SelectedFaithsNotifier, List<String>>((ref) {
  return SelectedFaithsNotifier();
});

// Notifier to manage selected faith traditions
class SelectedFaithsNotifier extends StateNotifier<List<String>> {
  SelectedFaithsNotifier() : super([]);
  
  // Toggle a faith tradition
  void toggleFaith(String faith) {
    if (state.contains(faith)) {
      state = state.where((f) => f != faith).toList();
    } else {
      state = [...state, faith];
    }
  }
  
  // Set selected faiths
  void setFaiths(List<String> faiths) {
    state = faiths;
  }
  
  // Clear all selections
  void clearFaiths() {
    state = [];
  }
}
