// services/mcp-servers/fal-ai-mcp/index.js
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";
import { createFalClient } from "fal-client"; // Official fal.ai client

const server = new McpServer({
  name: "fal-ai-mcp",
  description: "Interface for fal.ai services using the official fal-client.",
  version: "1.0.0",
});

// Register tools
server.tool(
  "generate-image",
  "Generate an image using fal.ai models with the official fal-client.",
  {
    // Input schema for the tool
    model: z.string().describe("Model ID to use (e.g., 'fal-ai/illusion-diffusion')"),
    prompt: z.string().describe("Image generation prompt"),
    // Fal.ai often uses 'image_url' for image-to-image, and other specific params
    // Using passthrough for options allows flexibility.
    options: z.object({
        image_url: z.string().optional().describe("Optional URL for image-to-image tasks"),
        // Add other common fal.ai parameters if known, or keep it generic
    }).passthrough().optional().describe("Additional options for the fal.ai model")
  },
  async ({ model, prompt, options = {} }) => {
    // Fal.ai API key should be configured in the environment where this server runs.
    // Typically FAL_KEY or FAL_TOKEN. The createFalClient() should pick it up.
    const fal = createFalClient(); // No auth needed here if FAL_KEY is in env

    try {
      const result = await fal.run(model, {
        // Fal.ai expects input as an object, often with a 'prompt' key.
        // Some models might have different structures.
        // The 'fal.run' method takes the model ID and an 'input' object.
        input: {
          prompt,
          ...options // Spread additional options like image_url, seed, etc.
        }
      });

      // The structure of 'result' can vary by fal.ai model.
      // For image models, it often includes an 'images' array with URLs.
      // It's crucial to check the specific model's output structure.
      // Assuming a common structure like { images: [{ url: "..." }] }
      if (result && result.images && result.images.length > 0 && result.images[0].url) {
        return {
          content: [
            {
              type: "text", // MCP content type
              text: JSON.stringify({ // Store structured data as JSON string
                image_url: result.images[0].url,
                model_used: model,
                input_prompt: prompt,
                // Include any other relevant info from the result if needed
                // e.g., result.seed, result.timings
              })
            }
          ]
        };
      } else {
        // If the response structure is not as expected
        console.error("Fal.ai response structure not as expected:", result);
        return {
          content: [
            {
              type: "text",
              text: JSON.stringify({
                error: "Failed to retrieve image URL from fal.ai response.",
                details: result // Include the raw result for debugging
              })
            }
          ]
        };
      }
    } catch (error) {
      console.error("Error calling fal.ai:", error);
      // Return an error structure that the MCP client can understand
      return {
        content: [
          {
            type: "text",
            text: JSON.stringify({
              error: "An error occurred while calling the fal.ai service.",
              message: error.message || "Unknown error",
              // Optionally include stack or other details for debugging
              // stack: error.stack 
            })
          }
        ],
        // Optionally, MCP might have a more formal way to signal errors.
        // For now, embedding error in content.
      };
    }
  }
);

// Add other tools for fal.ai if needed, e.g., for different types of models.

// Start the server using StdioServerTransport
const transport = new StdioServerTransport(server);
transport.run();

console.log("fal-ai-mcp server started and listening via stdio.");
