from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional

# State model to pass between agents
class PrayerPipelineState(BaseModel):
    prayer_focus: str
    wisdom_theme: str
    selected_faiths: List[str]
    faith_research: Dict[str, Dict] = Field(default_factory=dict)
    sacred_scenes: List[Dict] = Field(default_factory=list)
    scene_prompts: List[str] = Field(default_factory=list)
    generated_images: List[str] = Field(default_factory=list)
    generated_audio: Optional[str] = None
    generated_video: Optional[str] = None
    execution_logs: List[str] = Field(default_factory=list)
