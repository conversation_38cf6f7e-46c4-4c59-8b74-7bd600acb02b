import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'firebase_options.dart';
import 'routes/app_router.dart';
import 'theme/app_theme.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(
    const ProviderScope(
      child: SpiritSyncApp(),
    ),
  );
}

class SpiritSyncApp extends ConsumerWidget {
  const SpiritSyncApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);
    final themeManager = ref.watch(themeProvider);
    
    return DynamicColorBuilder(
      builder: (ColorScheme? lightDynamic, ColorScheme? darkDynamic) {
        // Create theme based on dynamic colors (if available)
        final appTheme = AppTheme(
          lightDynamicScheme: lightDynamic,
          darkDynamicScheme: darkDynamic,
        );
        
        return MaterialApp.router(
          title: 'SpiritSync',
          theme: appTheme.lightTheme,
          darkTheme: appTheme.darkTheme,
          themeMode: themeManager.themeMode,
          routerConfig: router,
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }
}
