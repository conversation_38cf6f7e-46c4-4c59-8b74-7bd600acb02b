{"name": "brave-groq-research", "version": "0.1.0", "description": "MCP server for faith research using Groq and Brave Search", "type": "module", "main": "build/index.js", "scripts": {"build": "tsc && node --eval \"import('fs').then(fs => fs.chmodSync('build/index.js', '755'))\"", "watch": "tsc -w", "start": "node build/index.js"}, "dependencies": {"@modelcontextprotocol/sdk": "1.9.0", "axios": "^1.6.7", "groq-sdk": "^0.3.0"}, "devDependencies": {"@types/node": "^20.11.0", "typescript": "^5.3.3"}}