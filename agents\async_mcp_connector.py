"""
Async MCP Connector for Enhanced Prayer Pipeline
Real implementation of MCP server connections with async support
"""

import asyncio
import json
import logging
import subprocess
import os
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class AsyncMCPConnector:
    """Async connector for MCP servers with real subprocess communication"""
    
    def __init__(self, server_name: str, server_path: str, server_args: List[str] = None):
        """
        Initialize MCP connector
        
        Args:
            server_name: Name of the MCP server
            server_path: Path to the server executable/script
            server_args: Additional arguments for the server
        """
        self.server_name = server_name
        self.server_path = server_path
        self.server_args = server_args or []
        self.process = None
        self.is_connected = False
        
    async def connect(self) -> bool:
        """Connect to the MCP server"""
        try:
            cmd = [self.server_path] + self.server_args
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Initialize MCP protocol
            init_message = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "enhanced-prayer-pipeline",
                        "version": "1.0.0"
                    }
                }
            }
            
            await self._send_message(init_message)
            response = await self._receive_message()
            
            if response and "result" in response:
                self.is_connected = True
                logger.info(f"Connected to MCP server: {self.server_name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to connect to {self.server_name}: {e}")
            
        return False
    
    async def disconnect(self):
        """Disconnect from the MCP server"""
        if self.process:
            try:
                self.process.terminate()
                await asyncio.wait_for(self.process.wait(), timeout=5.0)
            except asyncio.TimeoutError:
                self.process.kill()
                await self.process.wait()
            finally:
                self.process = None
                self.is_connected = False
                logger.info(f"Disconnected from MCP server: {self.server_name}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on the MCP server"""
        if not self.is_connected:
            raise RuntimeError(f"Not connected to MCP server: {self.server_name}")
        
        message = {
            "jsonrpc": "2.0",
            "id": asyncio.current_task().get_name() if asyncio.current_task() else "default",
            "method": "tools/call",
            "params": {
                "name": tool_name,
                "arguments": arguments
            }
        }
        
        try:
            await self._send_message(message)
            response = await self._receive_message()
            
            if "error" in response:
                raise RuntimeError(f"Tool call failed: {response['error']}")
                
            return response.get("result", {})
            
        except Exception as e:
            logger.error(f"Error calling tool {tool_name} on {self.server_name}: {e}")
            raise
    
    async def _send_message(self, message: Dict[str, Any]):
        """Send a JSON-RPC message to the server"""
        if not self.process or not self.process.stdin:
            raise RuntimeError("Process not available for sending")
            
        json_str = json.dumps(message) + "\n"
        self.process.stdin.write(json_str.encode())
        await self.process.stdin.drain()
    
    async def _receive_message(self) -> Dict[str, Any]:
        """Receive a JSON-RPC message from the server"""
        if not self.process or not self.process.stdout:
            raise RuntimeError("Process not available for receiving")
            
        line = await self.process.stdout.readline()
        if not line:
            raise RuntimeError("No response from server")
            
        try:
            return json.loads(line.decode().strip())
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode response: {line.decode()}")
            raise RuntimeError(f"Invalid JSON response: {e}")


class AsyncMCPManager:
    """Manager for multiple MCP server connections"""
    
    def __init__(self):
        self.connectors: Dict[str, AsyncMCPConnector] = {}
        self.initialized = False
        
    async def initialize(self) -> bool:
        """Initialize the MCP manager (compatibility method for tests)"""
        self.initialized = True
        return True
        
    async def add_server(self, name: str, server_path: str, server_args: List[str] = None) -> bool:
        """Add and connect to an MCP server"""
        connector = AsyncMCPConnector(name, server_path, server_args)
        success = await connector.connect()
        
        if success:
            self.connectors[name] = connector
            return True
        else:
            await connector.disconnect()
            return False
    
    async def remove_server(self, name: str):
        """Remove and disconnect from an MCP server"""
        if name in self.connectors:
            await self.connectors[name].disconnect()
            del self.connectors[name]
    
    async def call_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """Call a tool on a specific MCP server"""
        if server_name not in self.connectors:
            raise ValueError(f"Server {server_name} not found")
            
        return await self.connectors[server_name].call_tool(tool_name, arguments)
    
    async def shutdown_all(self):
        """Shutdown all MCP server connections"""
        tasks = [connector.disconnect() for connector in self.connectors.values()]
        await asyncio.gather(*tasks, return_exceptions=True)
        self.connectors.clear()
    
    def is_server_connected(self, server_name: str) -> bool:
        """Check if a server is connected"""
        return server_name in self.connectors and self.connectors[server_name].is_connected

async def setup_prayer_mcp_servers() -> AsyncMCPManager:
    """Setup all MCP servers for the prayer pipeline"""
    manager = AsyncMCPManager()
    
    # Get the project root directory
    project_root = Path(__file__).parent.parent
    
    # Setup other MCP servers (fal-ai, context7, etc.)
    # Add similar setup for other servers as needed
    
    return manager
