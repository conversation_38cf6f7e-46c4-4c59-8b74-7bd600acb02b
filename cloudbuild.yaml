# Google Cloud Build configuration for Vibe Prayer OmniFaith
steps:
  # Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:$COMMIT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:latest'
      - '.'

  # Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:$COMMIT_SHA'

  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:latest'

  # Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'vibe-prayer-omnifaith'
      - '--image'
      - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--timeout'
      - '3600'
      - '--concurrency'
      - '10'
      - '--max-instances'
      - '100'
      - '--set-env-vars'
      - 'ENVIRONMENT=production,PYTHONPATH=/app,NO_TORCH_COMPILE=1'

# Configure image and timeout
images:
  - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:$COMMIT_SHA'
  - 'gcr.io/$PROJECT_ID/vibe-prayer-omnifaith:latest'

options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'

timeout: '1800s'
