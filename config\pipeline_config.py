import json
import os
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, Field

class ModelConfig(BaseModel):
    """Configuration for LLM models used in the pipeline."""
    orchestrator: str = "google-gla:gemini-1.5-pro"
    scene_writer: str = "google-gla:gemini-1.5-pro"
    fast_writer: str = "google-gla:gemini-1.5-flash"
    audio_generator: str = "google-gla:gemini-1.5-pro"

class ServerConfig(BaseModel):
    """Configuration for MCP servers."""
    fal_ai: str = "fal-ai-mcp"
    replicate: str = "replicate-mcp"
    context7: str = "context7-mcp"
    brave_groq: str = "brave-groq-research"

class ApiKeyConfig(BaseModel):
    """Configuration for API keys."""
    gemini: Optional[str] = None
    fal_ai: Optional[str] = None
    replicate: Optional[str] = None
    brave: Optional[str] = None
    groq: Optional[str] = None

class PipelineConfig(BaseModel):
    """Main configuration for the prayer pipeline."""
    api_keys: ApiKeyConfig = Field(default_factory=ApiKeyConfig)
    models: ModelConfig = Field(default_factory=ModelConfig)
    servers: ServerConfig = Field(default_factory=ServerConfig)
    
    # Additional configuration options
    parallel_execution: bool = True
    max_retries: int = 3
    timeout_seconds: int = 60
    debug_mode: bool = False
    
    # Feature flags
    enable_image_generation: bool = True
    enable_audio_generation: bool = True
    enable_video_generation: bool = True
    
    # Storage options
    save_intermediate_results: bool = True
    result_directory: str = "Saved_Prayers"

class ConfigManager:
    """
    Manager for loading, validating and accessing pipeline configuration.
    Supports environment variables, config files, and runtime overrides.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the configuration manager.
        
        Args:
            config_path: Path to configuration file. If None, will look in default locations.
        """
        self.config_path = config_path or self._find_config_file()
        self.config = self._load_config()
    
    def _find_config_file(self) -> str:
        """Find the configuration file in default locations."""
        # Check several possible locations
        possible_paths = [
            "config/pipeline_config.json",
            os.path.join(os.getcwd(), "config/pipeline_config.json"),
            os.path.expanduser("~/.prayer_pipeline/config.json")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # If no config file exists, use a default path
        return "config/pipeline_config.json"
    
    def _load_config(self) -> PipelineConfig:
        """
        Load configuration from file and environment variables.
        Environment variables take precedence over file config.
        """
        # Start with default config
        config_data = {}
        
        # Try to load from file
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    config_data = json.load(f)
        except Exception as e:
            print(f"Warning: Could not load config file: {e}")
        
        # Load API keys from environment variables if available
        api_keys = config_data.get("api_keys", {})
        
        if os.environ.get("GEMINI_API_KEY"):
            api_keys["gemini"] = os.environ.get("GEMINI_API_KEY")
        
        if os.environ.get("FAL_API_KEY"):
            api_keys["fal_ai"] = os.environ.get("FAL_API_KEY")
        
        if os.environ.get("REPLICATE_API_TOKEN"):
            api_keys["replicate"] = os.environ.get("REPLICATE_API_TOKEN")
        
        if os.environ.get("BRAVE_API_KEY"):
            api_keys["brave"] = os.environ.get("BRAVE_API_KEY")
        
        if os.environ.get("GROQ_API_KEY"):
            api_keys["groq"] = os.environ.get("GROQ_API_KEY")
        
        # Update with environment-sourced API keys
        if api_keys:
            config_data["api_keys"] = api_keys
        
        # Create a PipelineConfig object
        return PipelineConfig(**config_data)
    
    def get_config(self) -> PipelineConfig:
        """Get the current configuration."""
        return self.config
    
    def update_config(self, updates: Dict[str, Any]) -> PipelineConfig:
        """
        Update configuration with new values.
        
        Args:
            updates: Dictionary of configuration updates.
            
        Returns:
            Updated configuration.
        """
        # Create a dictionary from the current config
        config_dict = self.config.dict()
        
        # Update with new values
        self._deep_update(config_dict, updates)
        
        # Create a new config object
        self.config = PipelineConfig(**config_dict)
        return self.config
    
    def _deep_update(self, target: Dict[str, Any], source: Dict[str, Any]) -> None:
        """Recursively update a nested dictionary."""
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._deep_update(target[key], value)
            else:
                target[key] = value
    
    def save_config(self, path: Optional[str] = None) -> None:
        """
        Save the current configuration to a file.
        
        Args:
            path: Path to save to. If None, uses the current config path.
        """
        save_path = path or self.config_path
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        
        # Save config as JSON
        with open(save_path, 'w') as f:
            json.dump(self.config.dict(), f, indent=2)
        
        print(f"Configuration saved to {save_path}")
    
    def get_api_key(self, service: str) -> Optional[str]:
        """
        Get an API key for a specific service.
        
        Args:
            service: Service name (e.g., 'gemini', 'fal_ai', 'replicate').
            
        Returns:
            API key or None if not found.
        """
        return getattr(self.config.api_keys, service, None)

# Create the initial configuration file
def initialize_config(config_path: str = "config/pipeline_config.json") -> None:
    """
    Initialize a default configuration file.
    
    Args:
        config_path: Path to save the configuration file.
    """
    # Create a default configuration
    default_config = PipelineConfig()
    
    # Ensure directory exists
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    # Save to file
    with open(config_path, 'w') as f:
        json.dump(default_config.dict(), f, indent=2)
    
    print(f"Default configuration created at {config_path}")

# Example usage
if __name__ == "__main__":
    # Initialize a default configuration file
    initialize_config()
    
    # Load the configuration
    config_manager = ConfigManager()
    config = config_manager.get_config()
    
    print("Current configuration:")
    print(f"Models: {config.models}")
    print(f"API Keys configured: {[k for k, v in config.api_keys.dict().items() if v]}")
    
    # Example update
    config_manager.update_config({
        "models": {
            "scene_writer": "google-gla:gemini-2.5-pro"  # Hypothetical future model
        },
        "debug_mode": True
    })
    
    # Save updated configuration
    config_manager.save_config()
