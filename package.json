{"name": "vibe-prayer-omnifaith", "version": "1.7.7", "description": "AI-powered multi-faith prayer generation application with TTS and image generation", "main": "tts_service.js", "scripts": {"start": "python main.py", "tts": "node tts_service.js", "install-mcp": "npm install --prefix services/mcp-servers/brave-groq-research && npm install --prefix services/mcp-servers/replicate-mcp", "build-mcp": "cd services/mcp-servers/brave-groq-research && npm run build", "dev": "python main.py", "test": "python -m pytest test_pydantic_agent.py", "docker-build": "docker build -t vibe-prayer-omnifaith .", "docker-run": "docker run -it --env-file .env -v $(pwd)/output:/app/output vibe-prayer-omnifaith"}, "keywords": ["prayer", "ai", "spiritual", "tts", "multi-faith", "langgraph", "pydantic"], "author": "Higher Power AI", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"axios": "^1.7.2", "dotenv": "^16.4.5"}, "devDependencies": {"nodemon": "^3.1.0"}, "repository": {"type": "git", "url": "https://github.com/higherpowerai/Vibe_Prayer_OmniFaith_1.77.git"}, "bugs": {"url": "https://github.com/higherpowerai/Vibe_Prayer_OmniFaith_1.77/issues"}, "homepage": "https://github.com/higherpowerai/Vibe_Prayer_OmniFaith_1.77#readme"}