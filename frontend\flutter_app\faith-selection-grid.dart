import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../providers/prayer_provider.dart';

class FaithSelectionGrid extends ConsumerWidget {
  const FaithSelectionGrid({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedFaiths = ref.watch(selectedFaithsProvider);
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 3.0,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: AppConstants.faithTraditions.length,
      itemBuilder: (context, index) {
        final faith = AppConstants.faithTraditions[index];
        final isSelected = selectedFaiths.contains(faith);
        final faithColor = AppConstants.faithColors[faith] ?? theme.colorScheme.primary;
        
        return _buildFaithCard(
          context,
          faith: faith,
          color: faithColor,
          isSelected: isSelected,
          onTap: () {
            ref.read(selectedFaithsProvider.notifier).toggleFaith(faith);
          },
        );
      },
    );
  }
  
  Widget _buildFaithCard(
    BuildContext context, {
    required String faith,
    required Color color,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : color.withOpacity(0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected ? color : Colors.transparent,
                border: Border.all(
                  color: color,
                  width: 1,
                ),
              ),
              child: isSelected
                  ? const Icon(
                      Icons.check,
                      size: 12,
                      color: Colors.white,
                    )
                  : null,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                faith,
                style: TextStyle(
                  color: isSelected ? color : null,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Example extension for FaithSelectionGrid to show more details
class FaithSelectionGridWithInfo extends ConsumerWidget {
  const FaithSelectionGridWithInfo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final selectedFaiths = ref.watch(selectedFaithsProvider);
    
    return Column(
      children: [
        const FaithSelectionGrid(),
        const SizedBox(height: 16),
        
        // Show information about selected faiths
        if (selectedFaiths.isNotEmpty) ...[
          Text(
            'Selected Traditions',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          ...selectedFaiths.map((faith) => _buildFaithInfo(context, faith)),
        ],
      ],
    );
  }
  
  Widget _buildFaithInfo(BuildContext context, String faith) {
    final theme = Theme.of(context);
    final description = AppConstants.faithDescriptions[faith] ?? '';
    final color = AppConstants.faithColors[faith] ?? theme.colorScheme.primary;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: color,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  faith,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}
