# Task ID: 54
# Title: Add Research Flag to Add-Task Command
# Status: done
# Dependencies: None
# Priority: medium
# Description: Enhance the add-task command with a --research flag that allows users to perform quick research on the task topic before finalizing task creation.
# Details:
Modify the existing add-task command to accept a new optional flag '--research'. When this flag is provided, the system should pause the task creation process and invoke the Perplexity research functionality (similar to Task #51) to help users gather information about the task topic before finalizing the task details. The implementation should:

1. Update the command parser to recognize the new --research flag
2. When the flag is present, extract the task title/description as the research topic
3. Call the Perplexity research functionality with this topic
4. Display research results to the user
5. Allow the user to refine their task based on the research (modify title, description, etc.)
6. Continue with normal task creation flow after research is complete
7. Ensure the research results can be optionally attached to the task as reference material
8. Add appropriate help text explaining this feature in the command help

The implementation should leverage the existing Perplexity research command from Task #51, ensuring code reuse where possible.

# Test Strategy:
Testing should verify both the functionality and usability of the new feature:

1. Unit tests:
   - Verify the command parser correctly recognizes the --research flag
   - Test that the research functionality is properly invoked with the correct topic
   - Ensure task creation proceeds correctly after research is complete

2. Integration tests:
   - Test the complete flow from command invocation to task creation with research
   - Verify research results are properly attached to the task when requested
   - Test error handling when research API is unavailable

3. Manual testing:
   - Run the command with --research flag and verify the user experience
   - Test with various task topics to ensure research is relevant
   - Verify the help documentation correctly explains the feature
   - Test the command without the flag to ensure backward compatibility

4. Edge cases:
   - Test with very short/vague task descriptions
   - Test with complex technical topics
   - Test cancellation of task creation during the research phase
