name: spirit_sync
description: A multi-faith prayer generator app connecting to a LangGraph backend.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # State management
  flutter_riverpod: ^2.4.0
  equatable: ^2.0.5

  # Routing
  go_router: ^11.1.2

  # Firebase
  firebase_core: ^2.15.1
  firebase_auth: ^4.9.0
  google_sign_in: ^6.1.5
  sign_in_with_apple: ^5.0.0
  github_sign_in: ^0.0.5
  
  # Network
  dio: ^5.3.2
  web_socket_channel: ^2.4.0
  connectivity_plus: ^5.0.1

  # UI Enhancements
  dynamic_color: ^1.6.6
  google_fonts: ^5.1.0
  flutter_markdown: ^0.6.17
  cached_network_image: ^3.3.0
  lottie: ^2.6.0
  shimmer: ^3.0.0

  # Media
  just_audio: ^0.9.35
  photo_view: ^0.14.0
  
  # Storage
  shared_preferences: ^2.2.1
  flutter_secure_storage: ^8.0.0
  path_provider: ^2.1.1

  # Utils
  intl: ^0.18.1
  share_plus: ^7.1.0
  package_info_plus: ^4.1.0
  url_launcher: ^6.1.14

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.3
  build_runner: ^2.4.6
  json_serializable: ^6.7.1
  mockito: ^5.4.2

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/animations/

  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins-Bold.ttf
          weight: 700
        - asset: assets/fonts/Poppins-Light.ttf
          weight: 300
        - asset: assets/fonts/Poppins-Medium.ttf
          weight: 500
