import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'dart:math';

class AudioPlayerWidget extends StatefulWidget {
  final String audioUrl;
  final String title;
  
  const AudioPlayerWidget({
    Key? key,
    required this.audioUrl,
    required this.title,
  }) : super(key: key);

  @override
  State<AudioPlayerWidget> createState() => _AudioPlayerWidgetState();
}

class _AudioPlayerWidgetState extends State<AudioPlayerWidget> {
  late AudioPlayer _audioPlayer;
  bool _isLoading = true;
  bool _isPlaying = false;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  
  @override
  void initState() {
    super.initState();
    _initAudioPlayer();
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }
  
  // Initialize audio player
  Future<void> _initAudioPlayer() async {
    _audioPlayer = AudioPlayer();
    
    try {
      // Set URL
      await _audioPlayer.setUrl(widget.audioUrl);
      
      // Listen to player state changes
      _audioPlayer.playerStateStream.listen((state) {
        final isPlaying = state.playing;
        final processingState = state.processingState;
        
        setState(() {
          _isPlaying = isPlaying && processingState == ProcessingState.ready;
          _isLoading = processingState == ProcessingState.loading || 
                       processingState == ProcessingState.buffering;
        });
      });
      
      // Listen to duration changes
      _audioPlayer.durationStream.listen((duration) {
        if (duration != null) {
          setState(() {
            _duration = duration;
          });
        }
      });
      
      // Listen to position changes
      _audioPlayer.positionStream.listen((position) {
        setState(() {
          _position = position;
        });
      });
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error initializing audio player: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }
  
  // Play or pause audio
  void _playPause() {
    if (_isPlaying) {
      _audioPlayer.pause();
    } else {
      _audioPlayer.play();
    }
  }
  
  // Seek to position
  void _seek(Duration position) {
    _audioPlayer.seek(position);
  }
  
  // Format duration
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Audio wave visualization
          SizedBox(
            height: 80,
            child: _isLoading 
                ? const Center(child: CircularProgressIndicator())
                : _buildWaveform(theme),
          ),
          const SizedBox(height: 16),
          
          // Seek bar
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 6),
              overlayShape: const RoundSliderOverlayShape(overlayRadius: 14),
              thumbColor: theme.colorScheme.primary,
              activeTrackColor: theme.colorScheme.primary,
              inactiveTrackColor: theme.colorScheme.surfaceVariant,
            ),
            child: Slider(
              min: 0,
              max: _duration.inMilliseconds.toDouble(),
              value: min(_position.inMilliseconds.toDouble(), _duration.inMilliseconds.toDouble()),
              onChanged: (value) {
                _seek(Duration(milliseconds: value.toInt()));
              },
            ),
          ),
          
          // Time indicators
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDuration(_position),
                  style: theme.textTheme.bodySmall,
                ),
                Text(
                  _formatDuration(_duration),
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          
          // Playback controls
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Skip back 10s
              IconButton(
                icon: const Icon(Icons.replay_10),
                onPressed: () {
                  _seek(_position - const Duration(seconds: 10));
                },
              ),
              const SizedBox(width: 16),
              
              // Play/pause button
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: Icon(
                    _isPlaying ? Icons.pause : Icons.play_arrow,
                    color: theme.colorScheme.onPrimary,
                    size: 32,
                  ),
                  onPressed: _isLoading ? null : _playPause,
                ),
              ),
              const SizedBox(width: 16),
              
              // Skip forward 10s
              IconButton(
                icon: const Icon(Icons.forward_10),
                onPressed: () {
                  _seek(_position + const Duration(seconds: 10));
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  // Build audio waveform visualization
  Widget _buildWaveform(ThemeData theme) {
    // Generate some random bars for visualization
    // In a real app, this would be based on audio frequencies
    const barCount = 40;
    final random = Random(widget.title.hashCode);
    final heights = List.generate(barCount, (_) => random.nextDouble() * 0.8 + 0.2);
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(barCount, (index) {
        final playedRatio = _duration.inMilliseconds > 0 
            ? _position.inMilliseconds / _duration.inMilliseconds
            : 0.0;
        final isPlayed = index < barCount * playedRatio;
        
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: 3,
          height: 60 * heights[index],
          decoration: BoxDecoration(
            color: isPlayed 
                ? theme.colorScheme.primary
                : theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(2),
          ),
        );
      }),
    );
  }
}
