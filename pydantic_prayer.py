"""
PydanticAI-based Prayer Application with Async Video Pipeline
Alternative entry point using PydanticAI agent framework with enhanced media capabilities
"""

import os
import json
import asyncio
import datetime
import logging
from dotenv import load_dotenv
from typing import List, Dict, Optional
from pathlib import Path

# Import PydanticAI components with async support
from agents.pydantic_ai_state import PrayerPipelineState
from agents.enhanced_prayer_pipeline import EnhancedPrayerPipeline
from agents.faith_research_agent import FaithResearchAgent, FaithResearchParams

# Load environment variables
load_dotenv()

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Define available spiritual movements
SPIRITUAL_MOVEMENTS = [
    'Christianity',
    'Islam',
    'Hinduism',
    'Buddhism',
    'Sikhism',
    'Judaism',
    'Bahá\'í Faith',
    'Confucianism',
    'Jainism',
    'Shinto',
    'Taoism',
    'Zoroastrianism',
    'Cherokee Spirituality',
    'Wicca'
]

def get_user_input() -> Dict:
    """
    Get and validate user input for prayer generation with enhanced media options.

    Returns:
        Dictionary with validated user input including async video options
    """
    pray_for_input = input("Who or what should we pray for? ")
    wisdom_input = input("What spiritual wisdom should we integrate? ")

    # Display available spiritual traditions
    print("\nAvailable spiritual traditions:")
    for i, faith in enumerate(SPIRITUAL_MOVEMENTS, 1):
        print(f"{i}. {faith}")

    # Get faith selections
    selected_faiths = []
    while not selected_faiths:
        faith_input = input("\nEnter the numbers of the faiths to include (comma-separated, or 'all' for all): ")

        if faith_input.lower().strip() == 'all':
            print("[INFO] You selected ALL spiritual traditions.")
            selected_faiths = SPIRITUAL_MOVEMENTS.copy()
        else:
            try:
                # Parse the comma-separated numbers
                faith_indices = [int(idx.strip()) for idx in faith_input.split(',') if idx.strip()]
                # Convert to faith names, handling out-of-range indices
                selected_faiths = [SPIRITUAL_MOVEMENTS[idx-1] for idx in faith_indices
                                if 1 <= idx <= len(SPIRITUAL_MOVEMENTS)]

                if not selected_faiths:
                    print("[WARNING] No valid faith selections found. Please try again.")
                else:
                    print(f"[INFO] You selected: {', '.join(selected_faiths)}")
            except ValueError:
                print("[WARNING] Invalid input format. Please use numbers separated by commas.")

    # Enhanced media generation options
    print("\n" + "=" * 50)
    print("           ENHANCED MEDIA OPTIONS")
    print("=" * 50)
    
    # Ask about image generation
    generate_images = False
    image_input = input("\nWould you like to generate image prayers? (yes/no): ")
    if image_input.lower().strip() in ["yes", "y", "true", "1"]:
        generate_images = True
        print("[INFO] ✅ Image prayer generation enabled.")
    else:
        print("[INFO] ❌ Image prayer generation disabled.")

    # Ask about video generation with async support
    generate_video = False
    video_input = input("\nWould you like to generate prayer videos using async pipeline? (yes/no): ")
    if video_input.lower().strip() in ["yes", "y", "true", "1"]:
        generate_video = True
        print("[INFO] ✅ Async prayer video generation enabled.")
        print("[INFO] 🎬 Videos will be generated in parallel using Replicate's Veo-2 model.")
    else:
        print("[INFO] ❌ Prayer video generation disabled.")
    
    # Ask about audio generation
    generate_audio = False
    audio_input = input("\nWould you like to generate prayer audio? (yes/no): ")
    if audio_input.lower().strip() in ["yes", "y", "true", "1"]:
        generate_audio = True
        print("[INFO] ✅ Prayer audio generation enabled.")
    else:
        print("[INFO] ❌ Prayer audio generation disabled.")

    # Ask about parallel processing preference
    use_parallel = True
    if generate_video or generate_images or generate_audio:
        parallel_input = input("\nUse parallel processing for faster media generation? (yes/no): ")
        if parallel_input.lower().strip() in ["no", "n", "false", "0"]:
            use_parallel = False
            print("[INFO] ⚡ Sequential processing enabled.")
        else:
            print("[INFO] 🚀 Parallel processing enabled for optimal performance.")

    # Return validated input with enhanced options
    return {
        "pray_for": pray_for_input,
        "wisdom_to_integrate": wisdom_input,
        "selected_faiths": selected_faiths,
        "generate_images": generate_images,
        "generate_video": generate_video,
        "audio_conversion_requested": generate_audio,
        "parallel_processing_enabled": use_parallel
    }

def ensure_output_directory() -> str:
    """Create a timestamped output directory for this run"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    run_dir = f"Saved_Prayers/Run_{timestamp}"
    os.makedirs(run_dir, exist_ok=True)
    return run_dir

def save_prayer_file(output_dir: str, content: str, filename: str) -> str:
    """Save prayer content to a file"""
    filepath = os.path.join(output_dir, filename)
    with open(filepath, "w", encoding="utf-8") as f:
        f.write(content)
    return filepath

def save_media_links(output_dir: str, results: Dict) -> str:
    """Save media links to a convenient reference file"""
    media_links = []
    
    # Video links
    if "generated_video_url" in results and results["generated_video_url"]:
        media_links.append(f"# Prayer Video\n")
        media_links.append(f"Primary Video: {results['generated_video_url']}\n\n")
    
    if "all_generated_videos" in results and results["all_generated_videos"]:
        media_links.append(f"## All Generated Videos\n")
        for i, video in enumerate(results["all_generated_videos"], 1):
            if "video_url" in video:
                media_links.append(f"Video {i}: {video['video_url']}\n")
        media_links.append("\n")
    
    # Image links
    if "generated_image_urls" in results and results["generated_image_urls"]:
        media_links.append(f"# Prayer Images\n")
        for i, url in enumerate(results["generated_image_urls"], 1):
            media_links.append(f"Image {i}: {url}\n")
        media_links.append("\n")
    
    # Audio links
    if "generated_audio_url" in results and results["generated_audio_url"]:
        media_links.append(f"# Prayer Audio\n")
        media_links.append(f"Audio: {results['generated_audio_url']}\n\n")
    
    if media_links:
        content = "# Prayer Media Links\n\n" + "".join(media_links)
        return save_prayer_file(output_dir, content, "Media_Links.md")
    
    return None

def display_execution_summary(results: Dict) -> None:
    """Display an enhanced summary of the execution results with async pipeline info"""
    print("\n" + "=" * 60)
    print("         🙏 ENHANCED PRAYER PIPELINE SUMMARY 🙏")
    print("=" * 60)

    # Check for errors
    if "error" in results:
        print(f"\n⚠️ [ERROR] {results['error']}")
        if "pipeline_logs" in results:
            print("\nExecution logs:")
            for log in results["pipeline_logs"]:
                print(f"  - {log}")
        return

    # Display main prayer information
    print(f"\n🙏 PRAYER FOCUS")
    print(f"Prayer for: {results.get('prayer_focus', 'Unknown')}")
    print(f"Wisdom theme: {results.get('wisdom_theme', 'Unknown')}")
    print(f"Faiths included: {', '.join(results.get('selected_faiths', []))}")

    # Display research summary
    if "research_summary" in results and results["research_summary"]:
        print(f"\n📚 RESEARCH SUMMARY")
        for faith, research in results["research_summary"].items():
            print(f"  ✅ {faith}")

    # Display generated scenes
    if "sacred_scenes_generated" in results and results["sacred_scenes_generated"]:
        print(f"\n🎬 SACRED SCENES")
        for i, scene in enumerate(results["sacred_scenes_generated"], 1):
            print(f"  ✅ Scene {i}: {scene.get('title', 'Untitled')}")

    # Enhanced media generation results
    media_generated = False
    
    # Video results with async details
    if "generated_video_url" in results and results["generated_video_url"]:
        print(f"\n🎥 ASYNC VIDEO GENERATION")
        print(f"  ✅ Primary Video: {results['generated_video_url']}")
        media_generated = True
        
        # Show all videos if multiple were generated
        if "all_generated_videos" in results and len(results["all_generated_videos"]) > 1:
            print(f"  📹 Total videos generated: {len(results['all_generated_videos'])}")
            
        # Show any video generation errors
        if "video_generation_errors" in results and results["video_generation_errors"]:
            print(f"  ⚠️ Video generation errors: {len(results['video_generation_errors'])}")

    # Image results
    if "generated_image_urls" in results and results["generated_image_urls"]:
        print(f"\n🖼️ IMAGES")
        for i, url in enumerate(results["generated_image_urls"], 1):
            print(f"  ✅ Image {i}: {url}")
        media_generated = True

    # Audio results
    if "generated_audio_url" in results and results["generated_audio_url"]:
        print(f"\n🔊 AUDIO")
        print(f"  ✅ {results['generated_audio_url']}")
        media_generated = True

    # Media summary
    if "media_summary" in results and results["media_summary"]:
        print(f"\n📊 MEDIA SUMMARY")
        summary = results["media_summary"]
        if "videos" in summary:
            print(f"  🎬 Videos: {summary['videos']['count']} generated")
        if "images" in summary:
            print(f"  🖼️ Images: {summary['images']['count']} generated")
        if "audio" in summary:
            print(f"  🔊 Audio: 1 generated")

    # Execution performance logs
    if "pipeline_logs" in results and results["pipeline_logs"]:
        print("\n📋 EXECUTION LOGS")
        for log in results["pipeline_logs"]:
            print(f"  - {log}")

    # Performance summary
    if media_generated:
        print(f"\n⚡ PERFORMANCE")
        print(f"  🚀 Async pipeline enabled for parallel media generation")
        print(f"  🔗 MCP servers used for video generation via Replicate")

    print("\n" + "=" * 60)

async def main():
    """Main entry point for the enhanced PydanticAI-based prayer application"""
    
    # Display welcome message
    print("\n" + "=" * 60)
    print("   🙏  ENHANCED PYDANTIC-AI PRAYER SYSTEM  🙏")
    print("=" * 60)
    print("\nThis application uses PydanticAI with async video pipeline to orchestrate")
    print("prayer generation from multiple spiritual traditions with enhanced media")
    print("capabilities including parallel video, image, and audio generation.")
    print("\n🎬 NEW: Async video generation using Replicate's Veo-2 model")
    print("🚀 NEW: Parallel media processing for optimal performance")

    pipeline = None
    try:
        # Check for required API keys
        required_keys = {
            "GOOGLE_API_KEY": "for Gemini models (required)",
            "REPLICATE_API_TOKEN": "for video generation (optional)",
            "FAL_KEY": "for image generation (optional)",
            "BRAVE_API_KEY": "for research (optional)"
        }
        
        missing_keys = []
        essential_keys = []
        
        for key, description in required_keys.items():
            if not os.environ.get(key):
                missing_keys.append(f"{key} ({description})")
                if "required" in description:
                    essential_keys.append(key)
        
        if missing_keys:
            print("\n⚠️  API KEY STATUS:")
            for key in missing_keys:
                print(f"  - ❌ {key}")
            
            if essential_keys:
                print(f"\n🚨 Essential keys missing: {', '.join(essential_keys)}")
                print("The application may not function properly without these keys.")
            else:
                print("\n✅ Essential keys are available. Optional features may be limited.")
                
            proceed = input("\nDo you want to proceed anyway? (yes/no): ")
            if proceed.lower() not in ["yes", "y", "1"]:
                print("Exiting application.")
                return
        else:
            print("\n✅ All API keys are configured!")
        
        # Get user input with enhanced options
        user_input = get_user_input()
        
        # Create output directory
        output_dir = ensure_output_directory()
        print(f"\n[INFO] 💾 Output will be saved to: {output_dir}")
        
        # Initialize the enhanced prayer pipeline
        config = {
            "gemini_api_key": os.environ.get("GOOGLE_API_KEY", ""),
        }
        
        print("\n[INFO] 🔧 Initializing enhanced PydanticAI prayer pipeline...")
        print("[INFO] 🔗 Setting up async MCP connections...")
        pipeline = EnhancedPrayerPipeline(config=config)
        
        # Execute the pipeline with enhanced features
        print("\n[INFO] ⚡ Executing enhanced prayer generation pipeline...")
        if user_input.get('generate_video') or user_input.get('generate_images'):
            print("[INFO] 🎬 Async media generation enabled - this may take longer but will run in parallel")
        
        results = await pipeline.run(user_input)
        
        # Save main prayer results
        if "unified_prayer_markdown" in results:
            md_path = save_prayer_file(
                output_dir, 
                results["unified_prayer_markdown"],
                "Unified_Prayer.md"
            )
            print(f"[INFO] 📝 Prayer saved to: {md_path}")
        
        # Save media links for easy access
        media_links_path = save_media_links(output_dir, results)
        if media_links_path:
            print(f"[INFO] 🔗 Media links saved to: {media_links_path}")
        
        # Display enhanced summary
        display_execution_summary(results)
        
    except KeyboardInterrupt:
        print("\n\n⏹️ Prayer generation canceled by user.")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}")
        print(f"\n[ERROR] An unexpected error occurred: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Cleanup async resources
        if pipeline:
            try:
                await pipeline.cleanup()
                print("\n[INFO] 🧹 Async resources cleaned up successfully")
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
    
    print("\n" + "=" * 60)
    print("              🙏 PROCESS COMPLETE 🙏")
    print("=" * 60)

if __name__ == "__main__":
    # Check for uvloop for better async performance (Unix only)
    try:
        import uvloop
        uvloop.install()
        print("[INFO] 🚀 uvloop installed for enhanced async performance")
    except ImportError:
        pass  # Windows or uvloop not available
    
    asyncio.run(main())
