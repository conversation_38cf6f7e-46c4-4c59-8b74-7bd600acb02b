"""
Research Nodes (Groq MCP Version)
LangGraph nodes for research functionality using Groq MCP server.
"""

from typing import Dict, Optional
import re
from models.prayer_state import PrayerState
from modelcontextprotocol import use_mcp_tool

def perform_research_node(state: PrayerState) -> Dict:
    """
    Perform research for selected faith traditions using Groq MCP server.

    Args:
        state: Current workflow state

    Returns:
        Updated state with research results
    """
    print("\n--- Performing Faith-Specific Research (Groq MCP) ---")

    prayer_focus = state.get('prayer_focus_theme', '')
    wisdom_focus = prayer_focus  # Default to using prayer focus

    if not prayer_focus:
        error_msg = "Cannot perform research: prayer focus theme not specified."
        print(f"   [ERROR] {error_msg}")
        return {"error_message": error_msg}

    # Get the list of spiritual movements to research
    movements = state.get('spiritual_movements_to_process', []).copy()
    if not movements:
        print("   [WARNING] No spiritual movements specified for research.")
        # Return complete empty state to avoid graph state issues
        return {
            "search_context": "",
            "faith_research": {},
            "religious_terms": {},
            "error_message": "Failed to perform research: No spiritual movements to process."
        }

    try:
        # Get general context from MCP
        general_result = use_mcp_tool(
            server_name="brave-groq-research",
            tool_name="general_research",
            arguments={
                "prayer_focus": prayer_focus,
                "wisdom_focus": wisdom_focus,
                "model_type": "llama-3.3-70B-vers"
            }
        )
        general_context = general_result["content"][0]["text"] if general_result.get("content") else ""

        print("   [INFO] Using general context from Groq MCP research")

        # Research each faith using MCP
        print(f"   [INFO] Researching terms for {len(movements)} selected faith traditions...")
        faith_research = {}
        religious_terms = state.get('religious_terms', {}).copy()

        import divine_names
        for faith in movements:
            result = use_mcp_tool(
                server_name="brave-groq-research",
                tool_name="faith_research",
                arguments={
                    "faith": faith,
                    "prayer_focus": prayer_focus,
                    "wisdom_focus": wisdom_focus,
                    "model_type": "llama-3.3-70B-vers"  # Use best model for faith research
                }
            )
            
            if result and result.get("content"):
                research_data = parse_mcp_response(result["content"][0]["text"])
                print(f"   [DEBUG] Found {len(research_data.get('divine_names', []))} divine names for {faith}")

                # Heuristic: determine main concept for fallback divine names
                focus_lower = prayer_focus.lower()
                if "heal" in focus_lower:
                    concept = "healing"
                elif "peace" in focus_lower:
                    concept = "peace"
                elif "wisdom" in focus_lower:
                    concept = "wisdom"
                elif "protect" in focus_lower or "safe" in focus_lower:
                    concept = "protection"
                elif "compassion" in focus_lower or "mercy" in focus_lower:
                    concept = "compassion"
                else:
                    concept = "creator"

                # Supplement missing divine_names or sacred_terms with divine_names.py
                if not research_data.get('divine_names'):
                    research_data['divine_names'] = divine_names.DIVINE_NAMES.get(concept, {}).get('specific', [])
                if not research_data.get('sacred_terms'):
                    # Optionally, sacred_terms could also be filled from divine_names.py or left empty
                    research_data['sacred_terms'] = []

                # Always set universal_with_specific_divine_name
                research_data["universal_with_specific_divine_name"] = divine_names.get_universal_with_specific(concept)

                # Combine divine names and sacred terms for backward compatibility
                terms = []
                terms.extend(research_data.get('divine_names', []))
                terms.extend(research_data.get('sacred_terms', []))
                if terms:
                    religious_terms[faith] = terms
                    print(f"   [INFO] Found {len(terms)} terms for {faith}")

                faith_research[faith] = research_data

        print("   [INFO] Research completed successfully.")
        return {
            "search_context": general_context,
            "religious_terms": religious_terms,
            "faith_research": faith_research
        }

    except Exception as e:
        error_msg = f"Error during research: {str(e)}"
        print(f"   [ERROR] {error_msg}")
        return {"error_message": error_msg}

def should_perform_research(_: PrayerState) -> str:
    """
    Determine if the workflow should perform research.

    Args:
        _: Current workflow state (unused)

    Returns:
        Next workflow edge to follow
    """
    return "perform_research"

def parse_mcp_response(text: str) -> Dict:
    """
    Parse the structured response from MCP server into research data.
    
    Args:
        text: Raw text response from MCP server
        
    Returns:
        Dictionary containing parsed research data
    """
    research_data = {
        "divine_names": [],
        "sacred_terms": [],
        "prayer_structure": "",
        "scriptural_references": [],
        "cultural_context": ""
    }

    # Extract sections using regex
    sections = {
        "DIVINE_NAMES": _extract_section(text, "DIVINE_NAMES", "SACRED_TERMS"),
        "SACRED_TERMS": _extract_section(text, "SACRED_TERMS", "PRAYER_STRUCTURE"),
        "PRAYER_STRUCTURE": _extract_section(text, "PRAYER_STRUCTURE", "SCRIPTURAL_REFERENCES"),
        "SCRIPTURAL_REFERENCES": _extract_section(text, "SCRIPTURAL_REFERENCES", "CULTURAL_CONTEXT"),
        "CULTURAL_CONTEXT": _extract_section(text, "CULTURAL_CONTEXT", None)
    }

    # Process list sections
    for section_name, items_list in [
        ("DIVINE_NAMES", "divine_names"),
        ("SACRED_TERMS", "sacred_terms"),
        ("SCRIPTURAL_REFERENCES", "scriptural_references")
    ]:
        if sections[section_name]:
            for line in sections[section_name].strip().split('\n'):
                clean_line = line.strip()
                clean_line = clean_line.lstrip('•-* ').strip()
                if clean_line and not any(placeholder in clean_line for placeholder in ['[name', '[term', '[quote']):
                    research_data[items_list].append(clean_line)

    # Process text sections
    for section_name, field_name in [
        ("PRAYER_STRUCTURE", "prayer_structure"),
        ("CULTURAL_CONTEXT", "cultural_context")
    ]:
        if sections[section_name]:
            research_data[field_name] = sections[section_name].strip()

    return research_data

def _extract_section(text: str, start_marker: str, end_marker: Optional[str]) -> str:
    """
    Extract a section from text between start and end markers.
    
    Args:
        text: Text to search
        start_marker: Section start marker
        end_marker: Section end marker (None if last section)
        
    Returns:
        Extracted section text
    """
    pattern = rf'(?i){re.escape(start_marker)}[:\s]*\n(.*?)'
    if end_marker:
        pattern += rf'(?=\n\s*{re.escape(end_marker)}[:\s]*\n|\Z)'
    
    match = re.search(pattern, text, re.DOTALL)
    return match.group(1).strip() if match else ""
