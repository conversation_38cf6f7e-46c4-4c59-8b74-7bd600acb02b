from pydantic_ai import Agent
from pydantic import BaseModel, <PERSON>
from typing import List, Dict, Optional
from .mcp_connector import MCPConnector
import asyncio

class FaithResearchParams(BaseModel):
    """Parameters for faith research requests."""
    faith: str
    prayer_focus: str
    wisdom_focus: str
    model_type: str = "default"  # default, mixtral, or fast

class FaithResearchResult(BaseModel):
    """Structured result of faith research."""
    faith_name: str
    key_concepts: List[str] = Field(default_factory=list)
    relevant_teachings: List[str] = Field(default_factory=list)
    prayer_elements: List[str] = Field(default_factory=list)
    symbolism: Dict[str, str] = Field(default_factory=dict)
    practice_insights: List[str] = Field(default_factory=list)
    source_references: List[str] = Field(default_factory=list)
    raw_research: Optional[str] = None

class FaithResearchAgent:
    """
    Agent for researching faith traditions and spiritual wisdom
    using the brave-groq-research MCP server.
    """
    
    def __init__(self):
        """Initialize the faith research agent."""
        # Connect to the MCP server for research
        self.research_mcp = MCPConnector("brave-groq-research")
        
        # Initialize the PydanticAI agent for processing research results
        self.agent = Agent(
            'google-gla:gemini-1.5-pro',
            system_prompt='''You are a respectful and knowledgeable spiritual researcher.
            Your role is to analyze information about religious and spiritual traditions
            and extract key elements relevant to prayer composition. Organize your analysis
            into clear categories without bias or judgment. Highlight authentic elements while
            respecting the integrity of each tradition.'''
        )
    
    async def _perform_mcp_research(self, params: FaithResearchParams) -> str:
        """
        Perform research using the brave-groq-research MCP server.
        
        Args:
            params: Research parameters including faith, focus areas, and model type.
            
        Returns:
            Raw research text from the MCP server.
        """
        try:
            # Call the faith_research tool on the MCP server
            result = await self.research_mcp.call_tool("faith_research", {
                "faith": params.faith,
                "prayer_focus": params.prayer_focus,
                "wisdom_focus": params.wisdom_focus,
                "model_type": params.model_type
            })
            
            # Extract the research text from the result
            if isinstance(result, dict) and "research" in result:
                return result["research"]
            elif isinstance(result, str):
                return result
            else:
                return f"Error: Unexpected research result format: {result}"
                
        except Exception as e:
            print(f"Error during MCP research: {e}")
            # For robustness, use a fallback research approach
            return await self._fallback_research(params)
    
    async def _fallback_research(self, params: FaithResearchParams) -> str:
        """
        Fallback research method using the agent directly if MCP fails.
        
        Args:
            params: Research parameters.
            
        Returns:
            Research text generated by the agent.
        """
        prompt = f"""
        Research the {params.faith} tradition regarding:
        - Prayer focus: {params.prayer_focus}
        - Wisdom theme: {params.wisdom_focus}
        
        Provide authentic insights about prayers, practices, concepts, and symbolism.
        """
        
        result = await self.agent.run(prompt)
        return result.output
    
    async def _structure_research(self, faith: str, raw_research: str) -> FaithResearchResult:
        """
        Process raw research text into structured categories using the agent.
        
        Args:
            faith: The name of the faith tradition.
            raw_research: The raw research text.
            
        Returns:
            Structured research result.
        """
        prompt = f"""
        Analyze this research on {faith} and organize it into these categories:
        1. Key concepts
        2. Relevant teachings
        3. Prayer elements
        4. Symbolism
        5. Practice insights
        6. Source references
        
        Research:
        {raw_research}
        
        Return your response as a JSON object with these keys:
        - key_concepts: array of strings
        - relevant_teachings: array of strings
        - prayer_elements: array of strings
        - symbolism: object mapping symbols to meanings
        - practice_insights: array of strings
        - source_references: array of strings
        """
        
        result = await self.agent.run(prompt)
        
        try:
            # Try to parse the response as JSON
            import json
            structured_data = json.loads(result.output)
            
            # Create a structured result object
            return FaithResearchResult(
                faith_name=faith,
                key_concepts=structured_data.get("key_concepts", []),
                relevant_teachings=structured_data.get("relevant_teachings", []),
                prayer_elements=structured_data.get("prayer_elements", []),
                symbolism=structured_data.get("symbolism", {}),
                practice_insights=structured_data.get("practice_insights", []),
                source_references=structured_data.get("source_references", []),
                raw_research=raw_research
            )
        except Exception as e:
            print(f"Error parsing structured research: {e}")
            # Fallback to a basic structure
            return FaithResearchResult(
                faith_name=faith,
                raw_research=raw_research
            )
    
    async def research_faith(self, params: FaithResearchParams) -> FaithResearchResult:
        """
        Main method to research a faith tradition.
        
        Args:
            params: Research parameters.
            
        Returns:
            Structured research result.
        """
        # Step 1: Perform the raw research
        raw_research = await self._perform_mcp_research(params)
        
        # Step 2: Structure the research results
        structured_result = await self._structure_research(params.faith, raw_research)
        
        return structured_result
    
    async def research_multiple_faiths(self, 
                                      faiths: List[str], 
                                      prayer_focus: str,
                                      wisdom_focus: str,
                                      model_type: str = "default") -> Dict[str, FaithResearchResult]:
        """
        Research multiple faith traditions in parallel.
        
        Args:
            faiths: List of faith traditions to research.
            prayer_focus: The focus of the prayer.
            wisdom_focus: The wisdom theme to integrate.
            model_type: The model type to use for research.
            
        Returns:
            Dictionary mapping faith names to research results.
        """
        # Create research tasks for each faith
        tasks = []
        for faith in faiths:
            params = FaithResearchParams(
                faith=faith,
                prayer_focus=prayer_focus,
                wisdom_focus=wisdom_focus,
                model_type=model_type
            )
            tasks.append(self.research_faith(params))
        
        # Run research tasks in parallel
        results = await asyncio.gather(*tasks)
        
        # Map results to faith names
        return {result.faith_name: result for result in results}

# Example usage
async def main():
    research_agent = FaithResearchAgent()
    
    # Research a single faith
    buddhism_research = await research_agent.research_faith(
        FaithResearchParams(
            faith="Buddhism",
            prayer_focus="Inner peace and compassion",
            wisdom_focus="Interdependence of all beings",
            model_type="default"
        )
    )
    
    print(f"Buddhism Research:\n{buddhism_research}")
    
    # Research multiple faiths
    faiths = ["Christianity", "Hinduism", "Indigenous Traditions"]
    multi_faith_research = await research_agent.research_multiple_faiths(
        faiths=faiths,
        prayer_focus="Healing and protection",
        wisdom_focus="Unity in diversity",
        model_type="default"
    )
    
    for faith, research in multi_faith_research.items():
        print(f"\n--- {faith} Research ---")
        print(f"Key concepts: {research.key_concepts}")
        print(f"Prayer elements: {research.prayer_elements}")

if __name__ == "__main__":
    # asyncio.run(main()) # Uncomment for testing
    pass
