"""
Simple test script for PydanticAI integration
This demonstrates a single PydanticAI agent in action without requiring full system setup
"""

import os
import asyncio
from dotenv import load_dotenv
from pydantic_ai import Agent

# Load environment variables
load_dotenv()

# Get API key from environment
GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")

async def test_simple_agent():
    """Test a simple PydanticAI agent."""
    print("\n=== Testing PydanticAI Agent ===\n")
    
    if not GOOGLE_API_KEY:
        print("ERROR: GOOGLE_API_KEY not set in environment. Please add it to your .env file.")
        return
    
    # Create an agent with the Gemini model
    agent = Agent(
        'google-gla:gemini-1.5-pro',  # Using 1.5 Pro for wider availability
        system_prompt='''You are a compassionate spiritual guide specialized in
        creating prayers that draw wisdom from multiple faith traditions while
        respecting their unique characteristics. Your prayers should be poetic, 
        meaningful, and spiritually resonant.'''
    )
    
    # Define test parameters
    prayer_focus = "healing for the planet"
    wisdom_theme = "interconnectedness of all life"
    traditions = ["Buddhism", "Indigenous Traditions", "Christianity"]
    
    # Construct the prompt
    prompt = f"""
    Create a unified prayer with the following parameters:
    
    Prayer focus: {prayer_focus}
    Wisdom theme: {wisdom_theme}
    Traditions to include: {', '.join(traditions)}
    
    The prayer should honor each tradition while creating a harmonious whole.
    Include appropriate terminology and concepts from each tradition.
    The prayer should be approximately 200-250 words in length.
    """
    
    print(f"Requesting prayer with focus: {prayer_focus}")
    print(f"Wisdom theme: {wisdom_theme}")
    print(f"Traditions: {', '.join(traditions)}")
    print("\nSending request to PydanticAI agent...\n")
    
    try:
        # Execute the agent
        result = await agent.run(prompt)
        
        # Display the result
        print("=== Generated Prayer ===\n")
        print(result.output)
        print("\n=== End of Prayer ===\n")
        
        # Success message
        print("✅ PydanticAI agent test completed successfully!")
        print("The agent generated a prayer based on the provided parameters.")
        print("This confirms that the PydanticAI integration is working correctly.")
        
    except Exception as e:
        print(f"❌ Error testing PydanticAI agent: {e}")
        print("Please check your API key and internet connection.")

if __name__ == "__main__":
    print("=== PydanticAI Integration Test ===")
    print("This script tests if the PydanticAI library is properly integrated.")
    asyncio.run(test_simple_agent())
