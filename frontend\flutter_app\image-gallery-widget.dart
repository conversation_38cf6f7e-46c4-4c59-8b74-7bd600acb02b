import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';

class ImageGalleryWidget extends StatefulWidget {
  final List<String> imageUrls;
  final String title;
  
  const ImageGalleryWidget({
    Key? key,
    required this.imageUrls,
    required this.title,
  }) : super(key: key);

  @override
  State<ImageGalleryWidget> createState() => _ImageGalleryWidgetState();
}

class _ImageGalleryWidgetState extends State<ImageGalleryWidget> {
  int _currentIndex = 0;
  final PageController _pageController = PageController();
  
  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Main image display
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: PageView.builder(
                controller: _pageController,
                itemCount: widget.imageUrls.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentIndex = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      // Image
                      Positioned.fill(
                        child: Image.network(
                          widget.imageUrls[index],
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Center(
                              child: CircularProgressIndicator(
                                value: loadingProgress.expectedTotalBytes != null
                                    ? loadingProgress.cumulativeBytesLoaded /
                                        loadingProgress.expectedTotalBytes!
                                    : null,
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.error_outline,
                                    color: Colors.red,
                                    size: 48,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'Failed to load image',
                                    style: theme.textTheme.titleMedium,
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                      
                      // Image controls overlay
                      Positioned(
                        top: 16,
                        right: 16,
                        child: Row(
                          children: [
                            // Share button
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: IconButton(
                                icon: const Icon(
                                  Icons.share,
                                  color: Colors.white,
                                ),
                                onPressed: () {
                                  // Share image URL
                                  _shareImage(index);
                                },
                              ),
                            ),
                            const SizedBox(width: 8),
                            
                            // Expand/zoom button
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: IconButton(
                                icon: const Icon(
                                  Icons.fullscreen,
                                  color: Colors.white,
                                ),
                                onPressed: () {
                                  // Show full screen image
                                  _showFullScreenImage(context, index);
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        
        // Image information
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Image ${_currentIndex + 1} of ${widget.imageUrls.length}',
              style: theme.textTheme.bodyMedium,
            ),
            // Rating stars (just for demonstration, not functional)
            Row(
              children: List.generate(5, (index) {
                return Icon(
                  index < 4 ? Icons.star : Icons.star_border,
                  color: index < 4 ? Colors.amber : Colors.grey,
                  size: 18,
                );
              }),
            ),
          ],
        ),
        const SizedBox(height: 16),
        
        // Thumbnail previews
        SizedBox(
          height: 64,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.imageUrls.length,
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: Container(
                  width: 64,
                  height: 64,
                  margin: const EdgeInsets.only(right: 8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    border: _currentIndex == index
                        ? Border.all(
                            color: theme.colorScheme.primary,
                            width: 2,
                          )
                        : null,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      widget.imageUrls[index],
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Center(
                          child: SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        const SizedBox(height: 16),
        
        // Image description
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Scene Description',
                  style: theme.textTheme.titleMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  _getImageDescription(_currentIndex),
                  style: theme.textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  // Get placeholder image description (in a real app, this would come from the backend)
  String _getImageDescription(int index) {
    final descriptions = [
      'A serene mountain lake at dawn, with mist rising from the water's surface. Golden light spills over the mountain peaks, symbolizing divine illumination.',
      'A blooming cherry tree in a peaceful meadow. Petals dance in the gentle breeze, representing life's beauty and impermanence.',
      'Hands of different skin tones joined in a circle, demonstrating unity, compassion, and the interconnectedness of all beings.',
      'A solitary candle flame glowing in darkness, symbolizing inner light and spiritual awakening that persists even in challenging times.',
      'Ocean waves meeting a sandy shore under a vibrant sunset. The eternal rhythm of waves represents the constancy of divine presence.',
    ];
    
    if (index < widget.imageUrls.length && index < descriptions.length) {
      return descriptions[index];
    }
    
    return 'A visual representation of the prayer, capturing the spiritual essence of the words in imagery.';
  }
  
  // Share image
  void _shareImage(int index) {
    final imageUrl = widget.imageUrls[index];
    final shareText = '${widget.title}\n\nVisual prayer: $imageUrl\n\nGenerated by SpiritSync';
    
    Share.share(shareText);
  }
  
  // Show full screen image
  void _showFullScreenImage(BuildContext context, int index) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black.withOpacity(0.5),
            iconTheme: const IconThemeData(color: Colors.white),
            title: Text(
              'Image ${index + 1} of ${widget.imageUrls.length}',
              style: const TextStyle(color: Colors.white),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.share),
                onPressed: () => _shareImage(index),
              ),
            ],
          ),
          body: Center(
            child: InteractiveViewer(
              clipBehavior: Clip.none,
              minScale: 0.5,
              maxScale: 4.0,
              child: Image.network(
                widget.imageUrls[index],
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
