"""
Text-to-Speech Nodes
LangGraph nodes for TTS processing and audio generation.
"""

from typing import Dict, List
import os
import datetime
import json
import subprocess
import random

from models.prayer_state import PrayerState
from services.openai_service import generate_verbal_text as openai_generate_verbal_text
from utils.file_utils import save_verbal_prayer, read_prayer_file

def ask_audio_conversion_node(state: PrayerState) -> PrayerState:
    """
    Ask the user if they want to convert the prayer to audio using OpenAI.
    Only asks for voice selection, with defaults for everything else.

    Args:
        state: Current workflow state

    Returns:
        Updated state with audio conversion preferences
    """
    print("\n--- Audio Conversion Query ---")

    # Check if we have a unified prayer to work with
    unified_prayer_filepath = state.get('unified_prayer_filepath')
    if not unified_prayer_filepath or not os.path.exists(unified_prayer_filepath):
        print("   [WARNING] No unified prayer found to convert to audio.")
        state['tts_confirmed'] = False
        state['tts_error_message'] = "No unified prayer found to convert to audio."
        return state

    # Display the unified prayer filepath
    print(f"Prayer file: {unified_prayer_filepath}")

    # Check if TTS is already confirmed in the state
    ALL_OPENAI_VOICES: List[str] = [
        "alloy", "ash", "ballad", "coral", "echo",
        "fable", "onyx", "nova", "sage", "shimmer", "verse"
    ]

    if state.get('tts_confirmed'):
        print("[INFO] Audio conversion is pre-confirmed. Proceeding with random voice selection.")
        state['tts_provider'] = "openai"
        state['tts_voice_id'] = random.choice(ALL_OPENAI_VOICES)
        print(f"   [INFO] Randomly selected voice: {state['tts_voice_id']}")

        state['tts_options'] = {
            "model": "gpt-4o-mini-audio-preview",
            "speed": 1.0
        }

        print(f"Using voice '{state['tts_voice_id']}' with GPT-4o-mini audio preview for enhanced narration.")
        return state

    convert_choice = input("Do you want to make this prayer into an audio file? (yes/no): ").strip().lower()

    if convert_choice in ["yes", "y"]:
        state['tts_confirmed'] = True
        state['tts_provider'] = "openai"

        print("\nSelect voice:")
        voice_map = {str(i+1): voice for i, voice in enumerate(ALL_OPENAI_VOICES)}
        for num, voice in voice_map.items():
            hint = ""
            if voice in ["alloy", "ash", "ballad", "coral", "sage", "verse"]: hint = "(neutral/varied)"
            elif voice in ["echo", "fable", "onyx"]: hint = "(male)"
            elif voice in ["nova", "shimmer"]: hint = "(female)"
            print(f"{num}. {voice.capitalize()} {hint}")

        voice_choice = input(f"Enter choice (1-{len(ALL_OPENAI_VOICES)}, default 1 - Alloy): ").strip()
        state['tts_voice_id'] = voice_map.get(voice_choice, "alloy")

        state['tts_options'] = {
            "model": "gpt-4o-mini-audio-preview",
            "speed": 1.0
        }

        print(f"\nUsing voice '{state['tts_voice_id']}' with GPT-4o-mini audio preview for enhanced narration.")
        return state
    else:
        print("Audio conversion skipped.")
        state['tts_confirmed'] = False
        return state

def prepare_tts_text_node(state: PrayerState) -> PrayerState:
    """
    Generate TTS-optimized text with emotional cues and stage directions using OpenAI.
    No user input required - uses OpenAI GPT-4o-mini by default.

    Args:
        state: Current workflow state

    Returns:
        Updated state with verbal prayer text
    """
    print("\n--- Generating TTS-Optimized Text ---")

    unified_prayer_filepath = state.get('unified_prayer_filepath')
    if not unified_prayer_filepath or not os.path.exists(unified_prayer_filepath):
        error_msg = "No unified prayer found to convert to verbal text."
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = ""
        state['verbal_prayer_filepath'] = None
        return state

    prayer_content = read_prayer_file(unified_prayer_filepath, skip_headers=True)
    if not prayer_content:
        error_msg = f"Failed to read content from {unified_prayer_filepath}"
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = ""
        state['verbal_prayer_filepath'] = None
        return state

    print("   [INFO] Using OpenAI GPT-4o-mini for prayer text optimization...")
    verbal_prayer = openai_generate_verbal_text(prayer_content)

    if verbal_prayer and verbal_prayer.startswith("[ERROR]"):
        error_msg = verbal_prayer[8:]
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = ""
        state['verbal_prayer_filepath'] = None
        return state
    elif not verbal_prayer:
        error_msg = "OpenAI generated empty verbal text."
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = ""
        state['verbal_prayer_filepath'] = None
        return state

    base_name = os.path.splitext(os.path.basename(unified_prayer_filepath))[0] if unified_prayer_filepath else f"UnknownSource_{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
    saved_prayers_dir = "Saved_Prayers"
    try:
        os.makedirs(saved_prayers_dir, exist_ok=True)
    except Exception as e:
        error_msg = f"Failed to create directory {saved_prayers_dir}: {e}"
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = verbal_prayer
        state['verbal_prayer_filepath'] = None
        return state
    verbal_file = os.path.join(saved_prayers_dir, f"{base_name}_Verbal.txt")

    save_successful = save_verbal_prayer(
        filepath=verbal_file,
        verbal_text=verbal_prayer,
        source_filepath=unified_prayer_filepath,
        model_name="OPENAI"
    )

    if save_successful:
        print(f"   [INFO] Verbal prayer saved to: {verbal_file}")
        state['verbal_prayer_text'] = verbal_prayer
        state['verbal_prayer_filepath'] = verbal_file
        state['error_message'] = None
        return state
    else:
        error_msg = f"Failed to save verbal prayer to {verbal_file}"
        state['error_message'] = error_msg
        state['verbal_prayer_text'] = verbal_prayer
        state['verbal_prayer_filepath'] = None
        return state

def generate_audio_node(state: PrayerState) -> PrayerState:
    """
    Generate audio from the TTS-optimized text using OpenAI.

    Args:
        state: Current workflow state

    Returns:
        Updated state with audio filepath
    """
    print("\n--- Generating Audio File ---")

    verbal_file = state.get('verbal_prayer_filepath')
    if not verbal_file or not os.path.exists(verbal_file):
        error_msg = state.get('error_message') or "No verbal prayer file found to convert to audio."
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['audio_filepath'] = None
        state['generated_audio'] = None # Ensure this is also cleared
        return state

    provider = "openai"
    voice_id = state.get('tts_voice_id') or 'alloy'
    options = state.get('tts_options') or {}

    is_using_advanced = options.get('model') == "gpt-4o-mini-audio-preview"
    if is_using_advanced:
        print(f"Using GPT-4o-mini audio preview for enhanced narration with voice '{voice_id}'...")
    else:
        print(f"Converting to audio using OpenAI TTS with voice '{voice_id}'...")

    base_path = os.path.splitext(verbal_file)[0]
    audio_extension = "mp3"
    output_path = f"{base_path}_audio.{audio_extension}"

    options_json = "{}" if not options else json.dumps(options)

    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    tts_script_path = os.path.join(project_root, "tts_service.js")

    if not os.path.exists(tts_script_path):
        error_msg = f"TTS service script not found at: {tts_script_path}"
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['audio_filepath'] = None
        state['generated_audio'] = None
        return state

    cmd = [
        "node",
        tts_script_path,
        verbal_file,
        provider,
    ]

    if voice_id:
        cmd.append(voice_id)
    else:
        cmd.append("default")

    cmd.append(output_path)

    if options:
        cmd.append(options_json)

    print(f"   [INFO] Executing: {' '.join(cmd)}")

    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            cwd=project_root
        )

        stdout, stderr = process.communicate()

        if process.returncode != 0:
            error_msg = f"TTS conversion failed with exit code {process.returncode}: {stderr}"
            print(f"   [ERROR] {error_msg}")
            state['error_message'] = error_msg
            state['audio_filepath'] = None
            state['generated_audio'] = None
            return state

        result = None
        for line in stdout.strip().split('\n'):
            if line.startswith('{') and line.endswith('}'):
                try:
                    result = json.loads(line)
                    break
                except json.JSONDecodeError:
                    pass

        if result and result.get("success", False):
            audio_path = result.get("outputPath")
            if not os.path.isabs(audio_path):
                 audio_path = os.path.join(project_root, audio_path)

            print(f"   [SUCCESS] Audio conversion completed: {audio_path}")
            state['audio_filepath'] = audio_path
            state['generated_audio'] = audio_path # Set generated_audio here
            state['error_message'] = None
            return state
        else:
            error_msg = result.get("error") if result else f"Unknown error or no JSON output from TTS script. Stdout: {stdout}, Stderr: {stderr}"
            print(f"   [ERROR] {error_msg}")
            state['error_message'] = error_msg
            state['audio_filepath'] = None
            state['generated_audio'] = None
            return state

    except FileNotFoundError:
         error_msg = f"'node' command not found. Please ensure Node.js is installed and in your PATH."
         print(f"   [ERROR] {error_msg}")
         state['error_message'] = error_msg
         state['audio_filepath'] = None
         state['generated_audio'] = None
         return state
    except Exception as e:
        error_msg = f"Error executing TTS conversion: {str(e)}"
        print(f"   [ERROR] {error_msg}")
        state['error_message'] = error_msg
        state['audio_filepath'] = None
        state['generated_audio'] = None
        return state

def audio_requested(state: PrayerState) -> str:
    """
    Check if the user confirmed audio conversion in ask_audio_conversion_node.

    Args:
        state: Current workflow state

    Returns:
        Next workflow edge to follow
    """
    if state.get('tts_confirmed'):
        print("\nEdge: User requested audio conversion, proceeding to TTS preparation.")
        return "yes"
    else:
        print("\nEdge: No audio conversion requested, ending workflow.")
        return "no"

# Entrypoint wrapper to run the TTS sequence for main.py
def run_tts(state: PrayerState) -> PrayerState:
    """
    Wrapper node to execute the full TTS pipeline sequentially.
    """
    state = ask_audio_conversion_node(state)

    choice = audio_requested(state)
    if choice == "yes":
        state = prepare_tts_text_node(state)
        state = generate_audio_node(state)
    return state

    return state
