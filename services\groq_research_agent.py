"""
Enhanced Research Agent Service using Groq API and Brave Search
Provides improved search capabilities and reliable faith-specific research.
"""

import os
import re
import json
import requests
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_fixed
from groq import Groq

# Load environment variables
load_dotenv()

# Get API keys
GROQ_API_KEY = os.environ.get("GROQ_API_KEY")
BRAVE_API_KEY = os.environ.get("BRAVE_API_KEY")

if not GROQ_API_KEY:
    print("[ERROR] GROQ_API_KEY not found in environment variables. Research agent will fail.")
if not BRAVE_API_KEY:
    print("[WARNING] BRAVE_API_KEY not found. Will proceed without web search capabilities.")

# API endpoints
BRAVE_SEARCH_URL = "https://api.search.brave.com/res/v1/web/search"

# Groq model options
GROQ_MODELS = {
    "default": "llama-3.3-70b-versatile",  # Default model for general research
    "llama-3.3-70B-vers": "llama-3.3-70b-versatile",  # Fixed model name
    "mixtral": "mixtral-8x7b-32768",  # For deeper theological research
    "fast": "gemma-3-7b-it"  # For quicker responses
}

class GroqResearchAgent:
    """
    Enhanced Research agent that performs detailed searches using Groq and Brave.
    Provides faith-specific research and reliably shares results with prayer writing agents.
    """

    def __init__(self, model_type="default", debug_mode=False):
        """
        Initialize the research agent.

        Args:
            model_type: Type of Groq model to use (default, mixtral, fast)
            debug_mode: Whether to output additional debugging information
        """
        self.groq_client = Groq(api_key=GROQ_API_KEY)
        self.model = GROQ_MODELS.get(model_type, GROQ_MODELS["default"])
        self.debug_mode = debug_mode
        print(f"[INFO] Initialized GroqResearchAgent with model: {self.model}")

    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_fixed(2),  # Wait 2 seconds between retries
    )
    def _search_brave(self, query: str, count: int = 5) -> List[Dict]:
        """
        Perform a web search using Brave Search API.

        Args:
            query: Search query
            count: Number of results to return

        Returns:
            List of search results with title, description, and URL
        """
        if not BRAVE_API_KEY:
            print("[WARNING] Brave Search API key not available. Skipping web search.")
            return []

        headers = {
            "Accept": "application/json",
            "X-Subscription-Token": BRAVE_API_KEY
        }

        params = {
            "q": query,
            "count": count,
            "search_lang": "en"
        }

        try:
            response = requests.get(BRAVE_SEARCH_URL, headers=headers, params=params)
            response.raise_for_status()
            data = response.json()
            
            results = []
            for web_result in data.get("web", {}).get("results", []):
                results.append({
                    "title": web_result.get("title", ""),
                    "description": web_result.get("description", ""),
                    "url": web_result.get("url", "")
                })
            return results

        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Error calling Brave Search API: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"[ERROR] Response status: {e.response.status_code}")
                print(f"[ERROR] Response text: {e.response.text}")
            return []

    @retry(
        stop=stop_after_attempt(3),  # Retry up to 3 times
        wait=wait_fixed(2),  # Wait 2 seconds between retries
    )
    def _make_groq_request(self, system_prompt: str, user_prompt: str) -> Optional[str]:
        """
        Make a request to the Groq API with improved error handling.

        Args:
            system_prompt: System prompt for the API
            user_prompt: User prompt for the API

        Returns:
            Response text from the API or None if the request fails
        """
        try:
            print(f"[INFO] Making research request to Groq API using model: {self.model}")
            
            completion = self.groq_client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=4096,
                top_p=1,
                stream=False
            )
            
            if completion.choices and completion.choices[0].message:
                print("[INFO] Research data obtained successfully.")
                return completion.choices[0].message.content.strip()
            else:
                print("[WARNING] No content found in Groq response.")
                return None

        except Exception as e:
            print(f"[ERROR] Error calling Groq API: {e}")
            return None

    def research_faith_specific(self, faith: str, prayer_focus: str, wisdom_focus: str) -> Dict:
        """
        Perform enhanced faith-specific research with structured output format.

        Args:
            faith: Name of the faith tradition
            prayer_focus: Focus of the prayer
            wisdom_focus: Spiritual wisdom to integrate

        Returns:
            Dictionary containing research results for the specific faith
        """
        # First, gather relevant web search results
        search_query = f"{faith} {prayer_focus} prayer {wisdom_focus} religious tradition"
        web_results = self._search_brave(search_query)
        
        # Create context from web results
        web_context = "\n\n".join([
            f"Source: {result['url']}\nTitle: {result['title']}\nDescription: {result['description']}"
            for result in web_results
        ])

        system_prompt = (
            f"You are an expert scholar and practitioner of {faith} with deep knowledge of its texts, "
            f"traditions, prayers, and spiritual concepts. Provide authentic, respectful, and accurate "
            f"information about {faith} that would help create a genuine prayer in this tradition.\n\n"
            f"Additional context from reliable sources:\n{web_context}"
        )

        user_prompt = (
            f"For a prayer focused on '{prayer_focus}' that integrates wisdom about '{wisdom_focus}', "
            f"provide detailed information specific to {faith} in this EXACT format:\n\n"
            f"DIVINE_NAMES:\n"
            f"- [name1]\n"
            f"- [name2]\n"
            f"- [name3]\n"
            f"- [name4]\n"
            f"- [name5]\n\n"
            f"SACRED_TERMS:\n"
            f"- [term1]\n"
            f"- [term2]\n"
            f"- [term3]\n"
            f"- [term4]\n"
            f"- [term5]\n\n"
            f"PRAYER_STRUCTURE:\n"
            f"[Brief description of how prayers are typically structured in this tradition, including any opening/closing formulas]\n\n"
            f"SCRIPTURAL_REFERENCES:\n"
            f"- [quote1]\n"
            f"- [quote2]\n"
            f"- [quote3]\n\n"
            f"CULTURAL_CONTEXT:\n"
            f"[Brief description of cultural/historical context relevant to this prayer theme in this tradition]\n\n"
            f"It is CRITICAL to follow this exact format with these exact section headings for proper parsing."
        )

        result = self._make_groq_request(system_prompt, user_prompt)
        if not result:
            print(f"[WARNING] No research data obtained for {faith}. Using empty values.")
            return {
                "divine_names": [],
                "sacred_terms": [],
                "prayer_structure": "",
                "scriptural_references": [],
                "cultural_context": ""
            }

        # Parse the response into structured data
        research_data = self._parse_faith_research(result, faith)
        
        # Display research results
        print(f"[INFO] Research results for {faith}:")
        print(f"  - Divine Names: {len(research_data['divine_names'])} items")
        print(f"  - Sacred Terms: {len(research_data['sacred_terms'])} items")
        print(f"  - Prayer Structure: {'Yes' if research_data['prayer_structure'] else 'No'}")
        print(f"  - Scriptural References: {len(research_data['scriptural_references'])} items")
        print(f"  - Cultural Context: {'Yes' if research_data['cultural_context'] else 'No'}")
        
        return research_data

    def _parse_faith_research(self, raw_text: str, faith: str) -> Dict:
        """Parse the faith-specific research response into structured data."""
        if self.debug_mode:
            print(f"[DEBUG] Parsing raw text for {faith}:\n{raw_text}")
            
        research_data = {
            "divine_names": [],
            "sacred_terms": [],
            "prayer_structure": "",
            "scriptural_references": [],
            "cultural_context": ""
        }

        # Extract sections using more robust pattern matching
        sections = {
            "DIVINE_NAMES": self._extract_section(raw_text, "DIVINE_NAMES", "SACRED_TERMS"),
            "SACRED_TERMS": self._extract_section(raw_text, "SACRED_TERMS", "PRAYER_STRUCTURE"),
            "PRAYER_STRUCTURE": self._extract_section(raw_text, "PRAYER_STRUCTURE", "SCRIPTURAL_REFERENCES"),
            "SCRIPTURAL_REFERENCES": self._extract_section(raw_text, "SCRIPTURAL_REFERENCES", "CULTURAL_CONTEXT"),
            "CULTURAL_CONTEXT": self._extract_section(raw_text, "CULTURAL_CONTEXT", None)
        }

        # Process list sections
        for section_name, items_list in [
            ("DIVINE_NAMES", "divine_names"),
            ("SACRED_TERMS", "sacred_terms"),
            ("SCRIPTURAL_REFERENCES", "scriptural_references")
        ]:
            if sections[section_name]:
                for line in sections[section_name].strip().split('\n'):
                    clean_line = line.strip()
                    clean_line = re.sub(r'^\s*[\-\*•\d]+\.?\s*|\[|\]', '', clean_line)
                    
                    if (clean_line and 
                        len(clean_line) < 100 and
                        not any(placeholder in clean_line.lower() for placeholder in ['[name', '[term', '[quote'])):
                        research_data[items_list].append(clean_line)

        # Process text sections
        for section_name, field_name in [
            ("PRAYER_STRUCTURE", "prayer_structure"),
            ("CULTURAL_CONTEXT", "cultural_context")
        ]:
            if sections[section_name]:
                text = sections[section_name].strip()
                text = re.sub(r'\[|\]', '', text)
                research_data[field_name] = text

        return research_data

    def _extract_section(self, text: str, start_marker: str, end_marker: Optional[str]) -> str:
        """Extract a section from the text between start and end markers."""
        pattern = rf'(?i){re.escape(start_marker)}[:\s]*\n(.*?)'
        if end_marker:
            pattern += rf'(?=\n\s*{re.escape(end_marker)}[:\s]*\n|\Z)'
        matches = re.search(pattern, text, re.DOTALL)
        return matches.group(1).strip() if matches else ""

    def get_complete_research(self, prayer_focus: str, wisdom_focus: str, faiths: List[str]) -> Tuple[str, Dict[str, Dict]]:
        """Get complete research data including general context and faith-specific research."""
        print(f"\n[INFO] Starting comprehensive research for prayer on '{prayer_focus}' with wisdom '{wisdom_focus}'")

        # Get general context
        general_context = self.get_general_context(prayer_focus, wisdom_focus)

        # Get faith-specific research
        faith_research = {}
        for faith in faiths:
            print(f"[INFO] Researching {faith}...")
            research_data = self.research_faith_specific(faith, prayer_focus, wisdom_focus)
            faith_research[faith] = research_data

        return general_context, faith_research
        
    def get_general_context(self, prayer_focus: str, wisdom_focus: str) -> str:
        """Get general spiritual context for the prayer focus and wisdom."""
        # First, gather relevant web search results
        search_query = f"{prayer_focus} {wisdom_focus} spiritual wisdom interfaith prayer"
        web_results = self._search_brave(search_query)
        
        # Create context from web results
        web_context = "\n\n".join([
            f"Source: {result['url']}\nTitle: {result['title']}\nDescription: {result['description']}"
            for result in web_results
        ])

        system_prompt = (
            "You are a spiritual research assistant with deep knowledge of world religions and spiritual traditions. "
            "Provide comprehensive, nuanced context that draws from multiple faith traditions.\n\n"
            f"Additional context from reliable sources:\n{web_context}"
        )

        user_prompt = (
            f"Research and provide deep spiritual context for a prayer focused on '{prayer_focus}' "
            f"that integrates wisdom about '{wisdom_focus}'. Include universal spiritual principles, "
            f"shared values across traditions, and meaningful insights that would enrich a multi-faith prayer. "
            f"Focus on depth, authenticity, and spiritual wisdom that transcends individual traditions."
        )

        result = self._make_groq_request(system_prompt, user_prompt)
        return result or ""
