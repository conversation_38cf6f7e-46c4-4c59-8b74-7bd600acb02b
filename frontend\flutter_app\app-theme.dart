import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

// Theme provider
final themeProvider = ChangeNotifierProvider<ThemeManager>((ref) {
  return ThemeManager();
});

// Theme manager for handling user preferences
class ThemeManager with ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  
  ThemeMode get themeMode => _themeMode;
  
  ThemeManager() {
    _loadThemePreference();
  }
  
  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    final prefs = await SharedPreferences.getInstance();
    final themeIndex = prefs.getInt('theme_mode') ?? 0;
    _themeMode = ThemeMode.values[themeIndex];
    notifyListeners();
  }
  
  // Set theme mode and save to SharedPreferences
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', mode.index);
    notifyListeners();
  }
  
  // Toggle between light and dark modes
  Future<void> toggleThemeMode() async {
    _themeMode = _themeMode == ThemeMode.light 
        ? ThemeMode.dark 
        : ThemeMode.light;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_mode', _themeMode.index);
    notifyListeners();
  }
}

// App theme configuration
class AppTheme {
  final ColorScheme? lightDynamicScheme;
  final ColorScheme? darkDynamicScheme;
  
  // Default seed color if dynamic colors are not available
  static const Color defaultSeedColor = Color(0xFF6750A4); // Purple
  
  AppTheme({
    this.lightDynamicScheme,
    this.darkDynamicScheme,
  });
  
  // Generate color scheme based on spiritual traditions
  static ColorScheme generateFaithColorScheme(
    List<String> faiths, 
    bool isDark,
  ) {
    // If no faiths selected, use primary color
    if (faiths.isEmpty) {
      return ColorScheme.fromSeed(
        seedColor: defaultSeedColor,
        brightness: isDark ? Brightness.dark : Brightness.light,
      );
    }
    
    // Get colors for selected faiths
    final colors = faiths.map((faith) => 
      AppConstants.faithColors[faith] ?? defaultSeedColor
    ).toList();
    
    // Use first color as seed color
    final seedColor = colors.first;
    
    // Create color scheme from seed
    return ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: isDark ? Brightness.dark : Brightness.light,
    );
  }
  
  // Get the light theme
  ThemeData get lightTheme {
    // Use dynamic color scheme if available, otherwise create default
    final colorScheme = lightDynamicScheme ?? 
      ColorScheme.fromSeed(
        seedColor: defaultSeedColor,
        brightness: Brightness.light,
      );
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      // Text theme with custom fonts
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        titleLarge: TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      // Cards with rounded corners
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      // Rounded buttons
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
        ),
      ),
      // Rounded input fields
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }
  
  // Get the dark theme
  ThemeData get darkTheme {
    // Use dynamic color scheme if available, otherwise create default
    final colorScheme = darkDynamicScheme ?? 
      ColorScheme.fromSeed(
        seedColor: defaultSeedColor,
        brightness: Brightness.dark,
      );
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      brightness: Brightness.dark,
      // Text theme with custom fonts
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        displayMedium: TextStyle(
          fontWeight: FontWeight.bold,
        ),
        titleLarge: TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      // Cards with rounded corners
      cardTheme: CardTheme(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      // Rounded buttons
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(28),
          ),
        ),
      ),
      // Rounded input fields
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
    );
  }
}
