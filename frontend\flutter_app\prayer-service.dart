import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import '../constants/app_constants.dart';
import '../models/prayer_state.dart';

// Enum to track prayer generation status
enum PrayerGenerationStatus {
  initial,
  researching,
  generating,
  aggregating,
  creatingAudio,
  creatingImages,
  completed,
  error,
}

class PrayerService {
  final Dio _dio;
  final String baseUrl;
  
  // Socket connection for streaming updates
  WebSocketChannel? _channel;
  StreamController<PrayerState>? _streamController;
  
  PrayerService({
    Dio? dio,
    this.baseUrl = AppConstants.baseApiUrl,
  }) : _dio = dio ?? Dio();
  
  // Initialize Dio client with interceptors for logging, etc.
  void _initDio() {
    _dio.options.baseUrl = baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    
    // Add interceptors for logging, authentication, etc.
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          debugPrint('REQUEST[${options.method}] => PATH: ${options.path}');
          return handler.next(options);
        },
        onResponse: (response, handler) {
          debugPrint('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
          return handler.next(response);
        },
        onError: (DioException e, handler) {
          debugPrint('ERROR[${e.response?.statusCode}] => PATH: ${e.requestOptions.path}');
          return handler.next(e);
        },
      ),
    );
  }
  
  // Start prayer generation process
  Future<String> generatePrayer({
    required String prayFor,
    required String wisdomToIntegrate,
    required List<String> selectedFaiths,
    bool generateImages = false,
    String? voiceId,
  }) async {
    try {
      // Construct prayer request payload
      final data = {
        'pray_for': prayFor,
        'wisdom_to_integrate': wisdomToIntegrate,
        'selected_faiths': selectedFaiths,
        'generate_images': generateImages,
        'voice_id': voiceId,
      };
      
      // Make API request to start prayer generation
      final response = await _dio.post(
        AppConstants.prayerGenerateEndpoint,
        data: data,
      );
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        // Parse response to get run ID
        final responseData = response.data;
        return responseData['run_id'] as String;
      } else {
        throw Exception('Failed to start prayer generation: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error generating prayer: $e');
      rethrow;
    }
  }
  
  // Get prayer generation status
  Future<PrayerState> getPrayerStatus(String runId) async {
    try {
      final response = await _dio.get(
        '${AppConstants.prayerStatusEndpoint}$runId',
      );
      
      if (response.statusCode == 200) {
        final responseData = response.data;
        return PrayerState.fromJson(responseData);
      } else {
        throw Exception('Failed to get prayer status: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error getting prayer status: $e');
      rethrow;
    }
  }
  
  // Stream prayer generation updates
  Stream<PrayerState> streamPrayerStatus(String runId) {
    _streamController = StreamController<PrayerState>.broadcast();
    
    try {
      // Initialize WebSocket connection
      final wsUrl = Uri.parse('$baseUrl${AppConstants.prayerStreamEndpoint}$runId');
      _channel = WebSocketChannel.connect(wsUrl);
      
      // Listen to WebSocket stream
      _channel!.stream.listen(
        (data) {
          try {
            final jsonData = json.decode(data as String);
            final prayerState = PrayerState.fromJson(jsonData);
            _streamController!.add(prayerState);
          } catch (e) {
            debugPrint('Error parsing prayer state update: $e');
            _streamController!.addError(e);
          }
        },
        onError: (error) {
          debugPrint('WebSocket error: $error');
          _streamController!.addError(error);
          _closeConnection();
        },
        onDone: () {
          debugPrint('WebSocket connection closed');
          _closeConnection();
        },
      );
      
      return _streamController!.stream;
    } catch (e) {
      debugPrint('Error streaming prayer status: $e');
      _streamController!.addError(e);
      _closeConnection();
      return _streamController!.stream;
    }
  }
  
  // Close WebSocket connection and stream controller
  void _closeConnection() {
    _channel?.sink.close();
    _streamController?.close();
  }
  
  // Get prayer audio URL
  String getAudioUrl(String audioPath) {
    return '$baseUrl${AppConstants.prayerAudioEndpoint}$audioPath';
  }
  
  // Get prayer image URL
  String getImageUrl(String imagePath) {
    return '$baseUrl${AppConstants.prayerImageEndpoint}$imagePath';
  }
  
  // Dispose resources
  void dispose() {
    _closeConnection();
  }
}
