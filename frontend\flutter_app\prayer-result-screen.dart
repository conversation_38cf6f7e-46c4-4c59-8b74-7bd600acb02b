import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:share_plus/share_plus.dart';
import '../constants/app_constants.dart';
import '../models/prayer_state.dart';
import '../providers/prayer_provider.dart';
import '../widgets/audio_player_widget.dart';
import '../widgets/image_gallery_widget.dart';

class PrayerResultScreen extends ConsumerStatefulWidget {
  final String runId;
  
  const PrayerResultScreen({
    Key? key,
    required this.runId,
  }) : super(key: key);

  @override
  ConsumerState<PrayerResultScreen> createState() => _PrayerResultScreenState();
}

class _PrayerResultScreenState extends ConsumerState<PrayerResultScreen> 
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isInitialized = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    // Load prayer data with the provided run ID
    _loadPrayer();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  // Load prayer data
  Future<void> _loadPrayer() async {
    try {
      await ref.read(prayerStateProvider.notifier).loadPrayer(widget.runId);
      setState(() {
        _isInitialized = true;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading prayer: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final prayerStateAsync = ref.watch(prayerStateProvider);
    
    return Scaffold(
      body: prayerStateAsync.when(
        data: (prayerState) {
          if (!_isInitialized || prayerState == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }
          
          // Check if prayer generation is still in progress
          if (!prayerState.isCompleted) {
            // Redirect to console screen if still in progress
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.replace('${AppConstants.prayerConsoleRoute}/${widget.runId}');
            });
            
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Prayer still in progress. Redirecting to console...'),
                ],
              ),
            );
          }
          
          // Check for errors
          if (prayerState.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'An error occurred',
                    style: theme.textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 32),
                    child: Text(
                      prayerState.errorMessage ?? 'Unknown error',
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      context.pop();
                    },
                    child: const Text('Go Back'),
                  ),
                ],
              ),
            );
          }
          
          // Get prayer theme and faiths
          final prayerTheme = prayerState.prayerFocusTheme ?? 'Prayer';
          final faiths = prayerState.spiritualMovementsToProcess ?? [];
          
          // Get prayer content
          final prayerText = prayerState.combinedPrayerText ?? '';
          final audioFilePath = prayerState.audioFilepath;
          final imageFilePaths = prayerState.imagePrayerFilepaths ?? [];
          
          // Get colors based on faiths
          final gradientColors = faiths.isEmpty 
              ? [theme.colorScheme.primary, theme.colorScheme.tertiary]
              : faiths
                  .take(2)
                  .map((faith) => 
                      AppConstants.faithColors[faith] ?? theme.colorScheme.primary)
                  .toList();
          
          if (gradientColors.length == 1) {
            gradientColors.add(gradientColors[0].withOpacity(0.7));
          }
          
          // Build the result screen
          return NestedScrollView(
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                // Header with gradient background
                SliverAppBar(
                  expandedHeight: 200.0,
                  pinned: true,
                  title: const Text('Prayer'),
                  actions: [
                    // Share button
                    IconButton(
                      icon: const Icon(Icons.share),
                      onPressed: () {
                        _sharePrayer(prayerTheme, prayerText);
                      },
                    ),
                    // Save to favorites button (would need to be implemented)
                    IconButton(
                      icon: const Icon(Icons.bookmark_outline),
                      onPressed: () {
                        // TODO: Implement save functionality
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Prayer saved to favorites'),
                          ),
                        );
                      },
                    ),
                  ],
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: gradientColors,
                        ),
                      ),
                      child: Stack(
                        children: [
                          // Background pattern
                          Positioned.fill(
                            child: Opacity(
                              opacity: 0.1,
                              child: Image.asset(
                                'assets/images/pattern_light.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          
                          // Prayer title
                          Positioned(
                            left: 16,
                            right: 16,
                            bottom: 48,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  prayerTheme,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 24,
                                    shadows: [
                                      Shadow(
                                        offset: Offset(1, 1),
                                        blurRadius: 3,
                                        color: Color.fromARGB(150, 0, 0, 0),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          
                          // Faith tradition chips
                          Positioned(
                            left: 16,
                            right: 16,
                            bottom: 16,
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: faiths.map((faith) => Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                child: Text(
                                  faith,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              )).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // Tab bar
                SliverPersistentHeader(
                  delegate: _SliverAppBarDelegate(
                    TabBar(
                      controller: _tabController,
                      labelColor: theme.colorScheme.primary,
                      unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.7),
                      tabs: const [
                        Tab(
                          icon: Icon(Icons.text_fields),
                          text: 'Text',
                        ),
                        Tab(
                          icon: Icon(Icons.headphones),
                          text: 'Audio',
                        ),
                        Tab(
                          icon: Icon(Icons.image),
                          text: 'Images',
                        ),
                      ],
                    ),
                  ),
                  pinned: true,
                ),
              ];
            },
            body: TabBarView(
              controller: _tabController,
              children: [
                // Text tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildTextContent(context, theme, prayerState),
                ),
                
                // Audio tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildAudioContent(
                    context, 
                    theme, 
                    prayerState,
                    audioFilePath,
                  ),
                ),
                
                // Images tab
                SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: _buildImagesContent(
                    context, 
                    theme, 
                    prayerState,
                    imageFilePaths,
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Error',
                style: theme.textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  context.pop();
                },
                child: const Text('Go Back'),
              ),
            ],
          ),
        ),
      ),
      
      // Bottom navigation bar
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            IconButton(
              icon: const Icon(Icons.home),
              onPressed: () {
                context.go(AppConstants.dashboardRoute);
              },
              tooltip: 'Home',
            ),
            IconButton(
              icon: const Icon(Icons.bookmark_border),
              onPressed: () {
                // TODO: Save to favorites
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Prayer saved to favorites'),
                  ),
                );
              },
              tooltip: 'Save',
            ),
            IconButton(
              icon: const Icon(Icons.share),
              onPressed: () {
                final prayerState = prayerStateAsync.valueOrNull;
                if (prayerState != null) {
                  _sharePrayer(
                    prayerState.prayerFocusTheme ?? 'Prayer',
                    prayerState.combinedPrayerText ?? '',
                  );
                }
              },
              tooltip: 'Share',
            ),
          ],
        ),
      ),
    );
  }
  
  // Build text content tab
  Widget _buildTextContent(
    BuildContext context, 
    ThemeData theme, 
    PrayerState prayerState,
  ) {
    final prayerText = prayerState.combinedPrayerText ?? '';
    final individualPrayers = prayerState.individualPrayers;
    
    if (prayerText.isEmpty) {
      return const Center(
        child: Text('No prayer text available.'),
      );
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Unified prayer
        Text(
          'Unified Prayer',
          style: theme.textTheme.headlineSmall,
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            prayerText,
            style: theme.textTheme.bodyLarge,
          ),
        ),
        const SizedBox(height: 24),
        
        // Individual tradition prayers
        if (individualPrayers != null && individualPrayers.isNotEmpty) ...[
          Text(
            'Individual Tradition Prayers',
            style: theme.textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          
          // Faith accordion panels
          ...individualPrayers.entries.map((entry) {
            final faith = entry.key;
            final prayer = entry.value;
            final faithColor = AppConstants.faithColors[faith] ?? theme.colorScheme.primary;
            
            return _buildExpandablePanel(
              context: context,
              title: faith,
              color: faithColor,
              child: Text(prayer),
            );
          }).toList(),
        ],
      ],
    );
  }
  
  // Build audio content tab
  Widget _buildAudioContent(
    BuildContext context, 
    ThemeData theme, 
    PrayerState prayerState,
    String? audioFilePath,
  ) {
    if (audioFilePath == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.headphones_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No audio available for this prayer.',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    final audioUrl = ref.read(prayerStateProvider.notifier).getAudioUrl(audioFilePath);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(height: 24),
        
        // Audio visualization / icon
        Container(
          width: 180,
          height: 180,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                theme.colorScheme.primary.withOpacity(0.7),
                theme.colorScheme.tertiary.withOpacity(0.7),
              ],
            ),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.headphones,
            size: 80,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 32),
        
        // Audio title
        Text(
          prayerState.prayerFocusTheme ?? 'Prayer',
          style: theme.textTheme.headlineSmall,
          textAlign: TextAlign.center,
        ),
        Text(
          'Voice: ${prayerState.ttsVoiceId ?? 'Default'}',
          style: theme.textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        
        // Audio player
        AudioPlayerWidget(
          audioUrl: audioUrl,
          title: prayerState.prayerFocusTheme ?? 'Prayer',
        ),
      ],
    );
  }
  
  // Build images content tab
  Widget _buildImagesContent(
    BuildContext context, 
    ThemeData theme, 
    PrayerState prayerState,
    List<String> imageFilePaths,
  ) {
    if (imageFilePaths.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.image_not_supported,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No images available for this prayer.',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }
    
    // Convert image paths to full URLs
    final imageUrls = imageFilePaths.map((path) {
      return ref.read(prayerStateProvider.notifier).getImageUrl(path);
    }).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Visual Prayer Representations',
          style: theme.textTheme.headlineSmall,
        ),
        const SizedBox(height: 16),
        
        // Image gallery
        ImageGalleryWidget(
          imageUrls: imageUrls,
          title: prayerState.prayerFocusTheme ?? 'Prayer',
        ),
      ],
    );
  }
  
  // Build expandable panel for faith traditions
  Widget _buildExpandablePanel({
    required BuildContext context,
    required String title,
    required Color color,
    required Widget child,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
      child: ExpansionTile(
        title: Text(
          title,
          style: TextStyle(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconColor: color,
        collapsedIconColor: color,
        childrenPadding: const EdgeInsets.all(16),
        children: [child],
      ),
    );
  }
  
  // Share prayer text
  void _sharePrayer(String title, String text) {
    final shareText = '''
$title

$text

Generated by SpiritSync - A Multi-Faith Prayer Experience
''';
    
    Share.share(shareText);
  }
}

// Delegate for SliverPersistentHeader
class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;
  
  _SliverAppBarDelegate(this.tabBar);
  
  @override
  double get minExtent => tabBar.preferredSize.height;
  
  @override
  double get maxExtent => tabBar.preferredSize.height;
  
  @override
  Widget build(
    BuildContext context, 
    double shrinkOffset, 
    bool overlapsContent,
  ) {
    return Container(
      color: Theme.of(context).colorScheme.surface,
      child: tabBar,
    );
  }
  
  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar;
  }
}
