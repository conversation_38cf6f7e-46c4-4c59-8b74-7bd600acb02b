python-dotenv
pydantic
pydantic-ai>=0.0.13  # PydanticAI agent framework
requests
langgraph==0.4.7
langchain-core
langchain
google-generativeai>=0.6.0  # For Gemini 2.5 models
openai
groq
anthropic
mistralai
tenacity
modelcontextprotocol
Pillow  # For image processing
aiohttp # For asynchronous HTTP requests with fal.ai
replicate  # For video generation via Replicate API
websockets  # For real-time progress updates
