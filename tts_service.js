#!/usr/bin/env node

// TTS Service for Prayer Application
// Now supports OpenAI “4o-mini-audio-preview” as the default, with fallback to “4o-mini-tts”

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { promisify } = require('util');
const writeFileAsync = promisify(fs.writeFile);
const readFileAsync = promisify(fs.readFile);
const { exec } = require('child_process');
const execAsync = promisify(exec);

// Command line arguments
const args = process.argv.slice(2);
const textFilePath = args[0];  // Path to the text file to convert
// Default provider switched to 'openai'
const provider = args[1] || 'openai';
const voiceId = args[2];  // Voice ID/name
const outputPath = args[3];  // Output file path
const options = args[4] ? JSON.parse(args[4]) : {};  // Additional options as JSON string

// Load environment variables from .env file if needed
try {
  require('dotenv').config();
} catch (err) {
  console.log('dotenv module not available, skipping .env loading');
}

// Get API keys from environment variables
const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const AZURE_API_KEY = process.env.AZURE_API_KEY;
const AZURE_REGION = process.env.AZURE_REGION || 'eastus';

// Default voices for each provider
const DEFAULT_VOICES = {
  openai: 'alloy',  // OpenAI's neutral voice
  azure: 'en-US-JennyNeural'
};

/**
 * Main function to convert text to speech
 */
async function convertTextToSpeech() {
  try {
    if (!fs.existsSync(textFilePath)) {
      throw new Error(`Text file does not exist at path: ${textFilePath}`);
    }
    let text = await readFileAsync(textFilePath, 'utf8');

    // Clean up the text to prevent dead space at the end
    text = text.trim();
    // Remove any header lines (starting with #)
    text = text.split('\n').filter(line => !line.trim().startsWith('#')).join('\n');
    // Remove any trailing whitespace or newlines
    text = text.replace(/\s+$/g, '');

    // Check if we have valid content
    if (!text.trim()) {
      throw new Error('No valid text content found after removing header lines');
    }
    const outputDir = path.dirname(outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    let audioBuffer;
    switch (provider.toLowerCase()) {
      case 'openai':
        audioBuffer = await useOpenAITTS(text, voiceId || DEFAULT_VOICES.openai, options);
        break;
      case 'azure':
        audioBuffer = await useAzureTTS(text, voiceId || DEFAULT_VOICES.azure, options);
        break;
      default:
        throw new Error(`Unsupported TTS provider: ${provider}`);
    }
    await writeFileAsync(outputPath, audioBuffer);
    console.log(`Audio file saved successfully to: ${outputPath}`);
    console.log(JSON.stringify({ success: true, outputPath: outputPath, provider: provider }));
    return true;
  } catch (error) {
    console.error('Error in TTS conversion:', error.message);
    console.log(JSON.stringify({ success: false, error: error.message, provider: provider }));
    return false;
  }
}

/**
 * Use OpenAI for TTS conversion
 * Default model is now "gpt-4o-mini-audio-preview" with fallback to "gpt-4o-mini-tts"
 * Adjusted input arguments and payload accordingly.
 */
async function useOpenAITTS(text, voice, options = {}) {
  if (!OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY environment variable is not set');
  }
  // Determine model: default to preview model, fallback set in code below.
  let model = options.model || 'gpt-4o-mini-audio-preview';
  const fallbackModel = 'gpt-4o-mini-tts';
  console.log(`[INFO] Using OpenAI TTS with voice: ${voice}`);
  console.log(`[INFO] Model: ${model}`);
  console.log(`[INFO] Speed: ${options.speed || 1.0}`);
  const speed = options.speed ? Math.min(Math.max(options.speed, 0.25), 2.0) : 1.0;

  // Try using the preview model first
  if (model === 'gpt-4o-mini-audio-preview') {
    console.log('Using OpenAI gpt-4o-mini-audio-preview model for narration');
    const url = 'https://api.openai.com/v1/chat/completions';
    const payload = {
      model: model,
      messages: [
        {
          role: "system",
          content: "You are a professional audiobook narrator. CRITICAL INSTRUCTION: NEVER verbalize text in parentheses.\n\nFollow these instructions with absolute precision:\n\n1. Text in parentheses like (whisper), (emphasis), (reverent), (pause) are ONLY directions for how to speak\n\n2. When you see a parenthetical direction, COMPLETELY SKIP saying those words and ONLY apply the style\n\n3. EXAMPLES of CORRECT handling:\n   - For '(whisper) Hello there' → Say ONLY 'Hello there' in a whispered voice\n   - For '(emphasis) Important point' → Say ONLY 'Important point' with emphasis\n   - For '(pause)' → Insert a brief pause without saying anything\n   - For '(reverent) Divine presence' → Say ONLY 'Divine presence' in a reverent tone\n\n4. This is the MOST IMPORTANT instruction: NEVER EVER read the words inside parentheses out loud\n\n5. Maintain natural pacing and appropriate emotional range throughout"
        },
        {
          role: "user",
          content: text
        }
      ],
      temperature: options.temperature || 0.7,
      max_tokens: options.max_tokens || 4096,
      top_p: options.top_p || 1.0,
      frequency_penalty: options.frequency_penalty || 0.0,
      presence_penalty: options.presence_penalty || 0.0,
      response_format: { type: "text_and_audio" },
      voice: voice || "alloy",
      speed: speed
    };
    try {
      const response = await axios({
        method: 'post',
        url: url,
        data: payload,
        headers: {
          'Authorization': `Bearer ${OPENAI_API_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      console.log('Received response from OpenAI API');
      if (response.data &&
          response.data.choices &&
          response.data.choices[0] &&
          response.data.choices[0].message) {
        const message = response.data.choices[0].message;
        if (message.content_blocks) {
          const audioBlock = message.content_blocks.find(block => block.type === 'audio');
          if (audioBlock && audioBlock.audio) {
            return Buffer.from(audioBlock.audio.data, 'base64');
          }
        } else if (message.audio) {
          return Buffer.from(message.audio, 'base64');
        }
      }
      throw new Error('Could not find audio data in gpt-4o-mini-audio-preview response');
    } catch (error) {
      console.error('Error with gpt-4o-mini-audio-preview:', error.message);
      console.log('Falling back to gpt-4o-mini-tts model');
      model = fallbackModel;
    }
  }
  // Standard TTS processing with fallback model
  // For standard TTS, we need to remove the parenthetical cues since it can't interpret them
  console.log('Using standard TTS model - removing parenthetical cues from text');

  // Remove all text within parentheses
  const cleanedText = text.replace(/\([^)]*\)\s*/g, '');

  const url = 'https://api.openai.com/v1/audio/speech';
  const payload = {
    model: model,
    input: cleanedText,
    voice: voice,
    response_format: options.format || 'mp3',
    speed: speed
  };
  const response = await axios({
    method: 'post',
    url: url,
    data: payload,
    headers: {
      'Authorization': `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer'
  });
  return Buffer.from(response.data);
}

/**
 * Use Azure for TTS conversion
 */
async function useAzureTTS(text, voice, options = {}) {
  if (!AZURE_API_KEY) {
    throw new Error('AZURE_API_KEY environment variable is not set');
  }

  // Remove all text within parentheses for Azure TTS
  const cleanedText = text.replace(/\([^)]*\)\s*/g, '');

  const voiceParts = voice.split('-');
  if (voiceParts.length < 3) {
    throw new Error('Azure voice ID format should be: language-region-name, e.g., en-US-JennyNeural');
  }
  const language = `${voiceParts[0]}-${voiceParts[1]}`;
  const url = `https://${AZURE_REGION}.tts.speech.microsoft.com/cognitiveservices/v1`;
  let ssml = `<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xmlns:mstts="https://www.w3.org/2001/mstts" xml:lang="${language}">`;
  if (options.style) {
    ssml += `<voice name="${voice}"><mstts:express-as style="${options.style}" styledegree="${options.styledegree || 1.0}">${cleanedText}</mstts:express-as></voice>`;
  } else {
    ssml += `<voice name="${voice}">${cleanedText}</voice>`;
  }
  ssml += '</speak>';
  const response = await axios({
    method: 'post',
    url: url,
    data: ssml,
    headers: {
      'Ocp-Apim-Subscription-Key': AZURE_API_KEY,
      'Content-Type': 'application/ssml+xml',
      'X-Microsoft-OutputFormat': options.format || 'audio-24khz-96kbitrate-mono-mp3',
      'User-Agent': 'PrayerAppTTS'
    },
    responseType: 'arraybuffer'
  });
  return Buffer.from(response.data);
}

// Execute main if run directly
if (require.main === module) {
  if (!textFilePath || !outputPath) {
    console.error('Usage: node tts_service.js <text_file_path> [provider] [voice_id] <output_path> [options_json]');
    console.error('Example: node tts_service.js prayer.txt openai alloy output.mp3 \'{"stability":0.5}\'');
    process.exit(1);
  }
  convertTextToSpeech().catch(err => {
    console.error('Fatal error:', err);
    process.exit(1);
  });
}

module.exports = {
  convertTextToSpeech,
  useOpenAITTS,
  useAzureTTS
};
