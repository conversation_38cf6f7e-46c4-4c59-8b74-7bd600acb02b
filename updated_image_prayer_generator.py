"""
Sacred Symbol Image Prayer Generator

This module extracts symbolic and sacred imagery from unified prayers, creates abstract
image prompts focusing on spiritual symbols rather than depicting specific people,
and generates beautiful devotional art using the fal.ai API.
"""

import os
import json
import time
import requests
import traceback
import asyncio
import aiohttp
import random
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv
from models.prayer_state import PrayerState # Import PrayerState

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

# Get the fal.ai API key
FAL_API_KEY = os.getenv("FAL_API_KEY")
if not FAL_API_KEY:
    logger.warning("[WARNING] FAL_API_KEY environment variable is not set. fal.ai image generation will be disabled.")
else:
    logger.info(f"[INFO] Found fal.ai API key: {FAL_API_KEY[:5]}...")

# Gemini API key (using GOOGLE_API_KEY for compatibility)
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
if not GEMINI_API_KEY:
    logger.warning("[WARNING] Neither GEMINI_API_KEY nor GOOGLE_API_KEY environment variable is set. Using fallback scene extraction.")

# Constants
SAVED_PRAYERS_DIR = Path("Saved_Prayers")
IMAGES_DIR = SAVED_PRAYERS_DIR / "Images"

# Ensure directories exist
SAVED_PRAYERS_DIR.mkdir(exist_ok=True)
IMAGES_DIR.mkdir(exist_ok=True)

# fal.ai model information
FAL_FLUX_DEV_MODEL_ID = "fal-ai/flux/dev" # Using the dev model for potentially newer features

# Define standard 16:9 resolutions suitable for diffusion models
ASPECT_RATIO_16_9 = {
    "SD": (1024, 576), # Standard Definition 16:9
    "HD": (1280, 720), # High Definition 16:9
    "FHD": (1920, 1080) # Full High Definition 16:9 - May be too large for some models
}

class UpdatedImagePrayerGenerator:
    """
    An improved class to generate personalized, trippy, and beautiful image prayers
    from unified prayer text using fal.ai, with 16:9 aspect ratio and enhanced prompting.
    """

    def __init__(self, gemini_api_key: str = None, fal_api_key: str = None):
        """
        Initialize the UpdatedImagePrayerGenerator.

        Args:
            gemini_api_key: API key for Google Gemini.
            fal_api_key: API key for fal.ai.
        """
        self.gemini_api_key = gemini_api_key or GEMINI_API_KEY
        self.fal_api_key = fal_api_key or FAL_API_KEY

        if not self.fal_api_key:
            raise ValueError("FAL_API_KEY is required for image generation.")

        # Image output format (png or jpeg)
        self.image_format = os.getenv("IMAGE_FORMAT", "png").lower()

        # Gemini API endpoints for different tasks
        self.gemini_pro_endpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent" # Using latest Pro
        self.gemini_flash_endpoint = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent" # Using latest Flash

        # fal.ai API endpoint base for the queue
        self.fal_queue_base_url = f"https://queue.fal.run/{FAL_FLUX_DEV_MODEL_ID}"
        self.fal_headers = {
            "Authorization": f"Key {self.fal_api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        # Select desired 16:9 resolution (e.g., SD for faster generation)
        self.width, self.height = ASPECT_RATIO_16_9["SD"]

    async def generate_image_prayers(self, unified_prayer_filepath: str, prayer_focus_theme: str, num_scenes: int = 1) -> List[str]:
        """
        Generate image prayers asynchronously from a unified prayer file using fal.ai.

        Args:
            unified_prayer_filepath: Path to the unified prayer markdown file.
            prayer_focus_theme: The specific theme or focus of the prayer for personalization.
            num_scenes: Number of scenes to extract.

        Returns:
            List of paths to generated images.
        """
        try:
            logger.info("\n" + "=" * 60)
            logger.info("STARTING ASYNC IMAGE PRAYER GENERATION (fal.ai - 16:9 Enhanced)")
            logger.info("=" * 60)
            logger.info(f"Unified Prayer Filepath: {unified_prayer_filepath}")
            logger.info(f"Prayer Focus Theme: {prayer_focus_theme}")
            logger.info(f"Target Aspect Ratio: 16:9 ({self.width}x{self.height})")

            # Read the unified prayer text
            try:
                if not Path(unified_prayer_filepath).exists():
                    raise FileNotFoundError(f"File does not exist: {unified_prayer_filepath}")
                with open(unified_prayer_filepath, 'r', encoding='utf-8') as f:
                    unified_prayer_text = f.read()
                logger.info(f"Successfully read unified prayer from {unified_prayer_filepath}")
            except FileNotFoundError:
                logger.error(f"[ERROR] Unified prayer file not found: {unified_prayer_filepath}")
                return []
            except Exception as e:
                logger.error(f"[ERROR] Failed to read unified prayer file: {e}")
                return []

            # Extract the run directory from the filepath
            run_dir = Path(unified_prayer_filepath).parent.name

            # Create output directory
            output_dir = IMAGES_DIR / run_dir
            output_dir.mkdir(exist_ok=True)
            logger.info(f"Output directory ensured: {output_dir}")

            # Extract scenes from the unified prayer, incorporating the focus theme
            logger.info("\n[1/3] Extracting personalized spiritual scenes...")
            scenes = await self.extract_scenes(unified_prayer_text, prayer_focus_theme, num_scenes) # Pass theme

            if not scenes:
                logger.error("[ERROR] Failed to extract any personalized scenes from the prayer text.")
                return []

            logger.info(f"[SUCCESS] Extracted {len(scenes)} personalized spiritual scenes.")

            # Create image prompts from scenes, incorporating the focus theme
            logger.info("\n[2/3] Creating detailed, trippy, and beautiful image prompts...")
            image_prompts = await self.create_image_prompts(scenes, prayer_focus_theme) # Pass theme

            if not image_prompts:
                logger.error("[ERROR] Failed to create image prompts.")
                return []

            logger.info(f"[SUCCESS] Created {len(image_prompts)} detailed image prompts.")

            # Generate images from prompts asynchronously
            logger.info("\n[3/3] Generating images using fal.ai API...")
            image_paths = await self.generate_images(image_prompts, run_dir) # Made async

            if not image_paths:
                logger.error("[ERROR] Failed to generate any images.")
                return []

            logger.info(f"[SUCCESS] Generated {len(image_paths)} beautiful image prayers using fal.ai.")

            logger.info("\n" + "=" * 60)
            logger.info("ASYNC IMAGE PRAYER GENERATION COMPLETE")
            logger.info("=" * 60)

            return image_paths

        except Exception as e:
            logger.error(f"[ERROR] An unexpected error occurred during async image prayer generation: {str(e)}")
            traceback.print_exc()
            return []

    async def extract_scenes(self, unified_prayer_text: str, prayer_focus_theme: str, num_scenes: int = 3) -> List[str]:
        """
        Extract powerful, personalized scenes from the unified prayer text (now async).

        Args:
            unified_prayer_text: The unified prayer text.
            prayer_focus_theme: The specific theme or focus of the prayer for personalization.
            num_scenes: Number of scenes to extract.

        Returns:
            List of extracted scene descriptions.
        """
        # Try using Gemini API if available
        if self.gemini_api_key:
            try:
                # Run Gemini call in a separate thread to avoid blocking asyncio loop
                loop = asyncio.get_running_loop()
                return await loop.run_in_executor(None, self._extract_scenes_gemini_sync, unified_prayer_text, prayer_focus_theme, num_scenes)
            except Exception as e:
                logger.warning(f"[WARNING] Gemini scene extraction failed: {str(e)}")
                logger.info("[INFO] Falling back to rule-based scene extraction.")

        # Fallback to rule-based extraction (can remain synchronous)
        # Pass theme to rule-based as well for potential future use
        return self._extract_scenes_rule_based(unified_prayer_text, prayer_focus_theme, num_scenes)

    def _extract_scenes_gemini_sync(self, unified_prayer_text: str, prayer_focus_theme: str, num_scenes: int) -> List[str]:
        """
        Synchronous helper for Gemini scene extraction, focusing on sacred symbols and abstract imagery
        rather than depicting specific people.

        Args:
            unified_prayer_text: The unified prayer text.
            prayer_focus_theme: The specific theme or focus of the prayer for personalization.
            num_scenes: Number of scenes to extract.

        Returns:
            List of extracted scene descriptions.
        """
        prompt = f"""
        You are an expert at identifying symbolic, sacred, and visually rich abstract scenes from spiritual text.
        The overall prayer focus is: **{prayer_focus_theme}**

        I'll provide you with a unified prayer text. Your task is to extract {num_scenes} powerful,
        visually compelling symbolic scenes that would make beautiful, meaningful, and imaginative images
        reflecting the prayer's focus theme: "{prayer_focus_theme}".

        IMPORTANT CONSTRAINTS:
        - DO NOT describe any specific human figures, faces, or people
        - Focus on sacred symbols, abstract representations, religious imagery, and natural elements
        - Use symbolic elements like light, sacred geometry, mandalas, and spiritual objects instead of people
        - Create uplifting scenes with universal spiritual imagery that transcends specific individuals

        For each scene, focus on:
        1. **Sacred Symbolism**: Use multi-faith holy symbols, sacred geometry, and spiritual imagery
        2. **Nature Elements**: Incorporate cosmic, celestial, and natural imagery as metaphors
        3. **Abstract Representation**: Use abstract visual metaphors rather than depicting specific entities
        4. **Universal Elements**: Focus on light, energy, sacred objects, and symbolic abstractions
        5. **Spiritual Architecture**: Consider temples, sacred spaces, and cosmic structures
        6. **Transcendent Beauty**: Create scenes of transcendent beauty without depicting specific people

        Format your response as a JSON array of scene descriptions. Each description should be
        a detailed paragraph (3-5 sentences) describing a symbolic visual scene rich with sacred imagery
        relevant to "{prayer_focus_theme}", without depicting any specific human figures.

        Here is the unified prayer text:

        {unified_prayer_text}
        """

        # Call Gemini Pro API for complex scene extraction (synchronous call)
        response = self._call_gemini_api_sync(prompt, use_pro=True)

        # Extract the scenes from the response (logic remains the same)
        try:
            # Try to parse as JSON
            scenes_text = response.strip()

            # Find JSON array in the text if it's not pure JSON
            if not scenes_text.startswith('['):
                import re
                json_match = re.search(r'\[\s*{.*}\s*\]', scenes_text, re.DOTALL)
                if json_match:
                    scenes_text = json_match.group(0)
                else:
                    # If no JSON array found, look for numbered scenes
                    scenes = []
                    scene_pattern = re.compile(r'Scene \d+:(.*?)(?=Scene \d+:|$)', re.DOTALL)
                    matches = scene_pattern.findall(scenes_text)
                    if matches:
                        return [match.strip() for match in matches]

                    # If still no scenes found, split by double newlines
                    return [scene.strip() for scene in scenes_text.split('\n\n') if scene.strip()]

            scenes = json.loads(scenes_text)

            # Handle different JSON formats
            if isinstance(scenes, list):
                if all(isinstance(item, str) for item in scenes):
                    return scenes
                elif all(isinstance(item, dict) for item in scenes):
                    # Extract the scene description from each dictionary
                    return [item.get('description', '') for item in scenes]

            # Fallback: return the raw text split by newlines
            return [scenes_text]

        except json.JSONDecodeError:
            # If JSON parsing fails, try to extract scenes manually
            logger.warning("[WARNING] Failed to parse JSON response. Extracting scenes manually.")

            # Split by numbered scenes
            import re
            scenes = []
            scene_pattern = re.compile(r'Scene \d+:(.*?)(?=Scene \d+:|$)', re.DOTALL)
            matches = scene_pattern.findall(response)

            if matches:
                return [match.strip() for match in matches]

            # If no numbered scenes, split by double newlines
            return [scene.strip() for scene in response.split('\n\n') if scene.strip()]

    def _extract_scenes_rule_based(self, unified_prayer_text: str, prayer_focus_theme: str, num_scenes: int) -> List[str]:
        """
        Extract scenes using a rule-based approach as fallback (remains synchronous).
        Includes basic theme filtering.

        Args:
            unified_prayer_text: The unified prayer text.
            prayer_focus_theme: The specific theme or focus of the prayer.
            num_scenes: Number of scenes to extract.

        Returns:
            List of extracted scene descriptions.
        """
        logger.info(f"[INFO] Using rule-based scene extraction (Theme: {prayer_focus_theme}).")

        # Split text into paragraphs
        paragraphs = [p for p in unified_prayer_text.split('\n\n') if p.strip()]

        # Enhanced list of visual and symbolic keywords
        visual_symbolic_keywords = [
            'light', 'see', 'vision', 'image', 'picture', 'scene',
            'behold', 'witness', 'view', 'landscape', 'nature',
            'sky', 'mountain', 'ocean', 'river', 'forest', 'tree',
            'sun', 'moon', 'star', 'divine', 'sacred', 'holy',
            'heaven', 'spirit', 'soul', 'heart', 'mind', 'body',
            'peace', 'love', 'joy', 'harmony', 'unity', 'beauty',
            'wisdom', 'truth', 'path', 'journey', 'bridge', 'connection',
            'transcend', 'ascend', 'illuminate', 'radiate', 'glow',
            'eternal', 'infinite', 'cosmic', 'universe', 'creation',
            'symbol', 'metaphor', 'allegory', 'vessel', 'container',
            'thread', 'weave', 'tapestry', 'garden', 'seed', 'root',
            'branch', 'flower', 'bloom', 'crystal', 'gem', 'jewel',
            'flame', 'fire', 'water', 'earth', 'air', 'element'
        ]

        visual_paragraphs = []
        for para in paragraphs:
            if any(keyword in para.lower() for keyword in visual_symbolic_keywords):
                visual_paragraphs.append(para)

        # If we don't have enough visual paragraphs, take the longest paragraphs
        if len(visual_paragraphs) < num_scenes:
            remaining = num_scenes - len(visual_paragraphs)
            non_visual_paragraphs = [p for p in paragraphs if p not in visual_paragraphs]
            sorted_by_length = sorted(non_visual_paragraphs, key=len, reverse=True)
            visual_paragraphs.extend(sorted_by_length[:remaining])

        # Limit to requested number of scenes
        scenes = visual_paragraphs[:num_scenes]

        # If we still don't have enough scenes, create some imaginative symbolic scenes without any people
        if len(scenes) < num_scenes:
            imaginative_scenes = [
                "An ancient tree of life stands at the center of a cosmic garden, its roots extending deep into the earth and its branches reaching toward the heavens. Golden threads of light connect the tree to countless smaller trees representing diverse spiritual traditions. The entire scene is bathed in a soft, ethereal glow that emanates from within each element, symbolizing the divine essence present in all forms of spiritual wisdom.",

                "A magnificent cosmic bridge spans between the earthly realm and the divine dimension, constructed from crystalline light and adorned with symbols from the world's wisdom traditions. Streams of multicolored light flow across the bridge, each representing a different spiritual path. As these luminous currents approach the summit, they merge into a brilliant tapestry of unified light, forming a radiant mandala that pulses with divine energy.",

                "A sacred mountain rises from a sea of consciousness, its peak touching the realm of divine wisdom. Around its base, symbols from various spiritual traditions are arranged in a perfect circle. From each symbol, a thread of golden light extends upward, weaving together into a luminous spiral that ascends the mountain. At the summit, these threads form a radiant crystal sphere reflecting infinite divine facets, representing the unified essence of all prayers and spiritual aspirations."
            ]
            scenes.extend(imaginative_scenes[:num_scenes - len(scenes)])

        return scenes

    async def create_image_prompts(self, scenes: List[str], prayer_focus_theme: str) -> List[str]:
        """
        Create detailed, personalized, trippy, and beautiful image prompts from scene descriptions (now async).

        Args:
            scenes: List of extracted scene descriptions.
            prayer_focus_theme: The specific theme or focus of the prayer for personalization.

        Returns:
            List of detailed image prompts.
        """
        # Try using Gemini API if available
        if self.gemini_api_key:
            try:
                # Run Gemini call in a separate thread
                loop = asyncio.get_running_loop()
                return await loop.run_in_executor(None, self._create_image_prompts_gemini_sync, scenes, prayer_focus_theme)
            except Exception as e:
                logger.warning(f"[WARNING] Gemini prompt creation failed: {str(e)}")
                logger.info("[INFO] Falling back to rule-based prompt creation.")

        # Fallback to rule-based prompt creation (can remain synchronous)
        return self._create_image_prompts_rule_based(scenes, prayer_focus_theme)

    def _create_image_prompts_gemini_sync(self, scenes: List[str], prayer_focus_theme: str) -> List[str]:
        """
        Synchronous helper for Gemini prompt creation, focusing on sacred symbols and abstract imagery
        rather than depicting specific people.

        Args:
            scenes: List of scene descriptions.
            prayer_focus_theme: The specific theme or focus of the prayer.

        Returns:
            List of detailed image prompts.
        """
        image_prompts = []
        # Enhanced negative prompt to avoid people generation
        negative_prompt = "text, words, letters, writing, signature, watermark, blurry, low quality, deformed, unrealistic, simple, plain, human, person, face, portrait, people" 

        for i, scene in enumerate(scenes):
            logger.info(f"  Creating prompt for scene {i+1}/{len(scenes)} (Theme: {prayer_focus_theme})...")

            prompt = f"""
            You are an expert AI image prompt engineer specializing in creating **spiritual, symbolic, and abstract sacred art** for the fal.ai Flux model (16:9 aspect ratio).

            **Prayer Focus Theme:** "{prayer_focus_theme}"
            **Scene Description:** "{scene}"

            **Your Task:** Transform the scene description into an exceptional, symbolic image prompt focused on sacred imagery and abstract spiritual concepts. The final image should be a masterpiece of spiritual visionary art reflecting universal sacred concepts.

            **CRITICAL CONSTRAINTS:**
            - **DO NOT describe or depict any specific human figures, faces, or people**
            - **Use sacred symbols, abstract forms, and architectural/geometric elements instead**
            - **Focus on universal spiritual imagery like mandalas, sacred geometry, light, and natural elements**

            **Prompting Guidelines:**
            1.  **Sacred Symbolism:** Use specific sacred symbols from multiple faith traditions (Tree of Life, Mandalas, Sacred Geometry, Merkabas, etc.)
            2.  **Abstract Representation:** Replace any human figures with abstract representations - streams of light, geometric forms, or spiritual symbols
            3.  **Setting & Environment:** Create transcendent, cosmic, or natural settings with a powerful, ethereal atmosphere
            4.  **Visual Style:** Blend styles like "visionary art," "sacred geometry," "ethereal digital painting," and "cosmic art" - favor beauty and intricacy
            5.  **Divine Light:** Use dramatic and symbolic lighting like "iridescent divine light," "cascading celestial rays," "bioluminescent glow"
            6.  **Color Symphony:** Create vibrant, otherworldly color palettes - "cosmic purples and blues," "liquid gold and emerald green," "rainbow iridescence"
            7.  **Sacred Composition:** Design a dynamic 16:9 composition with sacred proportions or geometric balance
            8.  **Keywords to Include:** `16:9, visionary art, psychedelic, sacred geometry, mystical, spiritual, intricate detail, cinematic lighting, no humans, sacred symbols, breathtaking beauty, masterpiece, 8k`

            **Example Transformation:**
            *   *Scene:* "A bridge of light connecting diverse people."
            *   *Theme:* "Unity in Healing"
            *   *Poor Prompt (don't do this):* "A cosmic bridge with diverse figures walking across it..." (Describes people)
            *   *Excellent Prompt (do this):* "A breathtaking cosmic bridge woven from iridescent threads of healing light, spanning across a nebula-filled sky (16:9 aspect ratio). Instead of human figures, streams of multicolored energy from different spiritual traditions (represented by their sacred symbols) flow toward the center, merging into a unified mandala of light. Below the bridge, a tranquil river reflects swirling galaxies while sacred lotus flowers bloom. Style: Visionary art meets ethereal digital painting with sacred geometry patterns integrated into the bridge structure. 16:9 aspect ratio, visionary art, psychedelic, spiritual, sacred geometry, intricate detail, no humans, abstract forms, hyperrealistic, cinematic lighting, breathtaking beauty, masterpiece, 8k"

            **Now, generate the prompt for the provided scene description and theme.** Return ONLY the final positive image prompt text, ready for the AI. Remember: NO HUMAN FIGURES OR FACES.
            """

            # Call Gemini Flash API for faster, imaginative prompt creation (synchronous call)
            positive_prompt = self._call_gemini_api_sync(prompt, use_pro=False) # Use Flash model

            # Clean up the response and add negative prompt (though fal.ai might not use it explicitly, it's good practice)
            # For fal.ai, we only need the positive prompt in the payload.
            final_prompt = positive_prompt.strip().strip('"\'')
            # We could store the negative prompt if the API supported it: final_prompt += f" --neg {negative_prompt}"
            image_prompts.append(final_prompt)

        return image_prompts

    def _create_image_prompts_rule_based(self, scenes: List[str], prayer_focus_theme: str) -> List[str]:
        """
        Create image prompts using a rule-based approach as fallback (remains synchronous).
        Enhanced for personalization and trippy/beautiful style.

        Args:
            scenes: List of scene descriptions.
            prayer_focus_theme: The specific theme or focus of the prayer.

        Returns:
            List of detailed image prompts.
        """
        logger.info(f"[INFO] Using rule-based prompt creation (Theme: {prayer_focus_theme}).")

        prompts = []

        # Enhanced Art styles focusing on trippy/beautiful/spiritual
        art_styles = [
            "ethereal visionary art with psychedelic fractal patterns",
            "luminous spiritual painting with sacred geometry overlays",
            "mystical fantasy art, glowing with cosmic energy",
            "transcendent digital painting, style of Alex Grey and Android Jones",
            "symbolic surrealism with vibrant, otherworldly colors",
            "celestial dreamscape, hyperrealistic details",
            "bio-luminescent nature scene infused with divine light",
            "liquid light and energy flows, abstract spiritual art"
        ]

        # Enhanced Lighting and atmosphere options
        lighting_options = [
            "illuminated by iridescent celestial light",
            "soft, ethereal bioluminescent glow emanating from within",
            "dramatic volumetric lighting with god rays breaking through cosmic clouds",
            "serene ultraviolet and deep indigo hues creating a mystical atmosphere",
            "warm, pulsating golden light radiating divine love",
            "shimmering prismatic light refracting through crystalline structures",
            "aurora borealis style lights dancing in a nebula sky"
        ]

        # Enhanced Symbolic elements
        symbolic_elements = [
            "a Tree of Life made of pure light, roots intertwined with galaxies",
            "a crystalline bridge arching over a river of liquid starlight",
            "interwoven threads of vibrant energy connecting glowing hearts",
            "a sacred chalice overflowing with iridescent cosmic nectar",
            "a multifaceted merkaba star radiating geometric patterns",
            "a spiral galaxy staircase ascending towards a blinding white light",
            "a celestial garden where flowers bloom with fractal light",
            "a luminous mandala portal opening to higher dimensions"
        ]

        # Keywords for quality and style
        keywords = "16:9 aspect ratio, visionary art, psychedelic, spiritual, sacred geometry, intricate detail, hyperrealistic, cinematic lighting, breathtaking beauty, masterpiece, 8k"

        for scene in scenes:
            # Select random elements
            style = random.choice(art_styles)
            lighting = random.choice(lighting_options)
            symbol = random.choice(symbolic_elements)

            # Integrate the prayer focus theme more directly
            themed_scene = f"{scene.strip()} visually representing the theme of '{prayer_focus_theme}'"

            # Create a more imaginative prompt
            prompt = f"{themed_scene}. Featuring {symbol}. {lighting}. Style: {style}. {keywords}"
            prompts.append(prompt)

        return prompts

    async def generate_images(self, image_prompts: List[str], run_dir: str) -> List[str]:
        """
        Generate images asynchronously from prompts using the fal.ai API.
        """
        output_dir = IMAGES_DIR / run_dir
        output_dir.mkdir(exist_ok=True)

        tasks = []
        # Use a single session for all requests
        async with aiohttp.ClientSession(headers=self.fal_headers) as session:
            for i, prompt in enumerate(image_prompts):
                logger.info(f"  Queueing image generation for prompt {i+1}/{len(image_prompts)}...")
                logger.info(f"  Prompt: {prompt[:100]}...")
                # Create a task for each image generation
                task = asyncio.create_task(self._generate_with_fal_async(session, prompt, i, output_dir))
                tasks.append(task)

            # Wait for all tasks to complete and gather results
            results = await asyncio.gather(*tasks)

        # Filter out None results (failures)
        image_paths = [path for path in results if path is not None]
        return image_paths

    async def _generate_with_fal_async(self, session: aiohttp.ClientSession, prompt: str, index: int, output_dir: Path) -> Optional[str]:
        """
        Generate an image asynchronously using the fal.ai queue API.

        Args:
        Args:
            session: The aiohttp client session.
            prompt: The image prompt.
            index: The index of the prompt.
            output_dir: The output directory.

        Returns:
            Path to the generated image or None if failed.
        """
        max_retries = 3
        request_id = None
        status_url = None
        response_url = None

        for attempt in range(1, max_retries + 1):
            try:
                # 1. Submit the request to the queue
                logger.info(f"  [fal-{index+1}] Submitting job (attempt {attempt}/{max_retries})...")
                submit_payload = {
                    "prompt": prompt,
                    "seed": random.randint(1, 9999999),
                    # Use the defined 16:9 width and height
                    "width": self.width,
                    "height": self.height,
                    "num_images": 1
                }
                logger.debug(f"  [fal-{index+1}] Submit Payload: {json.dumps(submit_payload, indent=2)}")
                async with session.post(self.fal_queue_base_url, json=submit_payload) as response:
                    response_text = await response.text()
                    logger.debug(f"  [fal-{index+1}] Submit Response Status: {response.status}")
                    logger.debug(f"  [fal-{index+1}] Submit Response Body: {response_text}")

                    if response.status == 429: # Rate limit
                        logger.warning(f"  [fal-{index+1}] Rate limit hit on submit. Retrying...")
                        await asyncio.sleep(2 ** attempt) # Exponential backoff
                        continue
                    response.raise_for_status() # Raise for other errors (4xx, 5xx)
                    submit_data = json.loads(response_text) # Parse from text
                    request_id = submit_data.get("request_id")
                    status_url = submit_data.get("status_url")
                    response_url = submit_data.get("response_url")
                    if not request_id or not status_url or not response_url:
                        raise ValueError(f"Missing required fields in fal.ai submit response: {submit_data}")
                    logger.info(f"  [fal-{index+1}] Job submitted. Request ID: {request_id}")
                    break # Submission successful
            except (aiohttp.ClientError, ValueError, asyncio.TimeoutError) as e:
                logger.error(f"  [ERROR][fal-{index+1}] Submit attempt {attempt} failed: {e}")
                if attempt == max_retries:
                    logger.error(f"  [ERROR][fal-{index+1}] Failed to submit job after {max_retries} attempts.")
                    return None
                await asyncio.sleep(2 ** attempt) # Exponential backoff before next submit attempt

        if not status_url:
            return None # Failed to submit

        # 2. Poll the status URL
        poll_interval = 1 # Start with 1 second
        max_poll_time = 300 # 5 minutes timeout for polling
        start_time = time.time()

        while time.time() - start_time < max_poll_time:
            try:
                logger.info(f"  [fal-{index+1}] Checking status (polling)...")
                async with session.get(status_url) as response:
                    response_text = await response.text()
                    logger.debug(f"  [fal-{index+1}] Status Poll Response Status: {response.status}")
                    logger.debug(f"  [fal-{index+1}] Status Poll Response Body: {response_text}")

                    if response.status == 429: # Rate limit
                        logger.warning(f"  [fal-{index+1}] Rate limit hit on status poll. Retrying...")
                        await asyncio.sleep(poll_interval * 1.5) # Increase delay slightly
                        continue
                    response.raise_for_status()
                    status_data = json.loads(response_text) # Parse from text
                    status = status_data.get("status")
                    logger.info(f"  [fal-{index+1}] Current status: {status}")

                    if status == "COMPLETED":
                        logger.info(f"  [fal-{index+1}] Job completed!")
                        break # Exit polling loop
                    elif status in ["IN_QUEUE", "IN_PROGRESS"]:
                        await asyncio.sleep(poll_interval)
                        poll_interval = min(poll_interval * 1.2, 15) # Increase interval, max 15s
                    else: # Handle unexpected statuses (e.g., FAILED)
                        raise ValueError(f"fal.ai job failed or unknown status: {status} - {status_data}")

            except (aiohttp.ClientError, ValueError, asyncio.TimeoutError) as e:
                logger.error(f"  [ERROR][fal-{index+1}] Status polling failed: {e}")
                # Allow polling to continue after transient errors, but fail if persistent
                await asyncio.sleep(poll_interval) # Wait before retrying poll

            if time.time() - start_time >= max_poll_time:
                 logger.error(f"  [ERROR][fal-{index+1}] Polling timed out after {max_poll_time} seconds.")
                 return None

        if status != "COMPLETED":
             logger.error(f"  [ERROR][fal-{index+1}] Job did not complete successfully. Final status: {status}")
             return None

        # 3. Get the final response (image URL)
        try:
            logger.info(f"  [fal-{index+1}] Fetching final result...")
            async with session.get(response_url) as response:
                response_text = await response.text()
                logger.debug(f"  [fal-{index+1}] Final Result Response Status: {response.status}")
                logger.debug(f"  [fal-{index+1}] Final Result Response Body: {response_text}")

                response.raise_for_status()
                result_data = json.loads(response_text) # Parse from text

                # Extract image URL (adjust based on actual fal.ai flux/dev response structure)
                image_url = None
                if 'images' in result_data and len(result_data['images']) > 0:
                    image_url = result_data['images'][0]['url'] # Assuming structure like {'images': [{'url': '...'}]}
                elif 'image' in result_data and isinstance(result_data['image'], dict) and 'url' in result_data['image']:
                     image_url = result_data['image']['url'] # Assuming structure like {'image': {'url': '...'}}
                elif 'output' in result_data and 'images' in result_data['output'] and len(result_data['output']['images']) > 0:
                     image_url = result_data['output']['images'][0]['url'] # Another possible structure
                else:
                    raise ValueError(f"Could not find image URL in fal.ai result: {result_data}")

                logger.info(f"  [fal-{index+1}] Image URL received: {image_url[:50]}...")

                # 4. Download the image
                logger.info(f"  [fal-{index+1}] Downloading image...")
                # Get current timestamp for unique filename
                current_timestamp = time.strftime("%Y%m%d_%H%M%S")
                image_filename = f"prayer_image_{current_timestamp}_{index+1}_fal.{self.image_format}"
                image_path = output_dir / image_filename

                # Use a separate session for downloading potentially large files without auth headers
                async with aiohttp.ClientSession() as download_session:
                    async with download_session.get(image_url) as img_response:
                        img_response.raise_for_status()
                        with open(image_path, 'wb') as f:
                            while True:
                                chunk = await img_response.content.read(1024)
                                if not chunk:
                                    break
                                f.write(chunk)

                logger.info(f"  [SUCCESS][fal-{index+1}] fal.ai image saved to {image_path}")
                return str(image_path)

        except (aiohttp.ClientError, ValueError, asyncio.TimeoutError) as e:
            logger.error(f"  [ERROR][fal-{index+1}] Failed to get/download result: {e}")
            return None


    def _call_gemini_api_sync(self, prompt: str, use_pro: bool = True) -> str:
        """
        Synchronous call to the Gemini API with a prompt and retry logic.

        Args:
            prompt: The prompt to send to Gemini.
            use_pro: Whether to use the Pro model (for complex tasks) or Flash model (for simpler tasks).

        Returns:
            The response text from Gemini.
        """
        if not self.gemini_api_key:
            raise ValueError("Gemini API key is not set.")

        # Use Pro model for complex scene extraction, Flash model for simpler prompt creation
        endpoint = self.gemini_pro_endpoint if use_pro else self.gemini_flash_endpoint
        url = f"{endpoint}?key={self.gemini_api_key}"

        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                payload = {
                    "contents": [{"parts": [{"text": prompt}]}],
                    "generationConfig": {
                        "temperature": 0.4,
                        "topP": 0.95,
                        "topK": 40,
                        "maxOutputTokens": 8192 if use_pro else 4096
                    }
                }
                headers = {"Content-Type": "application/json"}

                # Using requests library for synchronous call
                response = requests.post(url, json=payload, headers=headers, timeout=120) # Added timeout

                if response.status_code == 429:
                    logger.warning(f"[WARNING] Gemini API rate limit reached (attempt {retry_count+1}/{max_retries}).")
                    retry_count += 1
                    if retry_count < max_retries:
                        wait_time = 2 ** retry_count
                        logger.warning(f"Retrying in {wait_time} seconds...")
                        time.sleep(wait_time)
                    else:
                        raise Exception(f"Gemini API rate limit reached after {max_retries} attempts.")
                elif response.status_code != 200:
                    raise Exception(f"Gemini API error: {response.status_code} - {response.text}")
                else:
                    response_json = response.json()
                    try:
                        # Handle potential variations in response structure
                        candidates = response_json.get("candidates", [])
                        if candidates and "content" in candidates[0] and "parts" in candidates[0]["content"]:
                            return candidates[0]["content"]["parts"][0].get("text", "")
                        else:
                             raise Exception(f"Unexpected Gemini response structure: {response_json}")
                    except (KeyError, IndexError, TypeError) as e:
                        raise Exception(f"Failed to extract text from Gemini response: {e} - Response: {response_json}")

            except requests.exceptions.RequestException as e:
                 logger.error(f"[ERROR] Network error calling Gemini API: {e}")
                 retry_count += 1
                 if retry_count >= max_retries:
                     raise Exception(f"Failed to call Gemini API due to network errors after {max_retries} attempts: {e}")
                 wait_time = 2 ** retry_count
                 logger.warning(f"Retrying in {wait_time} seconds...")
                 time.sleep(wait_time)

            except Exception as e:
                # Handle non-network errors (like rate limits handled above or parsing errors)
                if "rate limit" not in str(e).lower():
                    raise # Re-raise non-rate-limit errors immediately

                # This part handles the rate limit exception raised after max retries
                retry_count += 1
                if retry_count >= max_retries:
                    raise Exception(f"Failed to call Gemini API after {max_retries} attempts: {e}")
                # Backoff logic is handled within the 429 check, this is redundant here
                # but kept for safety in case the exception is raised differently.
                wait_time = 2 ** retry_count
                logger.warning(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)


async def main(): # Make main async
    """
    Main async function to test the UpdatedImagePrayerGenerator.
    """
    # Check if fal.ai API key is set
    if not FAL_API_KEY:
        logger.error("[ERROR] FAL_API_KEY environment variable is not set.")
        logger.error("Please set it in your .env file or export it in your shell.")
        return

    # Initialize the UpdatedImagePrayerGenerator
    generator = UpdatedImagePrayerGenerator()

    # Find the most recent unified prayer file
    # Use Path.glob to find files matching the pattern within any subdirectory of SAVED_PRAYERS_DIR
    unified_prayer_files = list(SAVED_PRAYERS_DIR.glob("**/Unified_Prayer_*.md")) # Adjusted glob pattern
    if not unified_prayer_files:
        logger.error("[ERROR] No Unified_Prayer_*.md files found in any subdirectories of Saved_Prayers.")
        return

    # Sort by modification time (most recent first)
    unified_prayer_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    most_recent_file = unified_prayer_files[0]

    logger.info(f"[INFO] Using most recent unified prayer file: {most_recent_file}")

    # Generate image prayers asynchronously, providing a dummy theme for testing
    start_time = time.time()
    # Provide a sample prayer_focus_theme for testing purposes
    test_theme = "Finding peace in nature's embrace"
    logger.info(f"[INFO] Test Theme: {test_theme}")
    image_paths = await generator.generate_image_prayers(most_recent_file, test_theme, num_scenes=1) # Pass theme
    end_time = time.time()

    if image_paths:
        logger.info(f"\n[SUCCESS] Generated {len(image_paths)} image prayers in {end_time - start_time:.2f} seconds:")
        for path in image_paths:
            logger.info(f"- {path}")
    else:
        logger.error("\n[ERROR] Failed to generate any image prayers.")


if __name__ == "__main__":
    asyncio.run(main()) # Run the async main function
