"""
Prayer State Model Definition
This module defines the state objects used in the prayer generation workflow.
"""

from typing import TypedDict, List, Dict, Optional, Any

class FaithResearchData(TypedDict):
    """
    Structured research data for a specific faith tradition.
    Contains detailed information to enhance prayer authenticity.
    """
    divine_names: List[str]  # Names/titles for the divine in this tradition
    sacred_terms: List[str]  # Important religious terms and concepts
    prayer_structure: str  # How prayers are typically structured
    scriptural_references: List[str]  # Relevant quotes from sacred texts
    cultural_context: str  # Cultural/historical context for authenticity

class PrayerState(TypedDict):
    """
    Prayer state definition for the LangGraph workflow.
    Contains all the state data needed during the prayer generation process.
    """
    # Core prayer information
    prayer_focus_theme: str
    search_context: Optional[str]  # General context from search
    religious_terms: Dict[str, List[str]]  # Specific terms per religion

    # Enhanced research data (new)
    faith_research: Optional[Dict[str, FaithResearchData]]  # Detailed research per faith

    # Workflow state tracking
    spiritual_movements_to_process: List[str]
    individual_prayers: Dict[str, str]
    current_movement: Optional[str]
    run_output_dir: Optional[str]  # Directory for the current run's outputs
    error_message: Optional[str]  # To capture errors within the graph

    # Unified prayer generation fields
    combined_prayer_text: Optional[str]
    unified_prayer_markdown: Optional[str]
    unified_prayer_filename: Optional[str]
    unified_prayer_filepath: Optional[str]

    # TTS fields
    audio_conversion_requested: Optional[bool]  # Initial flag to enable TTS flow
    tts_confirmed: Optional[bool]  # User confirmation during the ask_audio_conversion step
    tts_error_message: Optional[str]  # Specific TTS error message
    tts_provider: Optional[str]
    tts_voice_id: Optional[str]
    tts_options: Optional[Dict]
    verbal_prayer_text: Optional[str]
    verbal_prayer_filepath: Optional[str]
    audio_filepath: Optional[str]

    # Image prayer fields
    image_prayer_requested: Optional[bool]  # Initial flag to enable image prayer generation
    image_prayer_generated: Optional[bool]  # Whether image prayers were successfully generated
    image_prayer_filepaths: Optional[List[str]]  # Paths to generated image prayer files
    
    # Video generation fields
    generate_video: Optional[bool]  # Flag to enable video generation
    generate_images: Optional[bool]  # Flag to enable image generation
    generated_videos: Optional[List[Dict]]  # List of generated video results
    generated_video: Optional[str]  # Primary video URL for unified prayer
    video_generation_error: Optional[str]  # Video generation error message
    video_generation_errors: Optional[List[Dict]]  # List of failed video generations
    
    # Async media processing fields
    sacred_scenes: Optional[List[Dict]]  # Sacred scenes for video/image generation
    scene_prompts: Optional[List[str]]  # Prompts used for scene generation
    generated_images: Optional[List[str]]  # List of generated image URLs
    generated_audio: Optional[str]  # Generated audio URL/path
    media_summary: Optional[Dict]  # Summary of all generated media
    
    # MCP and async processing fields
    mcp_manager: Optional[Any]  # MCP manager instance (async connector)
    execution_logs: List[str]  # Execution logs for tracking progress
    
    # Progress tracking for async operations
    async_tasks_status: Optional[Dict[str, str]]  # Status of async tasks
    parallel_processing_enabled: Optional[bool]  # Enable parallel media processing
