"""
Sacred Symbol Image Prayer Node

This module defines a LangGraph node for generating abstract spiritual imagery
that focuses on sacred symbols rather than depicting specific people.
"""

import os
import traceback
import asyncio
import logging
from typing import Dict, Any, List

from models.prayer_state import PrayerState
from updated_image_prayer_generator import UpdatedImagePrayerGenerator

logger = logging.getLogger(__name__)

def should_generate_image_prayer(state: PrayerState) -> str:
    """
    Conditional check: Should we generate image prayers?

    Args:
        state: The current prayer state

    Returns:
        "generate_image" if image prayer generation is requested, "skip_image" otherwise
    """
    # Check if image_prayer_requested is set to True by user input
    generate_image = False
    if isinstance(state, dict):
        generate_image = state.get('image_prayer_requested', False)
    else:
        generate_image = getattr(state, 'image_prayer_requested', False)

    logger.info(f"[CONDITIONAL] Should generate image prayer? {generate_image}")
    return "generate_image" if generate_image else "skip_image"

async def generate_image_prayer_node(state: PrayerState) -> PrayerState:
    """
    Generate image prayers from the unified prayer with robust error handling.
    This is now an async function to fit into the LangGraph async workflow.

    Args:
        state: The current prayer state

    Returns:
        Updated prayer state with image prayer information
    """
    logger.info("\n" + "=" * 60)
    logger.info("IMAGE PRAYER GENERATION NODE")
    logger.info("=" * 60)

    try:
        # Initialize the UpdatedImagePrayerGenerator
        generator = UpdatedImagePrayerGenerator()

        # Get necessary data from state
        if isinstance(state, dict):
            unified_prayer_filepath = state.get('unified_prayer_filepath')
            run_output_dir = state.get('run_output_dir')
            prayer_focus_theme = state.get('prayer_focus_theme') # Get the theme
        else:
            unified_prayer_filepath = getattr(state, 'unified_prayer_filepath', None)
            run_output_dir = getattr(state, 'run_output_dir', None)
            prayer_focus_theme = getattr(state, 'prayer_focus_theme', None) # Get the theme

        logger.info(f"Unified Prayer Filepath: {unified_prayer_filepath}")
        logger.info(f"Prayer Focus Theme: {prayer_focus_theme}")

        if not unified_prayer_filepath:
            error_msg = "No unified prayer filepath found in state."
            logger.error(f"[ERROR] {error_msg}")

            if isinstance(state, dict):
                state['error_message'] = state.get('error_message', '') + f" | {error_msg}"
                state['image_prayer_filepaths'] = []
                state['image_prayer_generated'] = False
            else:
                state.error_message = getattr(state, 'error_message', '') + f" | {error_msg}"
                state.image_prayer_filepaths = []
                state.image_prayer_generated = False

            return state

        if not prayer_focus_theme:
            error_msg = "No prayer focus theme found in state for personalization. Using fallback."
            logger.warning(f"[WARNING] {error_msg}")
            # Proceed without theme, but log warning
            prayer_focus_theme = "Universal spiritual connection" # Fallback theme

        # Generate image prayers with comprehensive error handling, passing the theme
        num_scenes = 3  # Default number of scenes to extract
        logger.info(f"[INFO] Calling image generator with theme: {prayer_focus_theme}")
        # Await the async function directly
        image_paths = await generator.generate_image_prayers(unified_prayer_filepath, prayer_focus_theme, num_scenes)

        # Update the state with image prayer information
        if isinstance(state, dict):
            state['generated_images'] = image_paths
            state['image_prayer_generated'] = bool(image_paths)
        else:
            state.generated_images = image_paths
            state.image_prayer_generated = bool(image_paths)

        if image_paths:
            logger.info(f"[SUCCESS] Generated {len(image_paths)} image prayers.")
            for i, path in enumerate(image_paths):
                logger.info(f"  {i+1}. {path}")
        else:
            logger.warning("[WARNING] No image prayers were generated.")

    except Exception as e:
        error_msg = f"Error generating image prayers: {str(e)}"
        logger.error(f"[ERROR] {error_msg}")
        traceback.print_exc()

        if isinstance(state, dict):
            state['error_message'] = state.get('error_message', '') + f" | {error_msg}"
            state['image_prayer_filepaths'] = []
            state['image_prayer_generated'] = False
        else:
            state.error_message = getattr(state, 'error_message', '') + f" | {error_msg}"
            state.image_prayer_filepaths = []
            state.image_prayer_generated = False

    logger.info("=" * 60)
    return state
