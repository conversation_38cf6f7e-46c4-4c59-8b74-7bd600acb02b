"""
SpiritSync API - FastAPI Application
This is the main entry point for the SpiritSync API.
"""

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import sys
import uuid
import asyncio
import j<PERSON>
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

# Add the parent directory to the path so we can import from the backend
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try to import the prayer workflow
try:
    from graph_definition_new import create_prayer_workflow
    from models.prayer_state import PrayerState
    from utils.file_utils import create_run_directory
    print("[INFO] Successfully imported backend modules")
except ImportError as e:
    print(f"[WARNING] Error importing backend modules: {e}")
    print("[WARNING] Some functionality may not work correctly")

# Create FastAPI app
app = FastAPI(title="SpiritSync API", version="1.0.0")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development; restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Try to mount static files for serving prayer assets
try:
    if os.path.exists("Saved_Prayers"):
        app.mount("/api/prayer/audio", StaticFiles(directory="Saved_Prayers"), name="audio")
    
    if os.path.exists("Saved_Prayers/Images"):
        app.mount("/api/prayer/image", StaticFiles(directory="Saved_Prayers/Images"), name="images")
    
    print("[INFO] Static file directories mounted successfully")
except Exception as e:
    print(f"[WARNING] Error mounting static directories: {e}")

# Storage for active prayer runs
active_runs = {}

# Pydantic model for prayer generation input
class PrayerInputAPI(BaseModel):
    pray_for: str = Field(..., min_length=1)
    wisdom_to_integrate: str = Field(..., min_length=1)
    selected_faiths: List[str] = Field(default=[])
    generate_images: bool = Field(default=False)
    voice_id: Optional[str] = None

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}

# Import and include routes
from routes.prayer_routes import router as prayer_router
app.include_router(prayer_router, prefix="/api")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
