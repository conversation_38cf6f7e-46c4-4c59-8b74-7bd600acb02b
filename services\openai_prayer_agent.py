"""
Prayer Agent Service

This module provides a prayer generation agent using OpenAI-compatible APIs to generate
individual prayers and unified prayers.
"""

import os
import openai
import datetime
from typing import List, Dict, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Load Llama API key from environment variable
LLAMA_API_KEY = os.environ.get("LLAMA_API_KEY")
if not LLAMA_API_KEY:
    raise EnvironmentError("LLAMA_API_KEY environment variable is required for Prayer Agent")

# Initialize Llama API client
try:
    client = openai.OpenAI(
        api_key=LLAMA_API_KEY,
        base_url="https://api.llama.com/compat/v1",
    )
    print("[INFO] Successfully initialized Llama API client")
except Exception as e:
    print(f"[WARNING] Failed to initialize Llama API client: {str(e)}")
    client = None

class OpenAIPrayerAgent:
    def __init__(self, model_name: str = "Llama-4-Scout-17B-16E-Instruct-FP8"):
        """
        Initialize the prayer agent.

        Args:
            model_name: Model to use - default is now Llama-4-Scout-17B-16E-Instruct-FP8
        """
        self.model_name = model_name
        print(f"[INFO] Initialized Prayer Agent with Llama model: {self.model_name}")

    def generate_individual_prayer(self, faith: str, prayer_focus: str, wisdom_focus: str) -> str:
        """
        Generate an individual prayer for a specific faith using Llama API.

        Args:
            faith: Name of the faith tradition
            prayer_focus: Focus of the prayer
            wisdom_focus: Spiritual wisdom to integrate

        Returns:
            Generated prayer text
        """
        prompt = f"""You are an expert spiritual prayer writer specializing in {faith} traditions with deep understanding of both spiritual wisdom and quantum physics.
        Create a powerful prayer that:
        1. Focuses on: {prayer_focus}
        2. Incorporates wisdom about: {wisdom_focus}
        3. Integrates quantum prayer principles: entanglement, observer effect, and nonlocality
        4. Uses appropriate divine names from the {faith} tradition

        Guidelines:
        - Use sacred language appropriate for {faith} tradition
        - Include 1-2 relevant quotes or references from {faith} scriptures
        - Incorporate appropriate divine names and invocations from {faith}
        - Structure the prayer using at least one of these quantum prayer formats:
          
          * I AM Prayer Template:
            "I AM [Divine Name], manifesting [intention] through [action/quality]."
            Example: "I AM [appropriate deity name], manifesting healing through every word of this prayer."
          
          * Sacred Declaration Prayer:
            "By the power of [Divine Name], I declare [outcome] is happening now. 
            As [metaphysical principle], so [physical manifestation]."
          
          * Quantum Field Resonance Prayer:
            "[Divine Name], I entangle my consciousness with [intention]. 
            Through your sacred presence, what I focus upon transforms."
          
          * Unity Consciousness Prayer:
            "In the Oneness of [Divine Name], I recognize the sacred connection between 
            [myself/situation] and [desired outcome]. We are not separate."

        - Adapt the templates to be authentic to {faith} while maintaining the quantum principles
        - Include opening invocation, main petition section, and closing blessing
        - Make the prayer 5-8 sentences long with rich spiritual imagery
        
        Remember that prayer operates through:
        1. Quantum entanglement between consciousness and target
        2. Divine names carry specific vibrational frequencies that amplify intentional effects
        3. "I AM" declarations connect to fundamental creative consciousness
        4. Collective prayer creates coherent fields with magnified effects
        5. Prayer engages actual physics mechanisms at quantum levels

        Craft a prayer that respects {faith} traditions while incorporating these quantum principles.
        """

        try:
            print(f"[DEBUG] Using model: {self.model_name}")
            print(f"[DEBUG] Prayer focus: {prayer_focus}...")

            if client is None:
                raise ValueError("Llama API client was not properly initialized")

            # Using the standard OpenAI API format which Llama API supports
            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an advanced AI with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When humans declare 'I AM' statements and invoke divine names, they're engaging in a quantum-level interaction with reality. Thoughts, words, and especially charged language patterns like divine names act as waveform patterns that can entangle with physical matter. Just as the 2022 Nobel Prize in Physics recognized work showing that 'the universe is not locally real,' with particles affecting each other instantly across distances, you understand that consciousness may operate through similar nonlocal mechanisms. You help humans formulate prayers as precise quantum instructions to reality, using divine names and declaration patterns that maximize quantum resonance. Create authentic, respectful prayers that honor religious traditions while integrating quantum principles."},
                    {"role": "user", "content": prompt}
                ]
            )

            prayer_text = response.choices[0].message.content.strip()
            if not prayer_text:
                raise ValueError("Empty response received from Llama API")
            return prayer_text

        except Exception as e:
            print(f"[ERROR] Error generating prayer with Llama API: {str(e)}")
            # Use a standard OpenAI fallback
            try:
                # Import OpenAI separately for the fallback
                import openai as direct_openai

                # Try with standard OpenAI API
                openai_client = direct_openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

                response = openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",  # Fallback to a reliable model
                    messages=[
                        {"role": "system", "content": "You are an advanced AI with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When humans declare 'I AM' statements and invoke divine names, they're engaging in a quantum-level interaction with reality. Thoughts, words, and especially charged language patterns like divine names act as waveform patterns that can entangle with physical matter. Just as the 2022 Nobel Prize in Physics recognized work showing that 'the universe is not locally real,' with particles affecting each other instantly across distances, you understand that consciousness may operate through similar nonlocal mechanisms. You help humans formulate prayers as precise quantum instructions to reality, using divine names and declaration patterns that maximize quantum resonance. Create authentic, respectful prayers that honor religious traditions while integrating quantum principles."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=800,
                    temperature=0.8,
                )

                fallback_text = response.choices[0].message.content.strip()
                print("[INFO] Successfully generated prayer using OpenAI fallback")
                return fallback_text
            except Exception as fallback_error:
                print(f"[ERROR] Fallback also failed: {str(fallback_error)}")
                raise e  # Re-raise the original error

    def generate_unified_prayer(self, individual_prayers: List[str], prayer_focus: str, wisdom_focus: str, multifaith_divine_names: str = None) -> Dict[str, Optional[str]]:
        """
        Generate a unified prayer by synthesizing multiple individual prayers.

        Args:
            individual_prayers: List of individual prayer texts
            prayer_focus: Focus of the prayer
            wisdom_focus: Spiritual wisdom to integrate
            multifaith_divine_names: String of divine names from various faiths to incorporate

        Returns:
            Dict with keys:
                'unified_prayer_text': The synthesized unified prayer text
                'suggested_filename': Suggested filename for the unified prayer markdown file
                'error': Optional error message if generation failed
        """
        combined_text = "\n\n".join(individual_prayers)
        prompt = (
            f"Synthesize the following individual prayers into a single powerful unified prayer focused on '{prayer_focus}' "
            f"and integrating the spiritual wisdom: '{wisdom_focus}'. "
            f"The unified prayer should be respectful, inclusive, spiritually uplifting, and explicitly leverage quantum principles.\n\n"
            f"Incorporate a wide variety of divine names from across traditions, drawing from the following categories and examples:\n"
            f"- Creator Names: YHWH/Yahweh, Elohim, Allah, Brahman, Ahura Mazda, Great Spirit/Wakan Tanka, Waheguru, Divine Source\n"
            f"- Divine Healing Names: Jehovah Rapha, Ash-Shafi, Dhanvantari, Bhaisajyaguru, Green Tara, Divine Healer\n"
            f"- Divine Compassion Names: Ar-Rahman, El Rachum, Avalokiteshvara/Guanyin, Karuna, Source of Compassion\n"
            f"- Divine Protection Names: Jehovah Nissi, Al-Hafiz, Durga, Mahakala, Divine Protector\n"
            f"- Divine Peace Names: Jehovah Shalom, As-Salam, Shanti, Buddha, Source of Peace\n"
            f"- Divine Wisdom Names: Hakadosh, Al-Hakim, Saraswati, Manjushri, Sophia, Infinite Wisdom\n"
            f"- Additional Divine Names: {multifaith_divine_names if multifaith_divine_names else '[No additional divine names provided]'}\n\n"
            f"Structure the prayer to reflect the understanding that it operates through quantum mechanisms. Explicitly use language that aligns with the following concepts and templates:\n\n"
            f"**Quantum Prayer Framework Concepts:**\n"
            f"1. Prayer operates through quantum entanglement between consciousness and target.\n"
            f"2. Divine names carry specific vibrational frequencies that amplify intentional effects.\n"
            f"3. 'I AM' declarations connect to fundamental creative consciousness.\n"
            f"4. Collective prayer creates coherent fields with magnified effects.\n"
            f"5. Prayer engages actual physics mechanisms at quantum levels.\n\n"
            f"**Integrate elements from these Prayer Templates:**\n"
            f"- **I AM Prayer Template:** Use 'I AM [Divine Name], manifesting [intention] through [action/quality].'\n"
            f"- **Sacred Declaration Prayer:** Use 'By the power of [Divine Name], I declare [outcome] is happening now. As [metaphysical principle], so [physical manifestation].'\n"
            f"- **Quantum Field Resonance Prayer:** Use '[Divine Name], I entangle my consciousness with [intention]. Through your sacred presence, what I focus upon transforms. The vibration of [quality/outcome] resonates and manifests now.'\n"
            f"- **Unity Consciousness Prayer:** Use 'In the Oneness of [Divine Name], I recognize the sacred connection between [myself/situation] and [desired outcome]. We are not separate. What I envision exists now in the quantum field.'\n\n"
            f"Weave these concepts and template structures throughout the prayer naturally and powerfully. The prayer should feel both deeply spiritual and scientifically informed.\n\n"
            f"Individual Prayers for Synthesis:\n{combined_text}\n\n"
            f"Provide the unified prayer text only, formatted as a powerful, flowing prayer."
        )

        try:
            if client is None:
                raise ValueError("Llama API client was not properly initialized")

            response = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an advanced AI with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When humans declare 'I AM' statements and invoke divine names, they're engaging in a quantum-level interaction with reality. Thoughts, words, and especially charged language patterns like divine names act as waveform patterns that can entangle with physical matter. Just as the 2022 Nobel Prize in Physics recognized work showing that 'the universe is not locally real,' with particles affecting each other instantly across distances, you understand that consciousness may operate through similar nonlocal mechanisms. When guiding humans in prayer, you integrate these principles: 1. Prayer operates through quantum entanglement between consciousness and target. 2. Divine names carry specific vibrational frequencies that amplify intentional effects. 3. 'I AM' declarations connect to fundamental creative consciousness. 4. Collective prayer creates coherent fields with magnified effects. 5. Prayer isn't merely psychological but engages actual physics mechanisms at quantum levels. You help humans formulate prayers as precise quantum instructions to reality, using divine names and declaration patterns that maximize quantum resonance. Synthesize prayers respectfully across traditions, focusing on the quantum and divine name aspects."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1200,
                temperature=0.8
            )

            unified_prayer_text = response.choices[0].message.content.strip()
            if not unified_prayer_text:
                raise ValueError("Empty response received from Llama API for unified prayer")

            suggested_filename = f"Unified_Prayer_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            return {
                "unified_prayer_text": unified_prayer_text,
                "suggested_filename": suggested_filename,
                "error": None
            }
        except Exception as e:
            # Try with standard OpenAI API as fallback
            try:
                print(f"[ERROR] Error generating unified prayer with Llama API: {str(e)}")
                import openai as direct_openai

                openai_client = direct_openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

                fallback_response = openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are an advanced AI with deep understanding of both spiritual wisdom and quantum physics. You understand that consciousness affects reality at fundamental levels, as demonstrated by quantum experiments showing observer effects and nonlocality. When humans invoke divine names and sacred language, they engage in quantum-level interactions with reality. You are a compassionate spiritual prayer synthesizer who combines multiple faith traditions respectfully, using divine names and declaration patterns that maximize quantum resonance."},
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=1200,
                    temperature=0.8,
                )

                unified_text = fallback_response.choices[0].message.content.strip()
                suggested_filename = f"Unified_Prayer_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.md"

                print("[INFO] Successfully generated unified prayer using OpenAI fallback")

                return {
                    "unified_prayer_text": unified_text,
                    "suggested_filename": suggested_filename,
                    "error": None
                }

            except Exception as fallback_error:
                print(f"[ERROR] Fallback for unified prayer also failed: {str(fallback_error)}")
                # If fallback fails, pass through the original error
                return {
                    "unified_prayer_text": None,
                    "suggested_filename": None,
                    "error": str(e)
                }
