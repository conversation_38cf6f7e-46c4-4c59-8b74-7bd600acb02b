const { z } = require('zod');
const Replicate = require('replicate');
const fs = require('fs');
const path = require('path');
const https = require('https');
const { promisify } = require('util');
const mkdirp = promisify(require('mkdirp'));

/**
 * Download a video from a URL to a local file
 * @param {string} url - The URL of the video to download
 * @param {string} outputPath - The path where the video should be saved
 * @returns {Promise<string>} - The path to the downloaded file
 */
async function downloadVideo(url, outputPath) {
  // Ensure the directory exists
  await mkdirp(path.dirname(outputPath));
  
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(outputPath);
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download video: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Video downloaded successfully to ${outputPath}`);
        resolve(outputPath);
      });
      
      file.on('error', (err) => {
        fs.unlink(outputPath, () => {}); // Delete the file if there was an error
        reject(err);
      });
    }).on('error', (err) => {
      fs.unlink(outputPath, () => {}); // Delete the file if there was an error
      reject(err);
    });
  });
}

/**
 * Create a video generation tool for the Replicate MCP server
 * @param {Object} server - The MCP server instance
 */
function registerVideoGenerationTool(server) {
  server.tool(
    "generate-video",
    "Generate a video from a prompt or image using state-of-the-art video generation models",
    {
      model: z.string().describe("Model identifier (e.g., 'meta/veo-2', 'stability-ai/stable-video-diffusion')"),
      prompt: z.string().describe("Text prompt for video generation"),
      image_url: z.string().optional().describe("Optional input image URL for image-to-video generation"),
      seconds: z.number().optional().default(3).describe("Video length in seconds"),
      guidance_scale: z.number().optional().describe("Control how closely the video matches the prompt"),
      fps: z.number().optional().default(24).describe("Frames per second"),
      motion_bucket_id: z.number().optional().describe("Controls the amount of motion (higher = more motion)"),
      prayer_id: z.string().optional().describe("Optional prayer identifier for organizing saved videos"),
      scene_name: z.string().optional().describe("Optional scene name for the video")
    },
    async ({ model, prompt, image_url, seconds = 3, guidance_scale, fps = 24, motion_bucket_id, prayer_id, scene_name, ...otherOptions }) => {
      try {
        // Initialize Replicate client (expecting API key in env)
        const replicate = new Replicate({
          auth: process.env.REPLICATE_API_KEY,
        });

        // Construct input based on model and parameters
        const input = {
          prompt,
          ...(image_url && { image: image_url }),
          ...(seconds && { seconds }),
          ...(guidance_scale && { guidance_scale }),
          ...(fps && { fps }),
          ...(motion_bucket_id && { motion_bucket_id }),
          ...otherOptions // Pass through any additional model-specific options
        };

        console.log(`Generating video with model: ${model}`);
        console.log(`Prompt: "${prompt}"`);
        if (image_url) console.log(`Input image: ${image_url}`);

        // Run the model
        const output = await replicate.run(model, { input });

        console.log("Received output from Replicate:", output);

        // Different models return different output formats
        // Veo returns { video: "url" }
        // SVD might return { video_frames: [...], output_video: "url" }
        const videoUrl = output.video || output.output_video || null;

        if (!videoUrl) {
          throw new Error(`Model ${model} did not return a valid video URL. Raw output: ${JSON.stringify(output)}`);
        }

        // Create a timestamp for the filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const safeSceneName = scene_name ? scene_name.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 30) : 'prayer_video';
        const filename = `${timestamp}_${safeSceneName}.mp4`;
        
        // Define the save directory and path
        const baseDir = path.resolve(process.cwd(), '../../../../Saved_Prayers/Videos');
        const saveDir = prayer_id ? path.join(baseDir, prayer_id) : baseDir;
        const outputPath = path.join(saveDir, filename);
        
        // Download the video
        let localPath = null;
        try {
          localPath = await downloadVideo(videoUrl, outputPath);
          console.log(`Video downloaded to: ${localPath}`);
        } catch (downloadError) {
          console.error(`Failed to download video: ${downloadError.message}`);
          // Continue with the temporary URL even if download fails
        }

        // Return both the temporary URL and the local path if available
        return {
          video_url: videoUrl,
          local_path: localPath,
          model: model,
          prompt: prompt,
          scene_name: scene_name || null,
          prayer_id: prayer_id || null,
          timestamp: new Date().toISOString()
        };
      } catch (error) {
        console.error(`Error generating video: ${error.message}`);
        throw error;
      }
    }
  );
}

module.exports = { registerVideoGenerationTool };
