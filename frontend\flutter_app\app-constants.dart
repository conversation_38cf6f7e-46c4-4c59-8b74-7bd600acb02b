import 'package:flutter/material.dart';

class AppConstants {
  // Faith traditions
  static const List<String> faithTraditions = [
    'Christianity',
    'Islam',
    'Hinduism',
    'Buddhism',
    'Sikhism',
    'Judaism',
    'Bahá\'í Faith',
    'Confucianism',
    'Jainism',
    'Shinto',
    'Taoism',
    'Zoroastrianism',
    'Cherokee Spirituality',
    'Wicca'
  ];
  
  // Faith colors
  static const Map<String, Color> faithColors = {
    'Christianity': Color(0xFF5B92E5),  // Blue
    'Islam': Color(0xFF2E8B57),         // Green
    'Hinduism': Color(0xFFFF7F50),      // Orange
    'Buddhism': Color(0xFFFFD700),      // Gold
    'Sikhism': Color(0xFFE25822),       // Rust
    'Judaism': Color(0xFF4682B4),       // Steel Blue
    'Bahá\'í Faith': Color(0xFF9370DB),  // Medium Purple
    'Confucianism': Color(0xFF708090),   // Slate Gray
    'Jainism': Color(0xFFCD5C5C),        // Indian Red
    'Shinto': Color(0xFFDC143C),         // Crimson
    'Taoism': Color(0xFF008080),         // Teal
    'Zoroastrianism': Color(0xFFB8860B),  // Dark Goldenrod
    'Cherokee Spirituality': Color(0xFF8B4513), // Saddle Brown
    'Wicca': Color(0xFF556B2F),          // Dark Olive Green
  };
  
  // Faith descriptions
  static const Map<String, String> faithDescriptions = {
    'Christianity': 'Based on the life and teachings of Jesus Christ, emphasizing love, forgiveness, and salvation.',
    'Islam': 'Founded on the teachings of Prophet Muhammad, focusing on submission to Allah and following the Quran.',
    'Hinduism': 'Diverse traditions originating in India, centered on concepts of dharma, karma, and spiritual liberation.',
    'Buddhism': 'Founded by Siddhartha Gautama (Buddha), focusing on ending suffering through the Eightfold Path.',
    'Sikhism': 'Founded by Guru Nanak, emphasizing equality, service, and devotion to one God.',
    'Judaism': 'The monotheistic faith of the Jewish people, focused on ethical conduct and covenant with God.',
    'Bahá\'í Faith': 'Founded by Bahá\'u\'lláh, teaching the unity of God, religion, and humanity.',
    'Confucianism': 'Based on teachings of Confucius, focusing on ethical conduct, family, and social harmony.',
    'Jainism': 'Ancient Indian religion emphasizing non-violence, truth, and respect for all living beings.',
    'Shinto': 'Indigenous faith of Japan, focusing on ritual practices and veneration of kami (spirits/gods).',
    'Taoism': 'Chinese philosophy and religion emphasizing living in harmony with the Tao (the way).',
    'Zoroastrianism': 'Ancient Iranian religion founded by prophet Zoroaster, focusing on Good Thoughts, Good Words, Good Deeds.',
    'Cherokee Spirituality': 'Native American spiritual tradition emphasizing harmony with nature and community.',
    'Wicca': 'Modern pagan religion honoring nature, often practicing ritual magic.',
  };
  
  // Prayer generation stages
  static const List<String> prayerStages = [
    'Researching',
    'Generating',
    'Aggregating',
    'Creating Audio',
    'Creating Images',
  ];
  
  // App routes
  static const String loginRoute = '/login';
  static const String dashboardRoute = '/dashboard';
  static const String createPrayerRoute = '/create-prayer';
  static const String prayerConsoleRoute = '/prayer-console';
  static const String prayerResultsRoute = '/prayer-results';
  static const String settingsRoute = '/settings';
  
  // API endpoints
  static const String baseApiUrl = 'https://your-backend-url.com/api';
  static const String prayerGenerateEndpoint = '/prayer/generate';
  static const String prayerStatusEndpoint = '/prayer/status/';
  static const String prayerStreamEndpoint = '/prayer/stream/';
  static const String prayerAudioEndpoint = '/prayer/audio/';
  static const String prayerImageEndpoint = '/prayer/image/';
  
  // Audio speaker voices
  static const List<Map<String, dynamic>> speakerVoices = [
    {'id': 'alloy', 'name': 'Alloy', 'gender': 'neutral'},
    {'id': 'echo', 'name': 'Echo', 'gender': 'male'},
    {'id': 'fable', 'name': 'Fable', 'gender': 'male'},
    {'id': 'onyx', 'name': 'Onyx', 'gender': 'male'},
    {'id': 'nova', 'name': 'Nova', 'gender': 'female'},
    {'id': 'shimmer', 'name': 'Shimmer', 'gender': 'female'},
    {'id': 'sage', 'name': 'Sage', 'gender': 'neutral'},
  ];
  
  // Storage keys
  static const String userInfoKey = 'user_info';
  static const String themeKey = 'app_theme';
  static const String recentPrayersKey = 'recent_prayers';
  static const String defaultVoiceKey = 'default_voice';
}
