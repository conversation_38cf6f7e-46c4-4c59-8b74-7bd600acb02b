#!/usr/bin/env python3
"""
Complete integration test for the enhanced video prayer pipeline.
Tests the full workflow with parallel video generation and beautiful spiritual scenes.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.prayer_state import PrayerState
from nodes.scene_generation_nodes import generate_sacred_scenes_node

def test_complete_enhanced_pipeline():
    """Test the complete enhanced pipeline integration."""
    print("🚀 Testing Complete Enhanced Video Prayer Pipeline")
    print("=" * 70)
    
    # Create a comprehensive test state
    test_state = {
        'prayer_focus_theme': 'Divine Healing Through Sacred Geometry and Angelic Light',
        'unified_prayer_markdown': """
        # Universal Prayer for Healing and Divine Connection
        
        Divine Source of infinite love and light, we come before you with hearts
        open to receive your sacred wisdom. May the Tree of Life flourish within
        our consciousness, its branches reaching toward cosmic understanding while
        its roots ground us in divine truth.
        
        Let sacred geometry guide our perception, revealing the hidden patterns
        that connect all existence. May angelic beings surround us with protection
        and healing energy, their crystalline light transforming all that needs
        renewal.
        
        Through the Flower of Life mandala, we see the interconnectedness of all
        creation. Let Merkaba star tetrahedrons spin in perfect harmony, opening
        portals to higher dimensions of consciousness and healing.
        
        May our chakras align as rainbow mandalas of divine light, each one
        spinning in perfect balance. Let fractal patterns of blessing expand
        infinitely, touching every corner of creation with healing energy.
        
        In the presence of Seraphim wings made of pure crystalline light,
        we surrender all that no longer serves our highest good. May golden
        ratio spirals guide our transformation, and may Platonic solids
        structure our understanding of divine order.
        
        Through the power of divine love, we manifest healing for ourselves,
        our loved ones, and all beings throughout the cosmos. Let this prayer
        resonate across all dimensions, creating ripples of peace and healing
        that extend to the farthest reaches of existence.
        
        So it is, and so it shall be. Amen.
        """,
        'generate_video': True,
        'image_prayer_requested': True,
        'audio_conversion_requested': True,
        'execution_logs': [],
        'run_output_dir': 'test_output'
    }
    
    print("📋 Test Configuration:")
    print(f"   Prayer Theme: {test_state['prayer_focus_theme']}")
    print(f"   Video Generation: {test_state['generate_video']}")
    print(f"   Image Generation: {test_state['image_prayer_requested']}")
    print(f"   Audio Generation: {test_state['audio_conversion_requested']}")
    print(f"   Prayer Length: {len(test_state['unified_prayer_markdown'])} characters")
    
    try:
        # Test enhanced scene generation
        print("\n🎨 Testing Enhanced Scene Generation...")
        enhanced_state = generate_sacred_scenes_node(test_state)
        
        sacred_scenes = enhanced_state.get('sacred_scenes', [])
        print(f"✅ Generated {len(sacred_scenes)} enhanced spiritual scenes")
        
        # Analyze generated scenes
        print("\n📊 Scene Analysis:")
        for i, scene in enumerate(sacred_scenes):
            print(f"\n🎬 Scene {i+1}:")
            print(f"   ID: {scene.get('scene_id', 'N/A')}")
            print(f"   Style: {scene.get('style', 'N/A')}")
            print(f"   Duration: {scene.get('duration', 'N/A')} seconds")
            
            description = scene.get('description', '')
            print(f"   Description: {description[:100]}...")
            
            video_prompt = scene.get('video_prompt', '')
            print(f"   Video Prompt: {video_prompt[:100]}...")
            
            # Check for spiritual elements
            if 'spiritual_elements' in scene:
                elements = scene['spiritual_elements']
                print(f"   🕉️  Sacred Symbol: {elements.get('sacred_symbol', 'N/A')[:40]}...")
                print(f"   👼 Angelic Element: {elements.get('angelic_element', 'N/A')[:40]}...")
                print(f"   🌟 Healing Theme: {elements.get('healing_theme', 'N/A')[:40]}...")
                print(f"   📐 Sacred Geometry: {elements.get('sacred_geometry', 'N/A')[:40]}...")
                print(f"   🌀 Fractal Element: {elements.get('fractal_element', 'N/A')[:40]}...")
        
        # Test workflow integration
        print("\n🔄 Testing Workflow Integration...")
        
        # Test routing logic
        from graph_definition_async import route_to_parallel_media_with_send
        sends = route_to_parallel_media_with_send(enhanced_state)
        print(f"✅ Parallel routing generated {len(sends)} Send objects")
        
        # Test workflow creation
        from graph_definition_async import create_prayer_workflow
        workflow = create_prayer_workflow(enable_async=True)
        print("✅ Enhanced async workflow created successfully")
        
        # Verify scene quality
        print("\n🎯 Scene Quality Verification:")
        
        quality_checks = {
            'has_descriptions': all(scene.get('description') for scene in sacred_scenes),
            'has_video_prompts': all(scene.get('video_prompt') for scene in sacred_scenes),
            'has_spiritual_elements': all(scene.get('spiritual_elements') for scene in sacred_scenes),
            'proper_duration': all(scene.get('duration', 0) > 0 for scene in sacred_scenes),
            'cinematic_style': all('cinematic' in scene.get('style', '').lower() or 'spiritual' in scene.get('style', '').lower() for scene in sacred_scenes)
        }
        
        for check, passed in quality_checks.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check.replace('_', ' ').title()}: {passed}")
        
        all_quality_checks_passed = all(quality_checks.values())
        
        # Test spiritual content verification
        print("\n🕉️  Spiritual Content Verification:")
        
        spiritual_keywords = [
            'Tree of Life', 'Flower of Life', 'Merkaba', 'sacred geometry',
            'Seraphim', 'angelic', 'divine light', 'crystalline',
            'chakra', 'mandala', 'fractal', 'golden ratio',
            'healing', 'cosmic', 'spiritual', 'luminous'
        ]
        
        content_checks = {}
        for keyword in spiritual_keywords:
            found_in_scenes = any(
                keyword.lower() in scene.get('description', '').lower() or
                keyword.lower() in scene.get('video_prompt', '').lower()
                for scene in sacred_scenes
            )
            content_checks[keyword] = found_in_scenes
            status = "✅" if found_in_scenes else "⚠️ "
            print(f"   {status} {keyword}: {'Found' if found_in_scenes else 'Not found'}")
        
        spiritual_content_score = sum(content_checks.values()) / len(content_checks) * 100
        print(f"\n📊 Spiritual Content Score: {spiritual_content_score:.1f}%")
        
        # Final assessment
        print("\n" + "=" * 70)
        print("🎉 ENHANCED VIDEO PRAYER PIPELINE TEST RESULTS")
        print("=" * 70)
        
        if all_quality_checks_passed and spiritual_content_score >= 50:
            print("✅ ALL TESTS PASSED! Enhanced pipeline is working perfectly!")
            print("\n🌟 Key Achievements:")
            print(f"   • Generated {len(sacred_scenes)} beautiful spiritual scenes")
            print(f"   • {spiritual_content_score:.1f}% spiritual content coverage")
            print("   • Advanced LLM prompting working")
            print("   • Parallel video generation ready")
            print("   • Sacred symbols and geometry integrated")
            print("   • Angelic elements and healing themes included")
            print("   • Fractal patterns and divine imagery present")
            
            print("\n🎬 Ready for Video Generation!")
            print("   The enhanced scenes will create breathtaking spiritual videos")
            print("   featuring mandalas, sacred geometry, angelic light, and healing energy.")
            
            return True
        else:
            print("⚠️  Some quality checks failed, but basic functionality works")
            return False
            
    except Exception as e:
        print(f"❌ Pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the complete enhanced pipeline test."""
    success = test_complete_enhanced_pipeline()
    
    if success:
        print("\n🚀 The enhanced video prayer pipeline is ready!")
        print("Users will now experience beautiful spiritual videos with:")
        print("• Sacred symbols and mandalas")
        print("• Angelic light beings and divine energy")
        print("• Healing chakras and energy fields")
        print("• Sacred geometry and fractal patterns")
        print("• Cinematic spiritual visionary art")
    else:
        print("\n⚠️  Please review the test results above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
