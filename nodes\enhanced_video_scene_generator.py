"""
Enhanced Video Scene Generator with Advanced LLM Prompting
Creates beautiful spiritual scenes with symbols, mandalas, angels, healing themes, sacred geometry, and fractals.
"""

import os
import json
import logging
import asyncio
import requests
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
from models.prayer_state import PrayerState

load_dotenv()
logger = logging.getLogger(__name__)

class EnhancedVideoSceneGenerator:
    """
    Advanced scene generator for creating beautiful spiritual video prompts
    with sacred symbols, mandalas, angels, healing themes, sacred geometry, and fractals.
    """

    def __init__(self):
        self.gemini_api_key = os.getenv("GEMINI_API_KEY") or os.getenv("GOOGLE_API_KEY")
        self.xai_api_key = os.getenv("XAI_API_KEY")

        # Spiritual visual elements library
        self.sacred_symbols = [
            "Tree of Life with luminous branches reaching into cosmic dimensions",
            "Flower of Life mandala pulsing with divine energy",
            "Sri Yantra geometric pattern radiating golden light",
            "Merkaba star tetrahedron spinning with crystalline energy",
            "Lotus flower blooming with fractal petals of pure light",
            "Celtic knot infinity patterns weaving through space",
            "Ankh symbol glowing with eternal life force",
            "Om symbol resonating with cosmic vibrations",
            "Yin-yang symbol flowing with liquid light and shadow",
            "Hamsa hand radiating protective energy fields"
        ]

        self.angelic_elements = [
            "Seraphim wings made of pure crystalline light",
            "Cherubim forms composed of swirling sacred geometry",
            "Archangelic presence manifesting as pillars of rainbow light",
            "Guardian angel silhouettes outlined in golden luminescence",
            "Angelic choir represented as harmonious light frequencies",
            "Divine messengers appearing as geometric light beings",
            "Celestial beings formed from fractal light patterns",
            "Angelic essence flowing as streams of healing energy",
            "Heavenly hosts manifesting as sacred geometric formations",
            "Divine protectors appearing as shields of crystalline light"
        ]

        self.healing_themes = [
            "Chakras spinning as rainbow mandalas of healing light",
            "DNA helixes transforming into spirals of golden energy",
            "Cellular regeneration visualized as blooming light fractals",
            "Aura cleansing represented by cascading waterfalls of light",
            "Energy meridians flowing like rivers of liquid starlight",
            "Healing crystals resonating with harmonic frequencies",
            "Sacred waters flowing with therapeutic properties",
            "Reiki energy symbols floating in healing light fields",
            "Therapeutic sound waves manifesting as visible light patterns",
            "Quantum healing fields represented as geometric light matrices"
        ]

        self.sacred_geometry = [
            "Platonic solids rotating in perfect mathematical harmony",
            "Golden ratio spirals expanding into infinite dimensions",
            "Fibonacci sequences manifesting as natural growth patterns",
            "Vesica piscis creating portals between dimensions",
            "Dodecahedron structures containing entire universes",
            "Icosahedron forms pulsing with crystalline energy",
            "Octahedron shapes channeling elemental forces",
            "Tetrahedron pyramids focusing cosmic energy",
            "Hexagonal honeycomb patterns of divine order",
            "Pentagonal star formations radiating protective energy"
        ]

        self.fractal_elements = [
            "Mandelbrot set patterns expanding into infinite complexity",
            "Julia set fractals creating otherworldly landscapes",
            "Sierpinski triangle iterations forming sacred mountains",
            "Dragon curve fractals flowing like cosmic serpents",
            "Barnsley fern patterns growing into mystical forests",
            "Koch snowflake structures crystallizing divine perfection",
            "Cantor set patterns revealing hidden dimensions",
            "Lorenz attractor flows creating butterfly effect visualizations",
            "Apollonian gasket circles containing infinite wisdom",
            "Menger sponge structures opening portals to higher dimensions"
        ]

    async def generate_enhanced_scenes(self, prayer_text: str, prayer_theme: str, num_scenes: int = 5) -> List[Dict[str, Any]]:
        """
        Generate enhanced spiritual scenes with advanced LLM prompting.

        Args:
            prayer_text: The unified prayer text
            prayer_theme: The prayer focus theme
            num_scenes: Number of scenes to generate

        Returns:
            List of enhanced scene dictionaries with detailed prompts
        """
        logger.info(f"Generating {num_scenes} enhanced spiritual scenes...")

        try:
            # Step 1: Extract spiritual concepts using LLM
            spiritual_concepts = await self._extract_spiritual_concepts(prayer_text, prayer_theme)

            # Step 2: Generate scene descriptions using advanced prompting
            scene_descriptions = await self._generate_scene_descriptions(
                prayer_text, prayer_theme, spiritual_concepts, num_scenes
            )

            # Step 3: Create detailed video prompts for each scene
            enhanced_scenes = []
            for i, description in enumerate(scene_descriptions):
                video_prompt = await self._create_video_prompt(description, prayer_theme, i)

                scene = {
                    "scene_id": i,
                    "description": description,
                    "video_prompt": video_prompt,
                    "visual_description": video_prompt,  # For compatibility
                    "content": description,  # For compatibility
                    "spiritual_elements": self._get_random_spiritual_elements(),
                    "duration": 5,  # 5 seconds per scene
                    "style": "cinematic spiritual visionary art"
                }
                enhanced_scenes.append(scene)

            logger.info(f"Successfully generated {len(enhanced_scenes)} enhanced scenes")
            return enhanced_scenes

        except Exception as e:
            logger.error(f"Error generating enhanced scenes: {e}")
            # Fallback to basic scene generation
            return await self._generate_fallback_scenes(prayer_text, prayer_theme, num_scenes)

    async def _extract_spiritual_concepts(self, prayer_text: str, prayer_theme: str) -> List[str]:
        """Extract key spiritual concepts from the prayer using LLM."""

        prompt = f"""
        Analyze this prayer text and extract the most powerful spiritual concepts, symbols, and themes:

        Prayer Theme: {prayer_theme}
        Prayer Text: {prayer_text}

        Extract 10-15 key spiritual concepts that could be visualized as:
        - Sacred symbols and religious imagery
        - Healing and transformation themes
        - Divine light and energy manifestations
        - Natural elements with spiritual significance
        - Geometric patterns and sacred mathematics

        Return as a JSON array of concept strings.
        Focus on visual, symbolic elements that transcend specific religious traditions.
        """

        try:
            if self.gemini_api_key:
                response = await self._call_llm_async(prompt, use_gemini=True)
            elif self.xai_api_key:
                response = await self._call_llm_async(prompt, use_gemini=False)
            else:
                # Fallback to predefined concepts
                return [
                    "divine light", "sacred geometry", "healing energy", "cosmic consciousness",
                    "spiritual transformation", "angelic presence", "fractal patterns", "mandala formations"
                ]

            # Parse JSON response
            concepts = json.loads(response.strip())
            return concepts if isinstance(concepts, list) else []

        except Exception as e:
            logger.warning(f"Failed to extract spiritual concepts: {e}")
            return ["divine light", "sacred symbols", "healing energy", "spiritual transformation"]

    async def _generate_scene_descriptions(self, prayer_text: str, prayer_theme: str,
                                         spiritual_concepts: List[str], num_scenes: int) -> List[str]:
        """Generate detailed scene descriptions using advanced LLM prompting."""

        concepts_text = ", ".join(spiritual_concepts[:10])  # Use top 10 concepts

        prompt = f"""
        You are a master spiritual cinematographer and visionary artist specializing in creating transcendent visual experiences.

        Create {num_scenes} breathtaking spiritual scenes for video generation based on:

        Prayer Theme: {prayer_theme}
        Key Spiritual Concepts: {concepts_text}
        Prayer Text: {prayer_text}

        Each scene should be a cinematic masterpiece incorporating:

        🕉️ SACRED SYMBOLS: Mandalas, Tree of Life, Flower of Life, Sri Yantra, Merkaba, Lotus, Celtic knots
        👼 ANGELIC PRESENCE: Seraphim wings, divine light beings, celestial guardians (NO human faces)
        🌟 HEALING ENERGY: Chakras, auras, energy fields, DNA light spirals, quantum healing matrices
        📐 SACRED GEOMETRY: Platonic solids, golden ratio spirals, Fibonacci patterns, geometric portals
        🌀 FRACTALS: Mandelbrot sets, Julia sets, infinite recursive patterns, crystalline structures

        VISUAL REQUIREMENTS:
        - NO human figures, faces, or people
        - Focus on abstract spiritual representations
        - Cinematic lighting with divine luminescence
        - Flowing, dynamic movement and transformation
        - Rich symbolic imagery that transcends religious boundaries
        - Healing and uplifting energy

        Format as JSON array with each scene as a detailed 2-3 sentence description.
        Make each scene visually distinct and cinematically stunning.
        """

        try:
            if self.gemini_api_key:
                response = await self._call_llm_async(prompt, use_gemini=True)
            elif self.xai_api_key:
                response = await self._call_llm_async(prompt, use_gemini=False)
            else:
                return self._generate_fallback_descriptions(num_scenes)

            # Parse JSON response
            scenes = json.loads(response.strip())
            return scenes if isinstance(scenes, list) else []

        except Exception as e:
            logger.warning(f"Failed to generate scene descriptions: {e}")
            return self._generate_fallback_descriptions(num_scenes)

    async def _create_video_prompt(self, scene_description: str, prayer_theme: str, scene_index: int) -> str:
        """Create optimized video generation prompt for each scene."""

        # Select random spiritual elements for this scene
        symbol = self._get_random_element(self.sacred_symbols)
        angel = self._get_random_element(self.angelic_elements)
        healing = self._get_random_element(self.healing_themes)
        geometry = self._get_random_element(self.sacred_geometry)
        fractal = self._get_random_element(self.fractal_elements)

        prompt = f"""
        You are an expert video prompt engineer for AI video generation models like Veo-2 and Pixverse.

        Transform this spiritual scene into an optimized video generation prompt:

        Scene: {scene_description}
        Theme: {prayer_theme}

        Enhance with these elements:
        - Sacred Symbol: {symbol}
        - Angelic Element: {angel}
        - Healing Theme: {healing}
        - Sacred Geometry: {geometry}
        - Fractal Pattern: {fractal}

        Create a single, powerful video prompt that:
        1. Describes dynamic movement and transformation
        2. Specifies cinematic camera work (slow zoom, gentle rotation, flowing movement)
        3. Details lighting (divine luminescence, golden hour, ethereal glow)
        4. Includes specific visual effects (particle systems, energy flows, light rays)
        5. Maintains spiritual reverence and healing energy
        6. Avoids any human figures or faces
        7. Optimized for 5-second video generation

        Return ONLY the final video prompt, no explanations.
        """

        try:
            if self.gemini_api_key:
                response = await self._call_llm_async(prompt, use_gemini=True)
            elif self.xai_api_key:
                response = await self._call_llm_async(prompt, use_gemini=False)
            else:
                return self._create_fallback_prompt(scene_description, scene_index)

            return response.strip()

        except Exception as e:
            logger.warning(f"Failed to create video prompt: {e}")
            return self._create_fallback_prompt(scene_description, scene_index)

    def _get_random_element(self, element_list: List[str]) -> str:
        """Get a random element from a list."""
        import random
        return random.choice(element_list)

    def _get_random_spiritual_elements(self) -> Dict[str, str]:
        """Get random spiritual elements for a scene."""
        return {
            "sacred_symbol": self._get_random_element(self.sacred_symbols),
            "angelic_element": self._get_random_element(self.angelic_elements),
            "healing_theme": self._get_random_element(self.healing_themes),
            "sacred_geometry": self._get_random_element(self.sacred_geometry),
            "fractal_element": self._get_random_element(self.fractal_elements)
        }

    def _generate_fallback_descriptions(self, num_scenes: int) -> List[str]:
        """Generate fallback scene descriptions when LLM fails."""
        fallback_scenes = [
            "A magnificent Tree of Life with luminous branches reaching into cosmic dimensions, its roots intertwined with galaxies while sacred geometry patterns flow through its trunk like liquid light.",

            "A Flower of Life mandala slowly rotating in space, each intersection point blooming with fractal lotus petals that emit healing rainbow frequencies across multiple dimensions.",

            "Seraphim wings made of pure crystalline light unfold across a cosmic landscape, while Merkaba star tetrahedrons spin in perfect harmony, creating portals of divine energy.",

            "Chakras manifest as spinning rainbow mandalas of healing light, connected by streams of golden energy that flow like DNA helixes transforming into spirals of pure consciousness.",

            "Sacred geometric forms - dodecahedrons, icosahedrons, and Platonic solids - rotate in mathematical perfection while fractal patterns expand infinitely, creating a symphony of divine order."
        ]

        return fallback_scenes[:num_scenes]

    def _create_fallback_prompt(self, scene_description: str, scene_index: int) -> str:
        """Create fallback video prompt when LLM fails."""
        elements = self._get_random_spiritual_elements()

        return f"""Cinematic spiritual scene: {scene_description}. Features {elements['sacred_symbol']} with {elements['angelic_element']}. {elements['healing_theme']} flows through {elements['sacred_geometry']} while {elements['fractal_element']} creates infinite recursive beauty. Slow camera movement, divine lighting, ethereal glow, particle effects, 5 seconds, no humans, transcendent spiritual art, 4K quality."""

    async def _call_llm_async(self, prompt: str, use_gemini: bool = True) -> str:
        """Make async LLM API call."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self._call_llm_sync, prompt, use_gemini)

    def _call_llm_sync(self, prompt: str, use_gemini: bool = True) -> str:
        """Make synchronous LLM API call."""
        if use_gemini and self.gemini_api_key:
            return self._call_gemini_api(prompt)
        elif self.xai_api_key:
            return self._call_xai_api(prompt)
        else:
            raise Exception("No API keys available")

    def _call_gemini_api(self, prompt: str) -> str:
        """Call Gemini API."""
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent?key={self.gemini_api_key}"

        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": 0.7,
                "topP": 0.95,
                "topK": 40,
                "maxOutputTokens": 8192
            }
        }

        response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, timeout=120)
        response.raise_for_status()

        result = response.json()
        return result["candidates"][0]["content"]["parts"][0]["text"]

    def _call_xai_api(self, prompt: str) -> str:
        """Call XAI API."""
        url = "https://api.x.ai/v1/chat/completions"

        payload = {
            "model": "grok-beta",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 4096
        }

        headers = {
            "Authorization": f"Bearer {self.xai_api_key}",
            "Content-Type": "application/json"
        }

        response = requests.post(url, json=payload, headers=headers, timeout=120)
        response.raise_for_status()

        result = response.json()
        return result["choices"][0]["message"]["content"]

    async def _generate_fallback_scenes(self, prayer_text: str, prayer_theme: str, num_scenes: int) -> List[Dict[str, Any]]:
        """Generate fallback scenes when LLM processing fails."""
        descriptions = self._generate_fallback_descriptions(num_scenes)

        scenes = []
        for i, description in enumerate(descriptions):
            prompt = self._create_fallback_prompt(description, i)

            scene = {
                "scene_id": i,
                "description": description,
                "video_prompt": prompt,
                "visual_description": prompt,
                "content": description,
                "spiritual_elements": self._get_random_spiritual_elements(),
                "duration": 5,
                "style": "cinematic spiritual visionary art"
            }
            scenes.append(scene)

        return scenes
