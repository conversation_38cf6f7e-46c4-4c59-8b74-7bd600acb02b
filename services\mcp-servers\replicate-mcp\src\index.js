const path = require('path');
const dotenv = require('dotenv');

// Load environment variables from the project root .env file
const projectRoot = path.resolve(__dirname, '../../../../');
dotenv.config({ path: path.join(projectRoot, '.env') });

const { McpServer } = require('@modelcontextprotocol/sdk/server/mcp');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio');
const { registerVideoGenerationTool } = require('./tools/video_generation');

// Initialize the MCP server
const server = new McpServer({
  name: 'replicate-mcp',
  description: 'Interface for Replicate.com services with enhanced video generation capabilities',
  version: '1.1.0', // Updated version to reflect video capabilities
});

// Register the video generation tool
registerVideoGenerationTool(server);

// Register any other existing tools here
// e.g., server.tool("other-tool", ...)

// Start the server
const transport = new StdioServerTransport(server);
transport.run();

console.log('Replicate MCP server started with video generation capabilities');
