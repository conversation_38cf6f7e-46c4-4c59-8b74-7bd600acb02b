import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_provider.dart';
import '../theme/app_theme.dart';
import '../constants/app_constants.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final themeManager = ref.watch(themeProvider);
    final authService = ref.watch(authServiceProvider);
    
    // Get user info
    final userDisplayName = authService.userDisplayName;
    final userPhotoUrl = authService.userPhotoURL;
    final userEmail = authService.userEmail;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // User profile section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                // User avatar
                CircleAvatar(
                  radius: 32,
                  backgroundImage: userPhotoUrl != null ? NetworkImage(userPhotoUrl) : null,
                  child: userPhotoUrl == null
                      ? Text(
                          userDisplayName.isNotEmpty ? userDisplayName[0] : '?',
                          style: theme.textTheme.headlineMedium,
                        )
                      : null,
                ),
                const SizedBox(width: 16),
                
                // User info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        userDisplayName,
                        style: theme.textTheme.titleLarge,
                      ),
                      if (userEmail != null)
                        Text(
                          userEmail,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          const Divider(),
          
          // Appearance settings
          ListTile(
            leading: const Icon(Icons.palette),
            title: const Text('Appearance'),
            subtitle: Text(
              themeManager.themeMode == ThemeMode.light
                  ? 'Light'
                  : themeManager.themeMode == ThemeMode.dark
                      ? 'Dark'
                      : 'System',
            ),
            onTap: () {
              _showThemeDialog(context, themeManager);
            },
          ),
          
          // Default preferences
          ListTile(
            leading: const Icon(Icons.settings_suggest),
            title: const Text('Default Preferences'),
            subtitle: const Text('Voice, image generation, etc.'),
            onTap: () {
              _showDefaultPreferencesDialog(context);
            },
          ),
          
          // Notification settings
          ListTile(
            leading: const Icon(Icons.notifications),
            title: const Text('Notifications'),
            subtitle: const Text('Off'),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Notification settings not implemented yet'),
                ),
              );
            },
          ),
          
          const Divider(),
          
          // Privacy & data
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('Privacy & Data'),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Privacy settings not implemented yet'),
                ),
              );
            },
          ),
          
          // Help & support
          ListTile(
            leading: const Icon(Icons.help),
            title: const Text('Help & Support'),
            onTap: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Help & support not implemented yet'),
                ),
              );
            },
          ),
          
          // About
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('About'),
            onTap: () {
              _showAboutDialog(context);
            },
          ),
          
          const Divider(),
          
          // Sign out
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('Sign Out'),
            onTap: () async {
              final confirmed = await _showSignOutConfirmationDialog(context);
              
              if (confirmed == true && context.mounted) {
                try {
                  await ref.read(authNotifierProvider.notifier).signOut();
                  if (context.mounted) {
                    context.go(AppConstants.loginRoute);
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Error signing out: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
          ),
          
          // App version info
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Center(
              child: Text(
                'SpiritSync v1.0.0',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.5),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Theme selection dialog
  void _showThemeDialog(BuildContext context, ThemeManager themeManager) {
    showDialog(
      context: context,
      builder: (context) => SimpleDialog(
        title: const Text('Select Theme'),
        children: [
          // Light theme
          SimpleDialogOption(
            onPressed: () {
              themeManager.setThemeMode(ThemeMode.light);
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.wb_sunny,
                    color: themeManager.themeMode == ThemeMode.light
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  const SizedBox(width: 16),
                  const Text('Light'),
                  const Spacer(),
                  if (themeManager.themeMode == ThemeMode.light)
                    Icon(
                      Icons.check,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                ],
              ),
            ),
          ),
          
          // Dark theme
          SimpleDialogOption(
            onPressed: () {
              themeManager.setThemeMode(ThemeMode.dark);
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.nightlight_round,
                    color: themeManager.themeMode == ThemeMode.dark
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  const SizedBox(width: 16),
                  const Text('Dark'),
                  const Spacer(),
                  if (themeManager.themeMode == ThemeMode.dark)
                    Icon(
                      Icons.check,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                ],
              ),
            ),
          ),
          
          // System theme
          SimpleDialogOption(
            onPressed: () {
              themeManager.setThemeMode(ThemeMode.system);
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  Icon(
                    Icons.brightness_auto,
                    color: themeManager.themeMode == ThemeMode.system
                        ? Theme.of(context).colorScheme.primary
                        : null,
                  ),
                  const SizedBox(width: 16),
                  const Text('System'),
                  const Spacer(),
                  if (themeManager.themeMode == ThemeMode.system)
                    Icon(
                      Icons.check,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  // Default preferences dialog
  void _showDefaultPreferencesDialog(BuildContext context) {
    final theme = Theme.of(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Default Preferences'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Default voice
            Text(
              'Default Voice',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            DropdownButtonFormField<String>(
              value: AppConstants.speakerVoices.first['id'] as String,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
              items: AppConstants.speakerVoices.map((voice) {
                return DropdownMenuItem<String>(
                  value: voice['id'] as String,
                  child: Text('${voice['name']} (${voice['gender']})'),
                );
              }).toList(),
              onChanged: (value) {
                // Save default voice preference
              },
            ),
            const SizedBox(height: 16),
            
            // Image generation toggle
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Generate Images by Default',
                  style: theme.textTheme.titleMedium,
                ),
                Switch(
                  value: true,
                  onChanged: (value) {
                    // Save image generation preference
                  },
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Close'),
          ),
          FilledButton(
            onPressed: () {
              // Save preferences
              Navigator.pop(context);
              
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Preferences saved'),
                ),
              );
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
  
  // About dialog
  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'SpiritSync',
      applicationVersion: 'v1.0.0',
      applicationIcon: const Icon(
        Icons.self_improvement,
        size: 48,
      ),
      applicationLegalese: '© 2025 SpiritSync Team',
      children: [
        const SizedBox(height: 16),
        Text(
          'SpiritSync is a multi-faith prayer app that connects to a LangGraph backend to generate beautiful prayers from multiple spiritual traditions.',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 16),
        Text(
          'Made with Flutter and a touch of spiritual wisdom.',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ],
    );
  }
  
  // Sign out confirmation dialog
  Future<bool?> _showSignOutConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context, false);
            },
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context, true);
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
