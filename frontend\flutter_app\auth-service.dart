import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/material.dart';
import 'package:github_sign_in/github_sign_in.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Get current authenticated user
  User? get currentUser => _auth.currentUser;
  
  // Stream of authentication state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  // Check if user is authenticated
  bool get isAuthenticated => _auth.currentUser != null;
  
  // Get user display name or email
  String get userDisplayName {
    final user = _auth.currentUser;
    if (user == null) return '';
    return user.displayName ?? user.email ?? 'User';
  }
  
  // Get user profile image URL
  String? get userPhotoURL => _auth.currentUser?.photoURL;
  
  // Get user email
  String? get userEmail => _auth.currentUser?.email;
  
  // Get user ID
  String? get userId => _auth.currentUser?.uid;
  
  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Web platform uses different sign-in method
      if (kIsWeb) {
        // Create a Google auth provider
        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        
        // Sign in with popup
        return await _auth.signInWithPopup(googleProvider);
      } else {
        // Trigger authentication flow on mobile
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
        
        if (googleUser == null) return null;
        
        // Obtain auth details
        final GoogleSignInAuthentication googleAuth = 
            await googleUser.authentication;
        
        // Create credential
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        
        // Sign in with credential
        return await _auth.signInWithCredential(credential);
      }
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      rethrow;
    }
  }
  
  // Sign in with Apple
  Future<UserCredential?> signInWithApple() async {
    try {
      // Web platform uses different sign-in method
      if (kIsWeb) {
        // Create an Apple auth provider
        AppleAuthProvider appleProvider = AppleAuthProvider();
        
        // Sign in with popup
        return await _auth.signInWithPopup(appleProvider);
      } else {
        // Request credential from Apple
        final appleCredential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );
        
        // Create OAuthCredential
        final oauthCredential = OAuthProvider('apple.com').credential(
          idToken: appleCredential.identityToken,
          accessToken: appleCredential.authorizationCode,
        );
        
        // Sign in with credential
        return await _auth.signInWithCredential(oauthCredential);
      }
    } catch (e) {
      debugPrint('Error signing in with Apple: $e');
      rethrow;
    }
  }
  
  // Sign in with GitHub
  Future<UserCredential?> signInWithGitHub(BuildContext context) async {
    try {
      if (kIsWeb) {
        // Create a GitHub auth provider for web
        GithubAuthProvider githubProvider = GithubAuthProvider();
        
        // Sign in with popup
        return await _auth.signInWithPopup(githubProvider);
      } else {
        // Native GitHub sign-in
        // Replace with your GitHub OAuth credentials
        final GitHubSignIn gitHubSignIn = GitHubSignIn(
          clientId: 'YOUR_GITHUB_CLIENT_ID',
          clientSecret: 'YOUR_GITHUB_CLIENT_SECRET',
          redirectUrl: 'YOUR_GITHUB_REDIRECT_URL',
        );
        
        // Initiate the sign-in flow
        final result = await gitHubSignIn.signIn(context);
        
        if (result.token == null) return null;
        
        // Create credential
        final githubAuthCredential = GithubAuthProvider.credential(result.token!);
        
        // Sign in with credential
        return await _auth.signInWithCredential(githubAuthCredential);
      }
    } catch (e) {
      debugPrint('Error signing in with GitHub: $e');
      rethrow;
    }
  }
  
  // Sign in anonymously (as guest)
  Future<UserCredential> signInAnonymously() async {
    try {
      return await _auth.signInAnonymously();
    } catch (e) {
      debugPrint('Error signing in anonymously: $e');
      rethrow;
    }
  }
  
  // Sign out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }
  
  // Store user data in secure storage
  Future<void> storeUserData(Map<String, dynamic> userData) async {
    try {
      await _secureStorage.write(
        key: 'user_data',
        value: userData.toString(),
      );
    } catch (e) {
      debugPrint('Error storing user data: $e');
    }
  }
  
  // Get user data from secure storage
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final data = await _secureStorage.read(key: 'user_data');
      if (data == null) return null;
      
      // Convert string to map (simple implementation)
      // In a real app, you would use json.decode
      final map = <String, dynamic>{};
      data.split(',').forEach((item) {
        final parts = item.split(':');
        if (parts.length == 2) {
          map[parts[0].trim()] = parts[1].trim();
        }
      });
      
      return map;
    } catch (e) {
      debugPrint('Error getting user data: $e');
      return null;
    }
  }
  
  // Delete user data from secure storage
  Future<void> deleteUserData() async {
    try {
      await _secureStorage.delete(key: 'user_data');
    } catch (e) {
      debugPrint('Error deleting user data: $e');
    }
  }
}
