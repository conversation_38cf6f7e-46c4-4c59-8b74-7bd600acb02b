import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../screens/login_screen.dart';
import '../screens/dashboard_screen.dart';
import '../screens/create_prayer_screen.dart';
import '../screens/prayer_console_screen.dart';
import '../screens/prayer_result_screen.dart';
import '../screens/settings_screen.dart';

// Router provider
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);
  
  // Redirect based on authentication state
  final redirect = (context, state) {
    // Check if user is authenticated
    final isAuthenticated = authState.when(
      data: (user) => user != null,
      loading: () => false,
      error: (_, __) => false,
    );
    
    // Check if current route is login
    final isLoggingIn = state.location == AppConstants.loginRoute;
    
    // If not authenticated and not on login page, redirect to login
    if (!isAuthenticated && !isLoggingIn) {
      return AppConstants.loginRoute;
    }
    
    // If authenticated and on login page, redirect to dashboard
    if (isAuthenticated && isLoggingIn) {
      return AppConstants.dashboardRoute;
    }
    
    // Otherwise, no redirect
    return null;
  };
  
  return GoRouter(
    routes: [
      // Login route
      GoRoute(
        path: AppConstants.loginRoute,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      // Dashboard route
      GoRoute(
        path: AppConstants.dashboardRoute,
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      
      // Create prayer route
      GoRoute(
        path: AppConstants.createPrayerRoute,
        name: 'create-prayer',
        builder: (context, state) => const CreatePrayerScreen(),
      ),
      
      // Prayer console route
      GoRoute(
        path: '${AppConstants.prayerConsoleRoute}/:runId',
        name: 'prayer-console',
        builder: (context, state) {
          final runId = state.pathParameters['runId'] ?? '';
          return PrayerConsoleScreen(runId: runId);
        },
      ),
      
      // Prayer results route
      GoRoute(
        path: '${AppConstants.prayerResultsRoute}/:runId',
        name: 'prayer-results',
        builder: (context, state) {
          final runId = state.pathParameters['runId'] ?? '';
          return PrayerResultScreen(runId: runId);
        },
      ),
      
      // Settings route
      GoRoute(
        path: AppConstants.settingsRoute,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    
    // Initial location
    initialLocation: AppConstants.loginRoute,
    
    // Redirect logic
    redirect: redirect,
    
    // Debug mode
    debugLogDiagnostics: true,
    
    // Error page
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 60,
            ),
            const SizedBox(height: 16),
            Text(
              'Route "${state.uri.path}" not found',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(AppConstants.dashboardRoute),
              child: const Text('Go to Dashboard'),
            ),
          ],
        ),
      ),
    ),
    
    // Refresh authentication listener
    refreshListenable: GoRouterRefreshStream(authState.when(
      data: (_) => null,
      loading: () => null,
      error: (_, __) => null,
    )),
  );
});

// Helper class for authentication state changes
class GoRouterRefreshStream extends ChangeNotifier {
  GoRouterRefreshStream(Stream<dynamic>? stream) {
    notifyListeners();
    
    _subscription = stream?.listen(
      (dynamic _) => notifyListeners(),
    );
  }
  
  late final StreamSubscription<dynamic>? _subscription;
  
  @override
  void dispose() {
    _subscription?.cancel();
    super.dispose();
  }
}
