import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../providers/prayer_provider.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final authService = ref.watch(authServiceProvider);
    final recentPrayers = ref.watch(recentPrayersProvider);
    
    // Get user info
    final userDisplayName = authService.userDisplayName;
    final userPhotoUrl = authService.userPhotoURL;
    
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App Bar
          SliverAppBar(
            expandedHeight: 180.0,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: const Text('SpiritSync'),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.tertiary,
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    // Background patterns (subtle prayer symbols)
                    Positioned.fill(
                      child: Opacity(
                        opacity: 0.1,
                        child: Image.asset(
                          'assets/images/pattern_light.png',
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              // Settings button
              IconButton(
                icon: const Icon(Icons.settings),
                onPressed: () => context.push(AppConstants.settingsRoute),
              ),
            ],
          ),
          
          // Main content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // User profile section
                  Card(
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        children: [
                          // User avatar
                          CircleAvatar(
                            radius: 32,
                            backgroundImage: userPhotoUrl != null 
                                ? NetworkImage(userPhotoUrl) 
                                : null,
                            child: userPhotoUrl == null 
                                ? Text(
                                    userDisplayName.isNotEmpty 
                                        ? userDisplayName[0]
                                        : '?',
                                    style: theme.textTheme.headlineMedium,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 16),
                          
                          // User info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Welcome,',
                                  style: theme.textTheme.titleMedium,
                                ),
                                Text(
                                  userDisplayName,
                                  style: theme.textTheme.headlineSmall,
                                ),
                              ],
                            ),
                          ),
                          
                          // Sign out button
                          IconButton(
                            icon: const Icon(Icons.logout),
                            onPressed: () async {
                              await ref.read(authNotifierProvider.notifier).signOut();
                              if (context.mounted) {
                                context.go(AppConstants.loginRoute);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  
                  // Stats section
                  _buildStatsSection(context, theme),
                  const SizedBox(height: 24),
                  
                  // Create prayer card
                  _buildCreatePrayerCard(context, theme),
                  const SizedBox(height: 24),
                  
                  // Recent prayers section
                  Text(
                    'Recent Prayers',
                    style: theme.textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  
                  // Recent prayers grid
                  recentPrayers.isEmpty
                      ? _buildEmptyRecentPrayers(context, theme)
                      : _buildRecentPrayersGrid(context, theme, recentPrayers),
                  const SizedBox(height: 24),
                  
                  // Explore faith traditions section
                  Text(
                    'Explore Faith Traditions',
                    style: theme.textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  
                  // Faith traditions grid
                  _buildFaithTraditionsGrid(context, theme),
                  const SizedBox(height(50), // Extra space at bottom
                ],
              ),
            ),
          ),
        ],
      ),
      
      // Floating action button to create prayer
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.push(AppConstants.createPrayerRoute),
        icon: const Icon(Icons.add),
        label: const Text('Create Prayer'),
      ),
    );
  }
  
  // Stats section with prayer activity
  Widget _buildStatsSection(BuildContext context, ThemeData theme) {
    return Row(
      children: [
        _buildStatCard(
          context,
          theme,
          icon: Icons.history,
          value: '12',
          label: 'Prayers',
        ),
        const SizedBox(width: 12),
        _buildStatCard(
          context,
          theme,
          icon: Icons.public,
          value: '6',
          label: 'Traditions',
        ),
        const SizedBox(width: 12),
        _buildStatCard(
          context,
          theme,
          icon: Icons.auto_awesome,
          value: 'Active',
          label: 'Current Prayer',
          isActive: true,
        ),
      ],
    );
  }
  
  // Individual stat card
  Widget _buildStatCard(
    BuildContext context,
    ThemeData theme, {
    required IconData icon,
    required String value,
    required String label,
    bool isActive = false,
  }) {
    final Color cardColor = isActive
        ? theme.colorScheme.primaryContainer
        : theme.colorScheme.surface;
    
    final Color textColor = isActive
        ? theme.colorScheme.onPrimaryContainer
        : theme.colorScheme.onSurface;
    
    return Expanded(
      child: Card(
        color: cardColor,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: textColor),
              const SizedBox(height: 8),
              Text(
                value,
                style: theme.textTheme.titleLarge?.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                label,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: textColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Create prayer card
  Widget _buildCreatePrayerCard(BuildContext context, ThemeData theme) {
    return Card(
      color: theme.colorScheme.primaryContainer,
      child: InkWell(
        onTap: () => context.push(AppConstants.createPrayerRoute),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Create a New Prayer',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Draw wisdom from multiple spiritual traditions',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                    const SizedBox(height: 16),
                    // Feature chips
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        _buildFeatureChip(context, 'Multi-Faith', Icons.public),
                        _buildFeatureChip(context, 'Audio', Icons.headphones),
                        _buildFeatureChip(context, 'Visual', Icons.image),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                  ),
                ),
                child: const Icon(
                  Icons.add,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  // Feature chip widget
  Widget _buildFeatureChip(BuildContext context, String label, IconData icon) {
    return Chip(
      backgroundColor: Colors.white.withOpacity(0.2),
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
    );
  }
  
  // Empty recent prayers
  Widget _buildEmptyRecentPrayers(BuildContext context, ThemeData theme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 32.0),
        child: Column(
          children: [
            Icon(
              Icons.history,
              size: 48,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No prayers yet',
              style: theme.textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first prayer to see it here',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => context.push(AppConstants.createPrayerRoute),
              icon: const Icon(Icons.add),
              label: const Text('Create Prayer'),
            ),
          ],
        ),
      ),
    );
  }
  
  // Recent prayers grid
  Widget _buildRecentPrayersGrid(
    BuildContext context,
    ThemeData theme,
    List<Map<String, dynamic>> recentPrayers,
  ) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.85,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: recentPrayers.length,
      itemBuilder: (context, index) {
        final prayer = recentPrayers[index];
        return _buildPrayerCard(context, theme, prayer);
      },
    );
  }
  
  // Individual prayer card
  Widget _buildPrayerCard(
    BuildContext context,
    ThemeData theme,
    Map<String, dynamic> prayer,
  ) {
    // Parse prayer data
    final id = prayer['id'] as String;
    final title = prayer['title'] as String;
    final faiths = prayer['faiths'] as List<String>;
    final hasAudio = prayer['hasAudio'] as bool;
    final hasImages = prayer['hasImages'] as bool;
    
    // Generate gradient based on faiths
    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: faiths.length >= 2
          ? [
              AppConstants.faithColors[faiths[0]] ?? theme.colorScheme.primary,
              AppConstants.faithColors[faiths[1]] ?? theme.colorScheme.secondary,
            ]
          : [
              AppConstants.faithColors[faiths.first] ?? theme.colorScheme.primary,
              AppConstants.faithColors[faiths.first]?.withOpacity(0.7) ?? 
                  theme.colorScheme.primary.withOpacity(0.7),
            ],
    );
    
    return Card(
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () => context.push('${AppConstants.prayerResultsRoute}/$id'),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card header with gradient
            Container(
              height: 80,
              decoration: BoxDecoration(
                gradient: gradient,
              ),
              child: Stack(
                children: [
                  // Background pattern
                  Positioned.fill(
                    child: Opacity(
                      opacity: 0.1,
                      child: Image.asset(
                        'assets/images/pattern_light.png',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  
                  // Media indicators
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Row(
                      children: [
                        if (hasAudio)
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.headphones,
                              color: Colors.white,
                              size: a16,
                            ),
                          ),
                        if (hasAudio && hasImages)
                          const SizedBox(width: 4),
                        if (hasImages)
                          Container(
                            padding: const EdgeInsets.all(4),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: const Icon(
                              Icons.image,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // Card content
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Prayer title
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  
                  // Faith tradition chips
                  Wrap(
                    spacing: 4,
                    runSpacing: 4,
                    children: faiths
                        .take(2)
                        .map((faith) => Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: theme.colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                faith,
                                style: theme.textTheme.labelSmall,
                              ),
                            ))
                        .toList(),
                  ),
                  if (faiths.length > 2)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surfaceVariant,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '+${faiths.length - 2} more',
                        style: theme.textTheme.labelSmall,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Faith traditions grid
  Widget _buildFaithTraditionsGrid(BuildContext context, ThemeData theme) {
    // Get 4 random faith traditions for preview
    final displayFaiths = AppConstants.faithTraditions.take(4).toList();
    
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.0,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: displayFaiths.length,
      itemBuilder: (context, index) {
        final faith = displayFaiths[index];
        final color = AppConstants.faithColors[faith] ?? theme.colorScheme.primary;
        
        return Container(
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              faith,
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }
}
