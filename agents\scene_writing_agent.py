from pydantic_ai import Agent

class SceneWritingAgent:
    def __init__(self, model_type="pro"):
        # Determine the model based on the model_type argument
        if model_type == "pro":
            model_name = "google-gla:gemini-2.5-pro"
        elif model_type == "flash":
            model_name = "google-gla:gemini-2.5-flash"
        else:
            # Default to flash or raise an error if an unsupported type is given
            model_name = "google-gla:gemini-2.5-flash" 
            print(f"Warning: Unsupported model_type '{model_type}'. Defaulting to gemini-2.5-flash.")

        self.agent = Agent(
            model_name,
            system_prompt='''You are an expert in creating sacred scenes from prayer themes.
            Extract powerful visual imagery from spiritual texts and create 
            detailed descriptions that can be rendered into images.'''
        )
    
    async def create_scenes(self, prayer_text: str, count: int = 3) -> str:
        """
        Creates a specified number of visual scenes based on the provided prayer text.

        Args:
            prayer_text: The text of the prayer to derive scenes from.
            count: The number of scenes to generate.

        Returns:
            A string containing the descriptions of the generated scenes.
            The actual format of the output will depend on how the LLM responds
            to the prompt. It might be a list, a JSON string, or just text.
            Further parsing might be needed by the caller.
        """
        prompt = f"Create {count} powerful visual scenes from this prayer: \"{prayer_text}\". Each scene should be a detailed description suitable for an image generation model. Number each scene clearly."
        
        # PydanticAI's agent.run() is now async by default in newer versions.
        # The plan used agent.run(), assuming it could be async or sync.
        # If using an older PydanticAI that has run_sync, that would be an option for non-async contexts.
        # Given the orchestrator is async, using await self.agent.run() is appropriate.
        result = await self.agent.run(prompt)
        
        # result.output will contain the LLM's response.
        # For example, it might be:
        # "1. Scene description one.\n2. Scene description two.\n3. Scene description three."
        # The orchestrator will then need to parse this if it needs individual scenes.
        return result.output

# Example usage (for testing purposes)
async def main():
    # Pro scene writer
    scene_writer_pro = SceneWritingAgent(model_type="pro")
    prayer = "Oh Divine Light, guide us through darkness, grant us strength in adversity, and fill our hearts with compassion for all beings. May peace prevail on Earth."
    
    print("--- Pro Scene Writer ---")
    pro_scenes_output = await scene_writer_pro.create_scenes(prayer, count=2)
    print(f"Generated Scenes (Pro):\n{pro_scenes_output}")

    # Flash scene writer
    scene_writer_flash = SceneWritingAgent(model_type="flash")
    print("\n--- Flash Scene Writer ---")
    flash_scenes_output = await scene_writer_flash.create_scenes(prayer, count=1)
    print(f"Generated Scenes (Flash):\n{flash_scenes_output}")

if __name__ == "__main__":
    import asyncio
    # asyncio.run(main()) # Commented out as this is library code.
    pass
