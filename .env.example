# Environment Variables Template for Vibe Prayer OmniFaith
# Copy this file to .env and fill in your actual API keys

# Required API Keys
GROQ_API_KEY=your_groq_api_key_here
PERPLEXITY_API_KEY=your_perplexity_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
TOGETHER_API_KEY=your_together_api_key_here
HUGGING_FACE_HUB_TOKEN=your_hugging_face_token_here
OPENAI_API_KEY=your_openai_api_key_here
BRAVE_API_KEY=your_brave_search_api_key_here
REPLICATE_API_KEY=your_replicate_api_key_here
XAI_API_KEY=your_xai_api_key_here
SAMBANOVA_API_KEY=your_sambanova_api_key_here
FAL_API_KEY=your_fal_api_key_here
LLAMA_API_KEY=your_llama_api_key_here

# Optional Configuration
NO_TORCH_COMPILE=1

# Optional API Keys (not required for basic functionality)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
AZURE_API_KEY=your_azure_api_key_here
AZURE_REGION=eastus

# Application Configuration
PYTHONPATH=/app
ENVIRONMENT=production
LOG_LEVEL=INFO
