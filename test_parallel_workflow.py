#!/usr/bin/env python3
"""
Test script for the parallel video generation workflow.
This script tests the new async workflow to ensure video generation runs in parallel.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.prayer_state import PrayerState

def test_workflow_creation():
    """Test that the async workflow can be created successfully."""
    print("🧪 Testing workflow creation...")
    
    try:
        from graph_definition_async import create_prayer_workflow
        workflow = create_prayer_workflow(enable_async=True)
        print("✅ Async workflow created successfully!")
        return True
    except Exception as e:
        print(f"❌ Failed to create async workflow: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routing_functions():
    """Test the routing functions with sample state."""
    print("\n🧪 Testing routing functions...")
    
    try:
        from graph_definition_async import route_to_parallel_media_with_send, start_parallel_media_node
        
        # Test state with video and image requested
        test_state = {
            'generate_video': True,
            'image_prayer_requested': True,
            'audio_conversion_requested': False,
            'prayer_focus_theme': 'Test theme',
            'unified_prayer_filepath': '/test/path.md'
        }
        
        # Test start_parallel_media_node
        result_state = start_parallel_media_node(test_state)
        print("✅ start_parallel_media_node executed successfully")
        
        # Test route_to_parallel_media_with_send
        sends = route_to_parallel_media_with_send(test_state)
        print(f"✅ route_to_parallel_media_with_send returned {len(sends)} Send objects")
        
        return True
    except Exception as e:
        print(f"❌ Failed routing function tests: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_import_resolution():
    """Test that all required imports can be resolved."""
    print("\n🧪 Testing import resolution...")
    
    try:
        # Test async video nodes
        from nodes.async_video_nodes import (
            async_video_generation_node,
            async_image_generation_task,
            async_audio_generation_task,
            media_aggregation_node
        )
        print("✅ Async video nodes imported successfully")
        
        # Test scene generation
        from nodes.scene_generation_nodes import generate_sacred_scenes_node
        print("✅ Scene generation node imported successfully")
        
        # Test image prayer nodes
        from nodes.updated_image_prayer_node import (
            generate_image_prayer_node,
            should_generate_image_prayer
        )
        print("✅ Updated image prayer nodes imported successfully")
        
        return True
    except Exception as e:
        print(f"❌ Import resolution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_functions():
    """Test that async functions can be called."""
    print("\n🧪 Testing async function calls...")
    
    try:
        # Create a minimal test state
        test_state = PrayerState(
            prayer_focus_theme="Test parallel processing",
            search_context="",
            religious_terms={},
            faith_research={},
            spiritual_movements_to_process=["Christianity"],
            individual_prayers={},
            current_movement=None,
            run_output_dir="test_output",
            error_message=None,
            combined_prayer_text=None,
            unified_prayer_markdown="# Test Prayer\n\nThis is a test prayer for parallel processing.",
            unified_prayer_filename=None,
            unified_prayer_filepath="test_prayer.md",
            audio_conversion_requested=False,
            tts_confirmed=False,
            tts_error_message=None,
            tts_provider=None,
            tts_voice_id=None,
            tts_options=None,
            verbal_prayer_text=None,
            verbal_prayer_filepath=None,
            audio_filepath=None,
            image_prayer_requested=False,
            image_prayer_generated=False,
            image_prayer_filepaths=None,
            generate_video=True,
            sacred_scenes=[
                {"description": "A peaceful mountain scene with divine light"},
                {"description": "A flowing river representing spiritual journey"}
            ],
            generated_videos=None,
            generated_video=None,
            generated_images=None,
            generated_audio=None
        )
        
        # Test scene generation (sync function)
        from nodes.scene_generation_nodes import generate_sacred_scenes_node
        result_state = generate_sacred_scenes_node(test_state)
        print("✅ Scene generation node executed successfully")
        
        print("✅ Async function tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Async function tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Parallel Video Generation Workflow Tests")
    print("=" * 60)
    
    tests = [
        ("Import Resolution", test_import_resolution),
        ("Workflow Creation", test_workflow_creation),
        ("Routing Functions", test_routing_functions),
        ("Async Functions", lambda: asyncio.run(test_async_functions()))
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The parallel workflow should work correctly.")
        print("\n💡 Key improvements made:")
        print("   • Video generation now runs in TRUE parallel with other media")
        print("   • Uses LangGraph Send API for concurrent execution")
        print("   • Async nodes properly handle state management")
        print("   • Fallback mechanisms in place for error handling")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
