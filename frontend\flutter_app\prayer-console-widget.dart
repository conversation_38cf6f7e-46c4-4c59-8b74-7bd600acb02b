import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../constants/app_constants.dart';
import '../models/prayer_state.dart';

class PrayerConsoleWidget extends ConsumerWidget {
  final PrayerState prayerState;
  
  const PrayerConsoleWidget({
    Key? key,
    required this.prayerState,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentStage = prayerState.currentStage;
    final progress = prayerState.progress;
    final status = prayerState.currentStatus ?? '';
    final statusDetails = prayerState.statusDetails ?? '';
    final tokenCount = prayerState.totalTokensUsed ?? 0;
    
    // Get the faith traditions being processed
    final faiths = prayerState.spiritualMovementsToProcess ?? [];
    
    // Get colors based on current stage
    final stageIndex = AppConstants.prayerStages.indexOf(status.capitalize());
    final stageColor = _getStageColor(theme, stageIndex);
    
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Prayer focus card
            Card(
              color: theme.colorScheme.primaryContainer.withOpacity(0.7),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Creating Prayer:',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      prayerState.prayerFocusTheme ?? 'Prayer',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Overall progress
            Text(
              'Overall Progress',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            
            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Linear progress indicator
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: progress / 100,
                    minHeight: 12,
                    backgroundColor: theme.colorScheme.surfaceVariant,
                    valueColor: AlwaysStoppedAnimation<Color>(stageColor),
                  ),
                ),
                const SizedBox(height: 8),
                
                // Progress percentage and stage
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      currentStage,
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: stageColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '$progress%',
                      style: theme.textTheme.titleMedium,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Progress timeline
            Text(
              'Progress Timeline',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            
            // Timeline indicators
            SizedBox(
              height: 80,
              child: _buildTimelineIndicators(context, theme, stageIndex),
            ),
            const SizedBox(height: 24),
            
            // Current stage details card
            Card(
              color: theme.colorScheme.surface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: stageColor,
                  width: 2,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Stage header
                    Row(
                      children: [
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            color: stageColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            _getStageIcon(status),
                            color: stageColor,
                            size: 28,
                          ),
                        ),
                        const SizedBox(width: a16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                currentStage,
                                style: theme.textTheme.titleLarge?.copyWith(
                                  color: stageColor,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (statusDetails.isNotEmpty)
                                Text(
                                  statusDetails,
                                  style: theme.textTheme.bodyMedium,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    // Status details
                    if (status == 'researching') ...[
                      _buildResearchingContent(context, theme),
                    ] else if (status == 'generating') ...[
                      _buildGeneratingContent(context, theme),
                    ] else if (status == 'aggregating') ...[
                      _buildAggregatingContent(context, theme),
                    ] else if (status == 'audio') ...[
                      _buildAudioContent(context, theme),
                    ] else if (status == 'images') ...[
                      _buildImagesContent(context, theme),
                    ] else ...[
                      Text('Processing prayer...'),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // Faith traditions progress
            if (faiths.isNotEmpty && status == 'generating') ...[
              Text(
                'Faith Traditions Progress',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 12),
              
              // Progress indicator for faith traditions
              _buildFaithProgressBar(context, theme),
              const SizedBox(height: 8),
              
              // Completed faiths count
              Text(
                '${(prayerState.individualPrayers?.length ?? 0)} of ${faiths.length} traditions completed',
                style: theme.textTheme.bodyMedium,
              ),
              const SizedBox(height: 16),
              
              // Faith cards for completed prayers
              ..._buildCompletedFaithCards(context, theme),
            ],
            
            // Token usage information
            const SizedBox(height: 24),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI Usage Statistics',
                      style: theme.textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total Tokens Used:',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          '$tokenCount tokens',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (prayerState.tokensByStage != null) ...[
                      const Divider(),
                      ...prayerState.tokensByStage!.entries.map((entry) {
                        return Padding(
                          padding: const EdgeInsets.only(top: 4.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${entry.key.capitalize()}:',
                                style: theme.textTheme.bodySmall,
                              ),
                              Text(
                                '${entry.value} tokens',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
  
  // Create a horizontal timeline with stage indicators
  Widget _buildTimelineIndicators(
    BuildContext context, 
    ThemeData theme, 
    int currentStageIndex,
  ) {
    return ListView.separated(
      scrollDirection: Axis.horizontal,
      itemCount: AppConstants.prayerStages.length,
      separatorBuilder: (context, index) {
        // Line connector for timeline
        final isCompleted = index < currentStageIndex;
        return Container(
          width: 20,
          margin: const EdgeInsets.symmetric(vertical: 35),
          height: 2,
          color: isCompleted 
              ? Colors.green 
              : theme.colorScheme.surfaceVariant,
        );
      },
      itemBuilder: (context, index) {
        final stageName = AppConstants.prayerStages[index];
        final isCompleted = index < currentStageIndex;
        final isCurrent = index == currentStageIndex;
        
        Color indicatorColor;
        Widget indicatorIcon;
        
        if (isCompleted) {
          indicatorColor = Colors.green;
          indicatorIcon = const Icon(
            Icons.check,
            color: Colors.white,
            size: 16,
          );
        } else if (isCurrent) {
          indicatorColor = _getStageColor(theme, index);
          indicatorIcon = const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          );
        } else {
          indicatorColor = theme.colorScheme.surfaceVariant;
          indicatorIcon = const Icon(
            Icons.circle_outlined,
            color: Colors.white,
            size: 16,
          );
        }
        
        return SizedBox(
          width: 80,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Stage indicator
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: indicatorColor,
                  shape: BoxShape.circle,
                ),
                child: Center(child: indicatorIcon),
              ),
              const SizedBox(height: 8),
              
              // Stage name
              Text(
                stageName,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isCurrent 
                      ? indicatorColor 
                      : (isCompleted 
                          ? Colors.green 
                          : theme.colorScheme.onSurface.withOpacity(0.6)),
                  fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }
  
  // Build progress bar for faith traditions
  Widget _buildFaithProgressBar(BuildContext context, ThemeData theme) {
    final totalFaiths = prayerState.spiritualMovementsToProcess?.length ?? 1;
    final processedFaiths = prayerState.individualPrayers?.length ?? 0;
    final progressValue = processedFaiths / totalFaiths;
    
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: LinearProgressIndicator(
        value: progressValue,
        minHeight: 8,
        backgroundColor: theme.colorScheme.surfaceVariant,
        valueColor: AlwaysStoppedAnimation<Color>(Colors.teal),
      ),
    );
  }
  
  // Build cards for completed faith prayers
  List<Widget> _buildCompletedFaithCards(BuildContext context, ThemeData theme) {
    final individualPrayers = prayerState.individualPrayers;
    if (individualPrayers == null || individualPrayers.isEmpty) {
      return [];
    }
    
    return individualPrayers.entries.map((entry) {
      final faith = entry.key;
      final faithColor = AppConstants.faithColors[faith] ?? theme.colorScheme.primary;
      
      return Card(
        margin: const EdgeInsets.only(bottom: 12),
        color: theme.colorScheme.surface,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Faith name and checkmark
              Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: faithColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    faith,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: faithColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Preview of generated prayer
              Text(
                _truncateText(entry.value, 100),
                style: theme.textTheme.bodyMedium,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      );
    }).toList();
  }
  
  // Build content for researching stage
  Widget _buildResearchingContent(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Researching spiritual terms and divine names for the selected traditions.',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 12),
        const LinearProgressIndicator(),
      ],
    );
  }
  
  // Build content for generating stage
  Widget _buildGeneratingContent(BuildContext context, ThemeData theme) {
    final faiths = prayerState.spiritualMovementsToProcess ?? [];
    final current = prayerState.currentMovement;
    
    if (current == null) {
      return const Text('Preparing to generate prayers...');
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Creating individual prayers from each spiritual tradition.',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 12),
        RichText(
          text: TextSpan(
            style: theme.textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
            ),
            children: [
              TextSpan(
                text: 'Currently generating prayer for ',
                style: TextStyle(color: theme.colorScheme.onSurface),
              ),
              TextSpan(
                text: current,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppConstants.faithColors[current] ?? theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
  
  // Build content for aggregating stage
  Widget _buildAggregatingContent(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Creating a unified prayer by combining insights from all selected faith traditions.',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 12),
        const LinearProgressIndicator(),
      ],
    );
  }
  
  // Build content for audio stage
  Widget _buildAudioContent(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Generating audio narration with your selected voice.',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const Icon(Icons.music_note),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: LinearProgressIndicator(
                  value: null, // Indeterminate
                ),
              ),
            ),
            const Icon(Icons.headphones),
          ],
        ),
      ],
    );
  }
  
  // Build content for images stage
  Widget _buildImagesContent(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Creating visual representations of your prayer.',
          style: theme.textTheme.bodyMedium,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            const Icon(Icons.palette),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: LinearProgressIndicator(
                  value: null, // Indeterminate
                ),
              ),
            ),
            const Icon(Icons.image),
          ],
        ),
      ],
    );
  }
  
  // Get color based on current stage
  Color _getStageColor(ThemeData theme, int stageIndex) {
    switch (stageIndex) {
      case 0: // Researching
        return Colors.blue;
      case 1: // Generating
        return Colors.purple;
      case 2: // Aggregating
        return Colors.deepOrange;
      case 3: // Audio
        return Colors.teal;
      case 4: // Images
        return Colors.amber;
      default:
        return theme.colorScheme.primary;
    }
  }
  
  // Get icon based on current stage
  IconData _getStageIcon(String status) {
    switch (status.toLowerCase()) {
      case 'researching':
        return Icons.search;
      case 'generating':
        return Icons.edit;
      case 'aggregating':
        return Icons.merge_type;
      case 'audio':
        return Icons.headphones;
      case 'images':
        return Icons.image;
      default:
        return Icons.hourglass_empty;
    }
  }
  
  // Truncate text to specified length
  String _truncateText(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return '${text.substring(0, maxLength)}...';
  }
}

// Extension to capitalize strings
extension StringExtension on String {
  String capitalize() {
    return isEmpty ? this : '${this[0].toUpperCase()}${substring(1)}';
  }
}
