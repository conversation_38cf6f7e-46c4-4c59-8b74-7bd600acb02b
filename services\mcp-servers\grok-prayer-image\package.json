{"name": "grok-prayer-image-mcp", "version": "1.0.0", "description": "MCP server for generating visual prayers using XAI and Grok 2 Vision", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["prayer", "grok", "xai", "visualization", "mcp", "spiritual"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.7", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "modelcontextprotocol": "file:../../../modelcontextprotocol"}}